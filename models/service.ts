export class ServiceApi {
  async getAll() {
    var v = await fetch('https://api.echomedi.com/api/services?pagination[page]=1&pagination[pageSize]=10000')
        .then((response) => response.json());
    var blogs = v.data;
    const result = blogs.map( (d:any) => {
      let v = new Service();
      v.slug = d.attributes.slug;
      v.label = d.attributes.label;
      return v;
    })
    return result;
  }

  async find(type: string) {
    var v = await fetch('https://api.echomedi.com/api/services?pagination[page]=1&pagination[pageSize]=10000&filters[type][$eq]=' + type)
        .then((response) => response.json());
    var blogs = v.data;
    const result = blogs?.map( (d:any) => {
      let v = new Service();
      v.slug = d.attributes.slug;
      v.label = d.attributes.label;
      return v;
    }) ?? [];
    return result;
  }

  async findOne(slug: string) {
    let v = await fetch('https://api.echomedi.com/api/service/findOne/' + slug)
    .then((response) => response.json());
    let blogs = v.service;
    let sub_package = blogs.sub_package;
    let pkg = sub_package?.package ?? null;
    let result = new Service();
    if (blogs) {
      result.id = blogs.id;
      result.label = blogs.label;
      result.en_label = blogs.en_label;
      result.en_desc = blogs.en_desc;
      result.en_detail = blogs.en_detail;
      result.slug = blogs.slug;
      result.desc = blogs.desc;
      result.detail = blogs.detail;
      result.image_url = blogs.image ? blogs.image.url : '';
      result.placeholder_image_url = blogs.image ? blogs.image.formats.thumbnail.url : '';
      result.price = blogs.price ?? 0;
      result.original_price = blogs.original_price ?? 0;
      result.show_buy_btn = blogs.show_buy_btn;
      result.show_inquiry_form = blogs.show_inquiry_form;
      result.show_booking_btn = blogs.show_booking_btn;
      result.benefit = blogs.benefit;
      result.en_benefit = blogs.en_benefit;
      result.genetica_image_url = blogs.genetica_image ? blogs.genetica_image.url : '';
      result.properties = blogs.properties ?? [];
      result.en_properties = blogs.en_properties ?? [];
      result.genetica_pdf = blogs.genetica_pdf;
      result.specification = blogs.specification ?? [];
      result.en_specification = blogs.en_specification ?? [];
      result.package = pkg;
      result.sub_package = sub_package;
      result.other_services = sub_package?.services;
    } else {
      result.id = "";
      result.label = "";
      result.slug = "";
      result.desc = "";
      result.detail = "";
      result.en_label = "";
      result.en_detail = "";
      result.en_desc = "";
      result.image_url = "";
      result.placeholder_image_url = "";
      result.price = "";
      result.show_booking_btn = "";
      result.show_inquiry_form = "";
      result.benefit = "";
      result.en_benefit = "";
      result.genetica_image_url = "";
      result.properties = [];
      result.en_properties = [];
      result.genetica_pdf = "";
      result.specification = [];
      result.en_specification = [];
    }
    return result;
  }
}

class Service {
  id!: string;
  label!: string;
  en_label!: string;
  slug!: string;
  desc!: string;
  en_desc!: string;
  price!: any;
  original_price!: any;
  detail!: string;
  en_detail!: string;
  image_url!: string;
  placeholder_image_url!: string;
  show_buy_btn: any;
  show_booking_btn: any;
  show_inquiry_form: any;
  benefit!: string;
  en_benefit!: string;
  genetica_image_url!: string;
  properties!: Array<string>;
  en_properties!: Array<string>;
  genetica_pdf!: string;
  specification!: Array<string>;
  en_specification!: Array<string>;
  package: any;
  sub_package: any;
  other_services: any;
}