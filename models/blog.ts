export class BlogApi {
  async getAll() {
    return [];
  }

  async findOne(slug: string) {
    var v = await fetch(
      'https://api.echomedi.com/api/blog/findOne/' + slug)
      .then((response) => response.json());
    var blogs = v.blog;
    var result = new Blog();
    if (blogs) {
      result.label = blogs.label;
      result.slug = blogs.slug;
      result.article = blogs.article;
      result.image_url = blogs.image ? blogs.image.url : '';
    } else {
      result.label = "";
      result.slug = "";
      result.article = "";
      result.image_url = "";
    }
    return result;
  }
}

class Blog {
  label!: string;
  slug!: string;
  article!: string;
  image_url!: string;
}