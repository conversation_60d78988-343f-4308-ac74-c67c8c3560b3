import { useEffect, useState } from "react";
import axios from "axios";
import { toast } from "react-hot-toast";

interface Address {
  province: {
    id: string;
    name: string;
  };
  district: {
    id: string;
    name: string;
  };
  ward: {
    id: string;
    name: string;
  };
  address: string;
}

interface Patient {
  phone?: string;
  email?: string;
  full_name?: string;
  birthday?: string;
  gender?: string;
  address?: Address;
}

interface UserData {
  patient: Patient;
  phone?: string;
  email?: string;
}

const useUserData = (token: string | null) => {
  const [userData, setUserData] = useState<UserData>({
    phone: "",
    email:"",
    patient: {
      phone: "",
      email: "",
      full_name: "",
      birthday: "",
      address: {
        province: {
          id: "",
          name: ""
        },
        district: {
          id: "",
          name: ""
        },
        ward: {
          id: "",
          name: ""
        },
        address: ""
      },
    }
  });

  useEffect(() => {
    if (!token) return;

    const fetchUserData = async () => {
      try {
        const response = await axios.get("https://api.echomedi.com/api/user/getMe", {
          headers: { Authorization: `Bearer ${token}` },
        });
        const { phone, email, full_name, birthday, gender, address } = response.data.patient;
        setUserData({
          phone,
          email,
          patient: {
            phone,
            email,
            full_name,
            birthday,
            gender,
            address
          }
        });
      
      } catch (error: any) {
      }
    };

    fetchUserData();
  }, [token]);

  return { userData, setUserData };
};

export default useUserData;