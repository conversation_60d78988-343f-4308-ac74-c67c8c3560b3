@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Roboto-Bold";
  src: url('../fonts/Roboto-Bold.ttf');
  font-weight: black;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Roboto-Regular";
  src: url('../fonts/Roboto-Regular.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Roboto-Regular";
  src: url('../fonts/Roboto-Regular.ttf');
  font-weight: bold;
  font-style: bold;
  font-display: swap;
}

@font-face {
  font-family: "Roboto-Regular";
  src: url('../fonts/Roboto-Regular.ttf');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

.slide .swiper-pagination-bullet {
  @apply h-2 w-2 bg-[#EEEEEE] opacity-100;
}

.slide .swiper-pagination-bullet-active {
  @apply w-3 rounded-full bg-[#156634];
}

.slide .swiper-pagination {
  @apply relative pt-[20px];
}


.slideblog .swiper-pagination {
  @apply relative pt-[36px];
}
.slideblog .swiper-pagination-bullet {
  @apply h-4 w-4 bg-[#EEEEEE] opacity-100;
}

.slideblog .swiper-pagination-bullet-active {
  @apply w-4 rounded-full bg-[#156634];
}

.slideblog .swiper-button-next::after,
.slideblog .swiper-button-prev::after {
  font-size: 20px !important;
  color: #B5B5B5;   
}

.slideblog .swiper-button-next,
.slideblog .swiper-button-prev {
  border-radius: 50%;
  background-color: #EFEFEF;
  height: 50px !important;
  width: 50px !important;
  /* margin-top: -30px !important; */
}

#supportCheckbox:checked~div span {
  @apply opacity-100;
}

.slide-corporate {
  width: 100%;
  height: 100%;
}

.slide-corporate .swiper-pagination-bullet {
  @apply  bg-[#EEEEEE] opacity-100;
}

.slide-corporate .swiper-pagination-bullet-active {
  @apply  rounded-full bg-[#156634];
}

html {
  scroll-behavior: smooth;
  background: #F8F9FB;
  /* font-family: 'Montserrat', sans-serif; */
  font-family: 'Roboto', sans-serif;
}

[id^="care_package_"],
[id^="initial_treatment_"],
[id^="comprehensive_health_"],
[id^="chronicdiseases_"] {
  scroll-margin-top: 160px;
}


.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}


.menu-item {
  @apply p-2 inline-block;
}

/* .menu-item:focus-within span {
  @apply text-[#156634] font-bold underline;
} */

.menu-sub-item {
  @apply block px-4 py-2 hover:bg-white hover:text-[#156634] hover:underline text-sm;
}

.mega-sub-item {
  @apply block p-2 hover:bg-white text-black cursor-pointer hover:text-[#156634] hover:underline text-sm;
}

.mega-sub-item-title {
  @apply block text-sm mb-2;
}

@layer utilities {
  .sub-dropdown:hover>.sub-dropdown-content {
    opacity: 1;
    visibility: visible;
    top: 0;
  }

  .sub-dropdown:hover>a {
    background-color: #fff;
    color: #000;
  }
}


.nav-button {
  display: none;
  background-color: transparent;
}

.nav-button.active, 
.nav-button.prev, 
.nav-button.next, 
.nav-button:first-child, 
.nav-button:nth-child(2), 
.nav-button:nth-last-child(2), 
.nav-button.ellipsis { 
  display: inline-flex;
}

.nav-button.visible {
  display: inline-flex;
}
.nav-button.active {
  background-color: #166534;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

input[type="radio"] {
  transform: scale(1.5);
  vertical-align: middle;
  margin-right: 12px; 
}


.custom-img {
  background-image: url("../assets/hero.jpg");
}

.darkened-image {
  filter: brightness(80%);

  height: 94px;
  width: 120px;
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 12px;
  background: #fff;

  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}


.swiper-button-next::after,
.swiper-button-prev::after {
  font-size: 12px !important;
  color: white;   
}

.swiper-button-next,
.swiper-button-prev {
  border-radius: 50%;
  background-color: #156634;
  height: 24px !important;
  width: 24px !important;
  /* margin-top: -30px !important; */
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
  overflow: hidden;
}

table th {
  border-left: 1px solid #494949;
  border-right: 1px solid #494949;
  padding: 10px;
  border-top: 1px solid #494949;
  border-bottom: 1px solid #494949;
}

table td {
  border-left: 1px solid #494949;
  border-right: 1px solid #494949;
  padding: 10px;
  border-top: 1px solid #494949;
  border-bottom: 1px solid #494949;
}

/* table th {
    background-color: #bbf7d0;
  }
  table tr:nth-of-type(even) td {
    background-color: #e3ffdf;
  }
  table .total th {
    background-color: white;
  } */
table .total td {
  text-align: right;
  font-weight: 700;
}

.mobile-header {
  display: none;
}

/*   
  @media only screen and (max-width: 760px) {
    p {
      display: block;
      font-weight: bold;
    }
  
    table tr td:not(:first-child),
  table tr th:not(:first-child),
  table tr td:not(.total-val) {
      display: none;
    }
    table tr:nth-of-type(even) td:first-child {
      background-color: #d9f4f2;
    }
    table tr:nth-of-type(odd) td:first-child {
      background-color: white;
    }
    table tr:nth-of-type(even) td:not(:first-child) {
      background-color: white;
    }
    table tr th:first-child {
      width: 100%;
      display: block;
    }
    table tr th:not(:first-child) {
      width: 40%;
      transition: transform 0.4s ease-out;
      transform: translateY(-9999px);
      position: relative;
      z-index: -1;
    }
    table tr td:not(:first-child) {
      transition: transform 0.4s ease-out;
      transform: translateY(-9999px);
      width: 60%;
      position: relative;
      z-index: -1;
    }
    table tr td:first-child {
      display: block;
      cursor: pointer;
    }
    table tr.total th {
      width: 25%;
      display: inline-block;
    }
    table tr td.total-val {
      display: inline-block;
      transform: translateY(0);
      width: 75%;
    }
  }
  @media only screen and (max-width: 300px) {
    table tr th:not(:first-child) {
      width: 50%;
      font-size: 14px;
    }
    table tr td:not(:first-child) {
      width: 50%;
      font-size: 14px;
    }
  } */

.accordion-content {
  transition: all .5s ease;
  opacity: 0;
  transition: all .5s;
  display: none;
}


.accordion-content.active {
  height: auto;
  opacity: 1;
  padding: 20px;
  display: block;
  transition: all .5s ease;
}

.accordion {
  width: 100%;
  padding: 1rem 2rem;
  display: flex;
  flex-direction: column;
  border-radius: 10%;
  overflow-y: auto;
}

.fold {
  /* border-bottom: 1px solid rgba(34, 36, 38, 0.35); */
  margin-top: -1px;
}

.fold .fold_trigger {
  text-align: start;
  width: 100%;
  cursor: pointer;
  /* border-bottom: 1px solid rgba(34, 36, 38, 0.35); */
}

.fold .fold_trigger.open {
  background-color: #dfffe0;
}

.fold .fold_trigger.open:before {
  transform: rotateZ(-180deg);
}

.fold .fold_content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 1200ms ease;
  /* padding-top: 10px;
  padding-bottom: 10px; */
}

.fold .fold_content.open {
  max-height: 400px;
  /* padding-top: 10px; */
  /* padding-bottom: 10px; */
}

.cart {
  box-shadow: 1px 1px #888888;
}

.markdown-container table {
  margin-left: -18px;
}

h1 {
  font-size: 30px;
}

h2 {
  font-size: 30px;
}

td {
  vertical-align: top;
}

.c-card img {
  transition: transform .3s ease-in-out;
}

.c-card:hover img {
  transform: scale(1.05);
}

.label-product {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.label-product-2 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.label-product-3 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.buttons_added {
  opacity: 1;
  display: inline-block;
  display: -ms-inline-flexbox;
  display: inline-flex;
  white-space: nowrap;
  vertical-align: top;
}

.is-form {
  overflow: hidden;
  cursor: pointer;
  position: relative;
  background-color:#F5F5F5;
  height:2rem;
  width: 1.9rem;
  padding: 0;
  text-shadow: 1px 1px 1px #fff;
  border: 1px solid #ddd;
}

.is-form:focus,
.input-text:focus {
  outline: none;
}

.is-form.minus {
  border-radius: 4px 0 0 4px;
}

.is-form.plus {
  border-radius: 0 4px 4px 0;
}

.input-qty {
  background-color: #fff;
  height: 2rem;
  text-align: center;
  font-size: 1rem;
  display: inline-block;
  vertical-align: top;
  margin: 0;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  border-left: 0;
  border-right: 0;
  padding: 0;
}

.input-qty::-webkit-outer-spin-button,
.input-qty::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.buttons_added_mb {
  display: inline-block;
  display: -ms-inline-flexbox;
  display: inline-flex;
  white-space: nowrap;
  vertical-align: top;
}

.is-form_mb {
  overflow: hidden;
  cursor: pointer;
  position: relative;
  background-color: #f9f9f9;
  height: 1.5rem;
  width: 1.3rem;
  padding: 0;
  text-shadow: 1px 1px 1px #fff;
  border: 1px solid #ddd;
}

.is-form_mb:focus,
.input-text_mb:focus {
  outline: none;
}

.is-form_mb.minus {
  border-radius: 4px 0 0 4px;
}

.is-form_mb.plus {
  border-radius: 0 4px 4px 0;
}

.input-qty_mb {
  background-color: #fff;
  height: 1.5rem;
  width: 1.3rem;
  text-align: center;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
  margin: 0;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  border-left: 0;
  border-right: 0;
  padding: 0;
}

.input-qty_mb::-webkit-outer-spin-button,
.input-qty_mb::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.zalo-chat-widget {
  bottom: 10px !important;
  right: 10px !important;
}

@media (max-width: 600px) {
  .zalo-chat-widget {
    bottom: 0px !important;
    right: 0px !important;
  }

  #gift-btn {
    font-size: 0.8rem !important;
  }

}

.scroll-container {
  overflow-x: hidden;
  white-space: nowrap;
  scroll-snap-type: x mandatory;
}

.scroll-item {
  scroll-snap-align: center;
}

/* Optional: To add some space between each item */
.scroll-item {
  margin-left: 8px;
  margin-right: 8px;
}

.h-screen1 {
  height: 400px
}

input {
  accent-color: #166534;
}

#gift-btn {
  width: fit-content;
  padding: 0 10px;
  border: none;
  color: white;
  font-weight: bold;
  font-size: 1.3rem;
  background: red;
  text-shadow: 0 3px 1px rgba(122, 17, 8, .8);
  box-shadow: 0 8px 0 rgb(183, 9, 0),
    0 15px 20px rgba(0, 0, 0, .35);
  text-transform: uppercase;
  transition: .4s all ease-in;
  outline: none;
  cursor: pointer;
  text-align: center;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

@keyframes horizontal-shaking {
  0% {
    transform: translateX(0)
  }

  25% {
    transform: translateX(5px)
  }

  50% {
    transform: translateX(-5px)
  }

  75% {
    transform: translateX(5px)
  }

  100% {
    transform: translateX(0)
  }
}

@keyframes vertical-shaking {
  0% {
    transform: translateY(0)
  }

  25% {
    transform: translateY(3px)
  }

  50% {
    transform: translateY(-3px)
  }

  75% {
    transform: translateY(3px)
  }

  100% {
    transform: translateY(0)
  }
}

a.horizontal-shake {
  animation: vertical-shaking 1s infinite;
}


ul.breadcrumb {
  display: block;
  height: 50px;
  margin: auto;
  list-style-type: none;
}

ul.breadcrumb>li.crumb.first-crumb {
  z-index: 300;
}

ul.breadcrumb>li.crumb.middle-crumb {
  z-index: 200;
}

ul.breadcrumb>li.crumb.last-crumb {
  z-index: 100;
}

ul.breadcrumb>li.crumb {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  height: 0;
  color: white;
  font-size: 12px;
  margin-left: -12px;
  margin-top: 2px;
  padding: 5.5px 0px 5.5px 20px;
  line-height: 40px;
  border-bottom: 15px solid #416044;
  border-right: 15px solid transparent;
  transition: .2s border-color linear;
}

ul.breadcrumb>li.crumb:hover,
ul.breadcrumb>li.crumb:hover:after {
  cursor: pointer;
  border-top-color: #CDE485;
  border-bottom-color: #CDE485;
  transition: .2s border-color linear;
}

li.crumb:after,
li.crumb:before {
  content: '';
  position: absolute;
  left: 0;
  transition: .2s border-color linear;
  box-sizing: content-box;
}

li.crumb:after {
  width: 100%;
  top: 25px;
  z-index: -1;
  border-top: 15px solid #416044;
  border-right: 15px solid transparent;
}

li.crumb:before {
  top: 100%;
  border-left: 15px solid white;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
}

.coccoc-alo-phone {
  position: fixed;
  visibility: hidden;
  background-color: transparent;
  width: 120px;
  height: 69px;
  cursor: pointer;
  z-index: 200000 !important;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  -webkit-transition: visibility .5s;
  -moz-transition: visibility .5s;
  -o-transition: visibility .5s;
  transition: visibility .5s;
  right: -20px;
  bottom: 100px;
}

.coccoc-alo-phone.coccoc-alo-show {
  visibility: visible
}

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none
  }
}

@-webkit-keyframes fadeInRightBig {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0)
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none
  }
}

@-webkit-keyframes fadeOutRight {
  0% {
    opacity: 1
  }

  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }
}

.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight
}

.coccoc-alo-phone.coccoc-alo-static {
  opacity: .6
}

.coccoc-alo-phone.coccoc-alo-hover,
.coccoc-alo-phone:hover {
  opacity: 1
}

.coccoc-alo-ph-circle {
  width: 100px;
  height: 100px;
  top: 10px;
  left: 10px;
  position: absolute;
  background-color: transparent;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  border: 2px solid rgba(30, 30, 30, 0.4);
  border: 2px solid #bfebfc 9;
  opacity: .1;
  -webkit-animation: coccoc-alo-circle-anim 1.2s infinite ease-in-out;
  -moz-animation: coccoc-alo-circle-anim 1.2s infinite ease-in-out;
  -ms-animation: coccoc-alo-circle-anim 1.2s infinite ease-in-out;
  -o-animation: coccoc-alo-circle-anim 1.2s infinite ease-in-out;
  animation: coccoc-alo-circle-anim 1.2s infinite ease-in-out;
  -webkit-transition: all .5s;
  -moz-transition: all .5s;
  -o-transition: all .5s;
  transition: all .5s;
  -webkit-transform-origin: 50% 50%;
  -moz-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  -o-transform-origin: 50% 50%;
  transform-origin: 50% 50%
}

.coccoc-alo-phone.coccoc-alo-active .coccoc-alo-ph-circle {
  -webkit-animation: coccoc-alo-circle-anim 1.1s infinite ease-in-out !important;
  -moz-animation: coccoc-alo-circle-anim 1.1s infinite ease-in-out !important;
  -ms-animation: coccoc-alo-circle-anim 1.1s infinite ease-in-out !important;
  -o-animation: coccoc-alo-circle-anim 1.1s infinite ease-in-out !important;
  animation: coccoc-alo-circle-anim 1.1s infinite ease-in-out !important
}

.coccoc-alo-phone.coccoc-alo-static .coccoc-alo-ph-circle {
  -webkit-animation: coccoc-alo-circle-anim 2.2s infinite ease-in-out !important;
  -moz-animation: coccoc-alo-circle-anim 2.2s infinite ease-in-out !important;
  -ms-animation: coccoc-alo-circle-anim 2.2s infinite ease-in-out !important;
  -o-animation: coccoc-alo-circle-anim 2.2s infinite ease-in-out !important;
  animation: coccoc-alo-circle-anim 2.2s infinite ease-in-out !important
}

.coccoc-alo-phone.coccoc-alo-hover .coccoc-alo-ph-circle,
.coccoc-alo-phone:hover .coccoc-alo-ph-circle {
  border-color: #00aff2;
  opacity: .5
}

.coccoc-alo-phone.coccoc-alo-green.coccoc-alo-hover .coccoc-alo-ph-circle,
.coccoc-alo-phone.coccoc-alo-green:hover .coccoc-alo-ph-circle {
  border-color: #F20000;
  border-color: #baf5a7 9;
  opacity: .5
}

.coccoc-alo-phone.coccoc-alo-green .coccoc-alo-ph-circle {
  border-color: #75eb50;
  border-color: #bfebfc 9;
  opacity: .5
}

.coccoc-alo-phone.coccoc-alo-gray.coccoc-alo-hover .coccoc-alo-ph-circle,
.coccoc-alo-phone.coccoc-alo-gray:hover .coccoc-alo-ph-circle {
  border-color: #ccc;
  opacity: .5
}

.coccoc-alo-phone.coccoc-alo-gray .coccoc-alo-ph-circle {
  border-color: #F20000;
  opacity: .5
}

.coccoc-alo-ph-circle-fill {
  width: 80px;
  height: 80px;
  top: 20px;
  left: 20px;
  position: absolute;
  background-color: #000;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  border: 2px solid transparent;
  opacity: .1;
  -webkit-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -moz-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -ms-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -o-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out;
  animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out;
  -webkit-transition: all .5s;
  -moz-transition: all .5s;
  -o-transition: all .5s;
  transition: all .5s;
  -webkit-transform-origin: 50% 50%;
  -moz-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  -o-transform-origin: 50% 50%;
  transform-origin: 50% 50%
}

.coccoc-alo-phone.coccoc-alo-active .coccoc-alo-ph-circle-fill {
  -webkit-animation: coccoc-alo-circle-fill-anim 1.7s infinite ease-in-out !important;
  -moz-animation: coccoc-alo-circle-fill-anim 1.7s infinite ease-in-out !important;
  -ms-animation: coccoc-alo-circle-fill-anim 1.7s infinite ease-in-out !important;
  -o-animation: coccoc-alo-circle-fill-anim 1.7s infinite ease-in-out !important;
  animation: coccoc-alo-circle-fill-anim 1.7s infinite ease-in-out !important
}

.coccoc-alo-phone.coccoc-alo-static .coccoc-alo-ph-circle-fill {
  -webkit-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out !important;
  -moz-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out !important;
  -ms-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out !important;
  -o-animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out !important;
  animation: coccoc-alo-circle-fill-anim 2.3s infinite ease-in-out !important;
  opacity: 0 !important
}

.coccoc-alo-phone.coccoc-alo-hover .coccoc-alo-ph-circle-fill,
.coccoc-alo-phone:hover .coccoc-alo-ph-circle-fill {
  background-color: rgba(0, 175, 242, 0.5);
  background-color: #00aff2 9;
  opacity: .75 !important
}

.coccoc-alo-phone.coccoc-alo-green.coccoc-alo-hover .coccoc-alo-ph-circle-fill,
.coccoc-alo-phone.coccoc-alo-green:hover .coccoc-alo-ph-circle-fill {
  background-color: rgba(117, 235, 80, 0.5);
  background-color: #baf5a7 9;
  opacity: .75 !important
}

.coccoc-alo-phone.coccoc-alo-green .coccoc-alo-ph-circle-fill {
  background-color: rgb(235 253 241);
  background-color: #a6e3fa 9;
  opacity: .75 !important
}

.coccoc-alo-phone.coccoc-alo-gray.coccoc-alo-hover .coccoc-alo-ph-circle-fill,
.coccoc-alo-phone.coccoc-alo-gray:hover .coccoc-alo-ph-circle-fill {
  background-color: rgba(204, 204, 204, 0.5);
  background-color: #ccc 9;
  opacity: .75 !important
}

.coccoc-alo-phone.coccoc-alo-gray .coccoc-alo-ph-circle-fill {
  background-color: rgba(117, 235, 80, 0.5);
  opacity: .75 !important
}

.coccoc-alo-ph-img-circle {
  width: 40px;
  height: 40px;
  top: 40px;
  left: 40px;
  position: absolute;
  background: #426044 url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAABNmlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjarY6xSsNQFEDPi6LiUCsEcXB4kygotupgxqQtRRCs1SHJ1qShSmkSXl7VfoSjWwcXd7/AyVFwUPwC/0Bx6uAQIYODCJ7p3MPlcsGo2HWnYZRhEGvVbjrS9Xw5+8QMUwDQCbPUbrUOAOIkjvjB5ysC4HnTrjsN/sZ8mCoNTIDtbpSFICpA/0KnGsQYMIN+qkHcAaY6addAPAClXu4vQCnI/Q0oKdfzQXwAZs/1fDDmADPIfQUwdXSpAWpJOlJnvVMtq5ZlSbubBJE8HmU6GmRyPw4TlSaqo6MukP8HwGK+2G46cq1qWXvr/DOu58vc3o8QgFh6LFpBOFTn3yqMnd/n4sZ4GQ5vYXpStN0ruNmAheuirVahvAX34y/Axk/96FpPYgAAACBjSFJNAAB6JQAAgIMAAPn/AACA6AAAUggAARVYAAA6lwAAF2/XWh+QAAAB/ElEQVR42uya7W3CMBCG31QM4A1aNggTlG6QbpBMkHYC1AloJ4BOABuEDcgGtBOETnD9c1ERCH/lwxeaV8oPFGP86Hy+DxMREW5Bd7gRjSDSNGn4/RiAOvm8C0ZCRD5PSkQVXSr1nK/xE3mcWimA1ZV3JYBZCIO4giQANoYxMwYS6+xKY4lT5dJPreWZY+uspqSCKPYN27GJVBDXheVSQe494ksiEWTuMXcu1dld9SARxDX1OAJ4lgjy4zDnFsC076A4adEiRwAZg4hOUSpNoCsBPDGM+HqkNGynYBCuILuWj+dgWysGsNe8nwL4GsrW0m2fxZBq9rW0rNcX5MOQ9eZD8JFahcG5g/iKT671alGAYQggpYWvpEPYWrU/HDTOfeRIX0q2SL3QN4tGhZJukVobQyXYWw7WtLDKDIuM+ZSzscyCE9PCy5IttCvnZNaeiGLNHKuz8ZVh/MXTVu/1xQKmIqLEAuJ0fNo3iG5B51oSkeKnsBi/4bG9gYB/lCytU5G9DryFW+3Gm+JLwU7ehbJrwTjq4DJU8bHcVbEV9dXXqqP6uqO5e2/QZRYJpqu2IUAA4B3tXvx8hgKp05QZW6dJqrLTNkB6vrRURLRwPHqtYgkC3cLWQAcDQGGKH13FER/NATzi786+BPDNjm1dMkfjn2pGkBHkf4D8DgBJDuDHx9BN+gAAAABJRU5ErkJggg==") no-repeat center center;
  background-size: 30px 30px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  border: 2px solid transparent;
  opacity: .7;
  -webkit-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out;
  -moz-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out;
  -ms-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out;
  -o-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out;
  animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out;
  -webkit-transform-origin: 50% 50%;
  -moz-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  -o-transform-origin: 50% 50%;
  transform-origin: 50% 50%
}

.coccoc-alo-phone.coccoc-alo-active .coccoc-alo-ph-img-circle {
  -webkit-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out !important;
  -moz-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out !important;
  -ms-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out !important;
  -o-animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out !important;
  animation: coccoc-alo-circle-img-anim 1s infinite ease-in-out !important
}

.coccoc-alo-phone.coccoc-alo-static .coccoc-alo-ph-img-circle {
  -webkit-animation: coccoc-alo-circle-img-anim 0s infinite ease-in-out !important;
  -moz-animation: coccoc-alo-circle-img-anim 0s infinite ease-in-out !important;
  -ms-animation: coccoc-alo-circle-img-anim 0s infinite ease-in-out !important;
  -o-animation: coccoc-alo-circle-img-anim 0s infinite ease-in-out !important;
  animation: coccoc-alo-circle-img-anim 0s infinite ease-in-out !important
}

.coccoc-alo-phone.coccoc-alo-hover .coccoc-alo-ph-img-circle,
.coccoc-alo-phone:hover .coccoc-alo-ph-img-circle {
  background-color: #00aff2
}

.coccoc-alo-phone.coccoc-alo-green.coccoc-alo-hover .coccoc-alo-ph-img-circle,
.coccoc-alo-phone.coccoc-alo-green:hover .coccoc-alo-ph-img-circle {
  background-color: #F20000;
  background-color: #F20000 9
}

.coccoc-alo-phone.coccoc-alo-green .coccoc-alo-ph-img-circle {
  background-color: #00aff2;
  background-color: #426044;
}

.coccoc-alo-phone.coccoc-alo-gray.coccoc-alo-hover .coccoc-alo-ph-img-circle,
.coccoc-alo-phone.coccoc-alo-gray:hover .coccoc-alo-ph-img-circle {
  background-color: #ccc
}

.coccoc-alo-phone.coccoc-alo-gray .coccoc-alo-ph-img-circle {
  background-color: #F20000
}

@-moz-keyframes coccoc-alo-circle-anim {
  0% {
    -moz-transform: rotate(0) scale(.5) skew(1deg);
    opacity: .1;
    -moz-opacity: .1;
    -webkit-opacity: .1;
    -o-opacity: .1
  }

  30% {
    -moz-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .5;
    -moz-opacity: .5;
    -webkit-opacity: .5;
    -o-opacity: .5
  }

  100% {
    -moz-transform: rotate(0) scale(1) skew(1deg);
    opacity: .6;
    -moz-opacity: .6;
    -webkit-opacity: .6;
    -o-opacity: .1
  }
}

@-webkit-keyframes coccoc-alo-circle-anim {
  0% {
    -webkit-transform: rotate(0) scale(.5) skew(1deg);
    -webkit-opacity: .1
  }

  30% {
    -webkit-transform: rotate(0) scale(.7) skew(1deg);
    -webkit-opacity: .5
  }

  100% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
    -webkit-opacity: .1
  }
}

@-o-keyframes coccoc-alo-circle-anim {
  0% {
    -o-transform: rotate(0) kscale(.5) skew(1deg);
    -o-opacity: .1
  }

  30% {
    -o-transform: rotate(0) scale(.7) skew(1deg);
    -o-opacity: .5
  }

  100% {
    -o-transform: rotate(0) scale(1) skew(1deg);
    -o-opacity: .1
  }
}

@-moz-keyframes coccoc-alo-circle-fill-anim {
  0% {
    -moz-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    -moz-transform: rotate(0) -moz-scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    -moz-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@-webkit-keyframes coccoc-alo-circle-fill-anim {
  0% {
    -webkit-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    -webkit-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@-o-keyframes coccoc-alo-circle-fill-anim {
  0% {
    -o-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    -o-transform: rotate(0) scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    -o-transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@-moz-keyframes coccoc-alo-circle-img-anim {
  0% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    -moz-transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    -moz-transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    -moz-transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    -moz-transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    -moz-transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    -moz-transform: rotate(0) scale(1) skew(1deg)
  }
}

@-webkit-keyframes coccoc-alo-circle-img-anim {
  0% {
    -webkit-transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    -webkit-transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    -webkit-transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    -webkit-transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    -webkit-transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    -webkit-transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    -webkit-transform: rotate(0) scale(1) skew(1deg)
  }
}

@-o-keyframes coccoc-alo-circle-img-anim {
  0% {
    -o-transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    -o-transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    -o-transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    -o-transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    -o-transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    -o-transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    -o-transform: rotate(0) scale(1) skew(1deg)
  }
}

@-moz-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none
  }
}

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none
  }
}

@-o-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none
  }
}

@-moz-keyframes fadeOutRight {
  0% {
    opacity: 1
  }

  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }
}

@-webkit-keyframes fadeOutRight {
  0% {
    opacity: 1
  }

  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }
}

@-o-keyframes fadeOutRight {
  0% {
    opacity: 1
  }

  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }
}

@keyframes fadeOutRight {
  0% {
    opacity: 1
  }

  100% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    -ms-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0)
  }
}

@-moz-keyframes coccoc-alo-circle-anim {
  0% {
    transform: rotate(0) scale(.5) skew(1deg);
    opacity: .1
  }

  30% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .5
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .1
  }
}

@-webkit-keyframes coccoc-alo-circle-anim {
  0% {
    transform: rotate(0) scale(.5) skew(1deg);
    opacity: .1
  }

  30% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .5
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .1
  }
}

@-o-keyframes coccoc-alo-circle-anim {
  0% {
    transform: rotate(0) scale(.5) skew(1deg);
    opacity: .1
  }

  30% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .5
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .1
  }
}

@keyframes coccoc-alo-circle-anim {
  0% {
    transform: rotate(0) scale(.5) skew(1deg);
    opacity: .1
  }

  30% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .5
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .1
  }
}

@-moz-keyframes coccoc-alo-circle-fill-anim {
  0% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@-webkit-keyframes coccoc-alo-circle-fill-anim {
  0% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@-o-keyframes coccoc-alo-circle-fill-anim {
  0% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@keyframes coccoc-alo-circle-fill-anim {
  0% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg);
    opacity: .2
  }

  100% {
    transform: rotate(0) scale(.7) skew(1deg);
    opacity: .2
  }
}

@-moz-keyframes coccoc-alo-circle-img-anim {
  0% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg)
  }
}

@-webkit-keyframes coccoc-alo-circle-img-anim {
  0% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg)
  }
}

@-o-keyframes coccoc-alo-circle-img-anim {
  0% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg)
  }
}

@keyframes coccoc-alo-circle-img-anim {
  0% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  10% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  20% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  30% {
    transform: rotate(-25deg) scale(1) skew(1deg)
  }

  40% {
    transform: rotate(25deg) scale(1) skew(1deg)
  }

  50% {
    transform: rotate(0) scale(1) skew(1deg)
  }

  100% {
    transform: rotate(0) scale(1) skew(1deg)
  }
}

#coccoc-alo-wrapper {
  position: fixed;
  width: 100%;
  bottom: 0;
  top: 0;
  left: 0;
  z-index: 2000000;
  overflow: visible;
  display: none;
  color: #383838
}

#coccoc-alo-wrapper.night-mode {
  color: #fff
}

.coccoc-alo-popup-close {
  -webkit-border-radius: 2px !important;
  -moz-border-radius: 2px !important;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  position: absolute !important;
  right: -15px !important;
  top: -15px !important;
  height: 30px !important;
  width: 30px !important;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3FpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDozZWEyNDI5ZC0yYmI3LWYzNDMtYjBjZi1jMGJjYTE4ODRmZjkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NjRGMTI2QTcxNDBFMTFFNUFENEZCRDVFQ0JDQjQyQzIiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NjRGMTI2QTYxNDBFMTFFNUFENEZCRDVFQ0JDQjQyQzIiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjVmYzc3OTY1LWUxNWUtNGU0Ni04ODFjLTBlOTQ3YjBmMzBmNyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozZWEyNDI5ZC0yYmI3LWYzNDMtYjBjZi1jMGJjYTE4ODRmZjkiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5iCEbHAAABl0lEQVR42sSXS07DMBCGnSKyDorEAVjACTgCIEVlXU5R9QjlCk3VAzTrLhMJ2NIVJ2DDuo9EsKUszEw0kaIQbI+bxy/9UhRP5pMcjz12pJTCQKfgO/AN+Bp8AfZo7Av8AX4Dv4CfwD/ajAhW2ANPwTtprj1946lyq6AP4I2014ZyGINPwAvZnBaUUwnGgJVsXqsqvAoOZXua/wceyfY1KngOlROWxjv4XLSrHfgKS3BALyYdQAUxJkUdu7o6jeNYZlmmnUeMwViNkOUieKiLTNNURlGkhOPYcrnMYw00RPDMJFIFZ0JRIYJfTaPr4BZQ1Fow9+EcgCAEWkLz/4zl9A1rzOUsTQCKJEny5yAIhO/73NV9GNjUhOM4tc8scae6PL3laedONYLXNtC6f85dXDNb6BHw0GgDKaCqxEz4fbFlpk1smQjnbJmCeqSuNO3jWNyDL8vHIrao4w6OxTGx/rQ+8z5an16bvd7a22pDvz0CuOU29NUrzKOuzqvlTN8orzAO89J2W7q0ndHYZ+nS9kw+6BL+CjAAEvDTBJC9qhAAAAAASUVORK5CYII=");
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer !important;
  -webkit-transition: .3s ease-out !important;
  -moz-transition: .3s ease-out !important;
  -o-transition: .3s ease-out !important;
  transition: .3s ease-out !important
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup-close {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3FpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDozZWEyNDI5ZC0yYmI3LWYzNDMtYjBjZi1jMGJjYTE4ODRmZjkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OUY2REUyNDQxNDE2MTFFNThBNEJENTVFNDA2QjFFOUEiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OUY2REUyNDMxNDE2MTFFNThBNEJENTVFNDA2QjFFOUEiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjVmYzc3OTY1LWUxNWUtNGU0Ni04ODFjLTBlOTQ3YjBmMzBmNyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozZWEyNDI5ZC0yYmI3LWYzNDMtYjBjZi1jMGJjYTE4ODRmZjkiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz56uyuzAAABfUlEQVR42sSXvU7DMBDHYxCdw8IDMMCWTDwCdClznLcJr9BUfYs+ALDSqXMisTD3S4K1MBx3kS1ZVuqvNslf+kuRfL5f5OTsMwOAyEFX6DH6Ef2AvkXHYuwH/YVeod/Rr+g/a0YCGxyjC/QW3LUTc2JTbhOUo9cQrrXI4Qy+RM/hfJqLnEYwBSzg/FrocB1cQneaHQNn0L0yyWOinKg0PtE3Ubfaou+bEhRvUEB/KuRSj2x1muc51HVtzUgxnHNbGLFGBJ7YIquqgjRNjXAaS5KkiXXQhMBTl0gT3BNKKgn84RrdBg+AkpaR5z7cAAhEwEBo850JfPCdJeGBUNLhIqQYGWOtz17yXWp1edVlD1nqZQi07Zv7/lzTUOgJ8NJpA5FQU2JP+LPcMvfGIyXLnBISnGJdt8xBDom+j8Ud+k49FvtqBPix1mc2ROszaLM3WHurN/SbE4Ab34Zev8K82Opc017MMV5hmOel7Um5tF2LsW/l0vYm/GtL+C/AAAHy+OD95QLeAAAAAElFTkSuQmCC")
}

#coccoc-alo-wrapper .coccoc-alo-popup-close:hover {
  opacity: .6 !important
}

.coccoc-alo-popup {
  display: inline-block;
  position: relative;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  border-radius: 16px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3wYZCyIUPNCUUwAAAA1JREFUCNdj+P///2cACe8D8SyfS4EAAAAASUVORK5CYII=");
  -webkit-transition: .6s ease-out;
  -moz-transition: .6s ease-out;
  -o-transition: .6s ease-out;
  transition: .6s ease-out;
  margin: 0 auto;
  z-index: 200001;
  text-align: center;
  padding: 60px 75px
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3wYZCyAMHYpuhwAAAA1JREFUCNdjMDY2/gwAAsMBjX/tf+YAAAAASUVORK5CYII=")
}

#coccoc-alo-wrapper .coccoc-alo-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEUAAACnej3aAAAAAXRSTlOZyTXzhgAAAApJREFUCB1jYAAAAAIAAc/INeUAAAAASUVORK5CYII=");
  top: 0;
  left: 0;
  z-index: 200000
}

.coccoc-alo-popup h3 {
  font-size: 24px;
  margin: 0 0 40px;
  font-family: 'Open Sans';
  font-weight: 300;
  white-space: nowrap
}

.night-mode .coccoc-alo-popup h3 {
  font-size: 23px
}

.night-mode .coccoc-alo-message {
  padding-bottom: 0
}

.coccoc-alo-message {
  height: 32px;
  padding: 18px 0 13px 0;
  text-align: center;
  clear: both;
  font-size: 14px
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper .label,
#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper .label+.input {
  float: left;
  width: 49%
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper .input {
  padding: 0 13px
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper label {
  color: #616161;
  font-size: 18px;
  height: 28px;
  line-height: 28px;
  padding-right: 15px
}

.coccoc-alo-number {
  color: #00bed5;
  font-size: 28px;
  font-family: Montserrat, "Lucida Console", Monaco, monospace, sans-serif;
  font-weight: normal;
  background-color: transparent;
  border: none;
  border-width: 0;
  display: inline-block;
  border-bottom: #00bed5 solid 1px;
  padding-bottom: 10px;
  margin: 0 auto;
  width: 221px
}

coccoc-alo-number::-ms-clear {
  display: none;
  width: 0;
  height: 0
}

.night-mode .coccoc-alo-number {
  border: #00bed5 solid 1px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  padding: 13px 31px
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"].valid-invalid {
  color: #ff496b
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]:focus {
  outline: 0
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-webkit-input-placeholder {
  color: #d1d1d1
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-moz-placeholder {
  color: #d1d1d1
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-ms-input-placeholder {
  color: #d1d1d1
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-moz-placeholder {
  color: #d1d1d1
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-webkit-input-placeholder {
  color: #60615f
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-moz-placeholder {
  color: #60615f
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-ms-input-placeholder {
  color: #60615f
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup .coccoc-alo-input-wrapper input[type="text"]::-moz-placeholder {
  color: #60615f
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-submit {
  border: none;
  border-width: 0;
  padding: 20px 40px;
  background-color: #333;
  -webkit-border-radius: 68px;
  -moz-border-radius: 68px;
  -webkit-border-radius: 68px;
  -moz-border-radius: 68px;
  border-radius: 68px;
  font-family: "Open Sans", Arial, Helvetica, sans-serif;
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  outline: none !important
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup .coccoc-alo-submit {
  background-color: #00bed5
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-submitavtive,
#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-submitvisited {
  outline: none !important
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-submit-moz-focus-inner {
  border: 0
}

#coccoc-alo-wrapper .coccoc-alo-popup .coccoc-alo-submit:hover {
  background-color: #00aff2;
  background-color: #00aff2
}

.coccoc-alo-blur {
  -webkit-filter: blur(3px);
  -ms-filter: blur(3px);
  -moz-filter: blur(3px);
  -o-filter: blur(3px);
  filter: blur(3px);
  filter: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxmaWx0ZXIgaWQ9ImJsdXIiPjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjMiLz48L2ZpbHRlcj48L3N2Zz4jYmx1cg==#blur")
}

#coccoc-countdown {
  padding-top: 20px;
  font-family: "Open Sans", Arial, Helvetica, sans-serif;
  font-size: 28px;
  font-weight: 300
}

.coccoc-alo-request-time {
  font-family: "Open Sans", Arial, Helvetica, sans-serif;
  padding: 6px 12px;
  font-size: 18px;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-request-time {
  color: #fff;
  background-color: #515350;
  border: 1px solid #606260
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-popup h3 {
  margin-bottom: 15px
}

#coccoc-alo-wrapper.night-mode .coccoc-alo-form .coccoc-alo-select-wrapper {
  margin-bottom: 35px
}

.coccoc-alo-table {
  display: table;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0
}

.coccoc-alo-cell {
  display: table-cell;
  vertical-align: middle;
  text-align: center
}

.valid-invalid-message {
  font-size: 13px;
  color: #ff496b
}

.valid-invalid-message:before {
  content: "* "
}



.phone_text {
  position: absolute;
  top: 90%;
  bottom: auto;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  left: 37px;
  background: #ededed;
  padding: 9px 17px;
  border-radius: 50px;
  font-size: 23px;
  border: 1px solid #ccc;
  font-weight: bold;
  color: red;
  padding-left: 42px;
  text-align: right;
  z-index: -1;
  opacity: 1;
  visibility: visible;
  overflow: visible;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -ms-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  /* number of lines to show */
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

.text-oscuro {
  color: #445065;
}

.title {}

.title::after,
.title::before {
  content: '';
  position: absolute;
  right: 1.25em;
  top: 1.25em;
  width: 2px;
  height: 0.75em;
  background-color: #7A7572;
  transition: all 0.2s;
}

.title::after {
  transform: rotate(90deg);
}

.content {
  max-height: 0;
}

.toggle:checked+.title,
.toggle:checked+.title+.content {
  box-shadow: 3px 3px 6px #ddd, -3px 3px 6px #ddd;
}

.toggle:checked+.title+.content {
  max-height: 100%;
}

.toggle:checked+.title::before {
  transform: rotate(90deg) !important;
}

.video-container {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 */
  height: 0;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.line {
  position: relative;
  margin-bottom: 2rem;
  margin-top: 2rem;
}

.line:before {
  display: block;
  content: '';
  width: 8px;
  height: 8px;
  background-color: #416044;
  position: relative;
  transform: rotate(45deg) translateX(-50%);
  left: 50%;
  top: 10px;
}

.line:after {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 780px;
  height: 3px;
  background: linear-gradient(to right, #416044 0%, #416044 45%, rgba(0, 0, 0, 0) 45%, rgba(0, 0, 0, 0) 55%, #416044 55%, #416044 100%);
}

h1.special {
  position: relative;
  margin-bottom: 2rem;
  margin-top: 0;
}

h1.special:before {
  display: block;
  content: '';
  width: 8px;
  height: 8px;
  background-color: #416044;
  position: relative;
  transform: rotate(45deg) translateX(-50%);
  left: 50%;
  top: 10px;
}

h1.special:after {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 1024px;
  height: 3px;
  background: linear-gradient(to right, #416044 0%, #416044 45%, rgba(0, 0, 0, 0) 45%, rgba(0, 0, 0, 0) 55%, #416044 55%, #416044 100%);
}

h1.special:before,
h1.special:after {
  position: absolute;
  top: 120%;
}

.line {
  position: relative;
  margin-bottom: 2rem;
  margin-top: 2rem;
}

.line:before {
  display: block;
  content: '';
  width: 12px;
  height: 12px;
  background-color: #416044;
  position: relative;
  transform: rotate(45deg) translateX(-50%);
  left: 50%;
  top: 10px;
}

.line:after {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 780px;
  height: 5px;
  background: linear-gradient(to right, #416044 0%, #416044 45%, rgba(0, 0, 0, 0) 45%, rgba(0, 0, 0, 0) 55%, #416044 55%, #416044 100%);
}

.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}