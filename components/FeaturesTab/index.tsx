import { useRouter } from "next/router";
import LinkComponent from "../Link";
const dataPackages = [
  {
    id: 1,
    href: "/goi_cham_soc/goi-cham-soc-phong-ngua/",
    label: "Chăm Sóc Phòng Ngừa",
    label_en: "Preventive Care",
    desc: "Đề xuất giải pháp để đảm bảo chủ động trong việc gìn giữ, bảo vệ sức khỏe thể chất và tinh thần.",
    desc_en: "Proposing solutions to ensure proactive maintenance and protection of both physical and mental health."
  },
  {
    id: 2,
    href: "/initial_treatment/goi-dieu-tri-ban-dau/",
    label: "Điều Trị Ban Đầu",
    label_en: "Primary care",
    desc: "Thực hiện các chẩn đoán và xét nghiệm, chúng tôi sẽ đưa ra phương pháp điều trị cần thiết",
    desc_en: "We recommend the appropriate treatment based on diagnostics and tests."
  },
  {
    id: 3,
    href: "/chronicdiseases/goi-quan-ly-benh-man-tinh/",
    label: "Quản Lý Bệnh Mạn Tính",
    label_en: "Chronic Diseases",
    desc: "Đồng hành chăm sóc chặt chẽ, kiểm soát bệnh và tối ưu hóa việc dùng thuốc an toàn, hiệu quả.",
    desc_en: "Close personal companionship, effective disease management, and the optimization of safe medication use."
  },
  {
    id: 4,
    href: "/comprehensive_health/goi-suc-khoe-toan-dien/",
    label: "Sức Khoẻ Toàn Diện",
    label_en: "Wellness",
    desc: "Theo dõi sức khỏe toàn diện, đề xuất dinh dưỡng và chăm sóc tinh thần tối ưu để bạn luôn khỏe mạnh cả thể chất lẫn tinh thần.",
    desc_en: "Keep track of your overall health and recommend the best nutrition and psychological care to maintain both your physical and mental well-being."
  }
]

const FeaturesTab = () => {
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";
  return (
    <>
      <section className="relative pb-12 pt-12 lg:pb-12 hidden md:block">
        <div className="relative mx-auto max-w-full md:mx-7 2xl:px-0">
          <div className="mx-auto text-start mb-8">
            <h2 className="font-bold md:text-[28px] text-2xl text-[#156634] uppercase pb-6">{locale == "vi" ? "4 NỀN TẢNG CHÍNH" : "4 Core Services"}</h2>
            <p className="text-base">{locale === "en" ? "" : "Chúng tôi tập trung vào chăm sóc sức khỏe toàn diện nhằm giúp khách hàng tiết kiệm thời gian, chi phí, tránh lạm dụng thuốc và các xét nghiệm không cần thiết"}</p>
          </div>
          <div className="grid max-w-md grid-cols-1 gap-4 lg:max-w-none lg:grid-cols-4 shadow-sm">
            {dataPackages.map((dataPackage: any, index: number) => (
              <>
                <LinkComponent href={dataPackage.href} skipLocaleHandling={false} locale={locale}>
                  <div key={index} className="group flex flex-col items-start text-start gap-2 p-5 hover:bg-[#F1FFEB]">
                    <div className="w-12 h-12 text-xl flex items-center justify-center font-semibold text-[#156634] p-2 rounded-full bg-[#F1FFEB] group-hover:bg-[#156634] group-hover:text-white">{dataPackage.id}</div>
                    <h3 className="text-lg font-semibold text-[#156634]">{locale === 'en' ? dataPackage.label_en : dataPackage.label}</h3>
                    <p className="text-sm h-20">
                      {locale === 'en' ? dataPackage.desc_en : dataPackage.desc}
                    </p>
                    <div className="mt-4">
                      <svg
                        className="w-6 h-6"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.9385 6L20.9999 12.0613L14.9385 18.1227"
                          stroke="black"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="group-hover:stroke-[#156634]"
                        ></path>
                        <path
                          d="M3 12.061L21 12.061"
                          stroke="black"
                          strokeWidth="2"
                          strokeLinecap="round"
                          className="group-hover:stroke-[#156634]"
                        ></path>
                      </svg>
                    </div>
                  </div>
                </LinkComponent>
              </>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default FeaturesTab;
