import React from 'react'
import { useRouter } from 'next/router';
export default function HealthTalksMainCore() {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const companyData = dataHealthIcon(locale);
    return (
        <>
            <section className="relative">
                    <div className="w-full flex-col justify-start items-start md:items-center inline-flex">
                            <ul role="list" className="mt-8">
                                {companyData.map((item, index) => (
                                    <li key={item.id}>
                                        <div className="relative pb-8">
                                            {index < companyData.length - 1 && (
                                                <span className="absolute left-6 top-4 -ml-px h-full w-0.5 border-l border-dashed border-[#C8EAD4]"></span>
                                            )}
                                            <div className="relative flex space-x-3">
                                                <div>
                                                    <span className="h-12 w-12 rounded-full flex items-center justify-center bg-white">
                                                        <img src={item.image} alt="" className="w-8 h-8" />
                                                    </span>
                                                </div>
                                                <div className="flex-col">
                                                    <h4 className="text-base md:text-center font-medium">{item.title}</h4>
                                                    <p className="text-sm md:text-center text-justify">{item.desc}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                    </div>
            </section>
        </>
    )
}


export const dataHealthIcon = (locale: string) => [
    {
        id: 1,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Hinh_thuc_cd2d2f2008.png",
        title: locale === "en" ? "Format" : "Hình thức",
        desc: locale === "en" ? "Offline and online" : "Trực tiếp và trực tuyến",
    },
    {
        id: 2,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Dia_diem_9c6ecdf1ab.png",
        title: locale === "en" ? "Location" : "Địa điểm",
        desc: locale === "en" ? "Corporate's office" : "Tại doanh nghiệp",
    },
    {
        id: 3,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thoi_gian_b71eda8fd3.png",
        title: locale === "en" ? "Duration" : "Thời gian trình bày",
        desc: locale === "en" ? "60 - 90 minutes/topic" : "60 - 90 phút/chủ đề",
    },
    {
        id: 4,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Chu_de_03b3b26e2a.png",
        title: locale === "en" ? "Topic" : "Chủ đề",
        desc: locale === "en" ? "Available topics or as business needs" : "Chủ đề có sẵn hoặc theo nhu cầu của doanh nghiệp",
    },
]
