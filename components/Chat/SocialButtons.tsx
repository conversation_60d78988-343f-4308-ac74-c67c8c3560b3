import React, { useState } from "react";
import { useRouter } from "next/router";

const SocialButtons = () => {
    const router = useRouter();
    const locale = (router.query.locale as string) || "en";
    const [isZaloOpen, setZaloOpen] = useState(false);
    const handleZaloClick = () => {
        setZaloOpen(true);
    };
    const handleMessengerClick = () => {
        window.open("https://www.facebook.com/messages/t/104159875780641", "_blank");
    };

    const handleZaloChatClose = () => {
        setZaloOpen(false);
    };

    return (
        <div className="fixed bottom-4 right-4 flex flex-col items-end z-10 bg-blue-100 rounded-3xl hover:bg-blue-200 z-[9999]">
            {!isZaloOpen && (
                <button
                    className=""
                    onClick={handleZaloClick}
                >
                    <img alt="ZALO" src="https://d3e4m6b6rxmux9.cloudfront.net/Icon_of_Zalo_svg_437364df07.webp?updated_at=2023-07-26T09:14:12.070Z" className="p-2 rounded-full h-[55px]" />

                </button>
            )}
            {isZaloOpen && (
                <div className="bg-green-50 absolute bottom-16 z-99 lg:w-[350px] w-[300px] rounded-lg">
                    <div className="bg-green-800 rounded-t-lg">
                        <div className="flex p-2">
                            <img src="https://d3e4m6b6rxmux9.cloudfront.net/Group_1_f6d5bbb682.png?updated_at=2023-07-26T08:26:08.561Z" className="p-2 rounded-full h-10" />
                            <p className="py-2 text-white font-semibold">ECHO MEDI</p>
                            <button
                                className="text-white font-bold py-2 px-4 rounded-full absolute top-0 right-0 focus:outline-none"
                                onClick={handleZaloChatClose}
                            >
                                <svg fill="#ffffff" width="25px" height="25px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <g data-name="Layer 2">
                                        <g data-name="collapse">
                                            <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0" />
                                            <path d="M19 9h-2.58l3.29-3.29a1 1 0 1 0-1.42-1.42L15 7.57V5a1 1 0 0 0-1-1 1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 0-2z" />
                                            <path d="M10 13H5a1 1 0 0 0 0 2h2.57l-3.28 3.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0L9 16.42V19a1 1 0 0 0 1 1 1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1z" />
                                        </g>
                                    </g>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <p className="text-center text-xs pt-[30px] text-slate-600 pb-7">{locale === "en" ? "Start conversation with ECHO MEDI" : "Bắt đầu trò chuyện với ECHO MEDI"}</p>
                    <div>

                    </div>
                    <div className="text-white pb-5 font-semibold text-xs items-center justify-between flex">
                        <div className="flex flex-col w-full pr-5">
                            <a href="https://zalo.me/982428549715388006" target="blank" className="ml-auto">
                                <button className="bg-green-800 text-white p-2 border rounded-3xl mb-3 hover:bg-white hover:text-black hover:border-green-500 hover:border">
                                    {locale === "en" ? "Chat with Thu Duc Clinic" : "Nhắn tin với chi nhánh Thủ Đức"}
                                </button>
                            </a>
                            <a href="https://zalo.me/90957007944802543" target="blank" className="ml-auto">
                                <button className="bg-green-800 text-white p-2 border rounded-3xl mb-3 hover:bg-white hover:text-black hover:border-green-500 hover:border">
                                    {locale === "en" ? "Chat with District 7 Clinic" : "Nhắn tin với chi nhánh Quận 7"}
                                </button>
                            </a>
                            <a href="https://zalo.me/2785469198341624818" target="blank" className="ml-auto">
                                <button className="bg-green-800 text-white p-2 border rounded-3xl mb-3 hover:bg-white hover:text-black hover:border-green-500 hover:border">
                                    {locale === "en" ? "Chat with Binh Duong Clinic" : "Nhắn tin với chi nhánh Bình Dương"}
                                </button>
                            </a>
                        </div>
                    </div>


                </div>
            )}
        </div>
    );
}
export default SocialButtons;
