import { useRouter } from "next/router";
import LinkComponent from "../Link";

type FaqData = {
  activeFaq: number;
  id: number;
  number: string;
  handleFaqToggle: (id: number) => void;
  question: {
    en: string;
    vi: string;
  };
  answer?: {
    en: string[];
    vi: string[];
  };
  image?: string;
  linkUrl?: string;
};

const FAQItem = ({ faqData }: { faqData: FaqData }) => {
  const { activeFaq, id, number, handleFaqToggle, question, answer, image, linkUrl } = faqData;
  const isActive = activeFaq === id;
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  return (
    <div className="flex flex-col rounded-lg bg-white py-1.5">
      <button
        onClick={() => handleFaqToggle(id)}
        className={`flex cursor-pointer items-center justify-between text-base ${isActive ? "font-bold" : "font-medium"} px-8 py-2 text-black`}
        aria-expanded={isActive}
      >
        <p className="flex items-center justify-start text-start text-sm md:text-base">
          <span className="mr-8 font-bold md:text-[28px] text-[20px] text-[#14813D]">{number}</span>
          {locale === 'vi' ? question.vi : question.en}</p>
        {isActive ? (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="#156634"
            xmlns="http://www.w3.org/2000/svg"
            className="rotate-45 ml-4 w-4 h-4"
          >
            <path
              d="M7.83331 7.83337V0.833374H10.1666V7.83337H17.1666V10.1667H10.1666V17.1667H7.83331V10.1667H0.833313V7.83337H7.83331Z"
              fill="#156634"
            />
          </svg>
        ) : (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="#156634"
            xmlns="http://www.w3.org/2000/svg"
            className="ml-4 w-4 h-4"
          >
            <path
              d="M7.83331 7.83337V0.833374H10.1666V7.83337H17.1666V10.1667H10.1666V17.1667H7.83331V10.1667H0.833313V7.83337H7.83331Z"
              fill="#156634"
            />
          </svg>
        )}
      </button>
      {linkUrl && (
        <div className={`px-6 py-1.5 ${isActive ? "block" : "hidden"}`}>
          <LinkComponent locale={locale} skipLocaleHandling={false} href={linkUrl}>
            <span className="font-medium text-[#14813D] text-sm underline md:px-16">{locale === "en" ? "See more" : "Xem thêm"}</span>
          </LinkComponent>
          {image &&
            <img src={image} alt="answer" className="w-full md:px-16 py-0.5" />
          }
        </div>
      )}
      <p className={`px-6 py-1.5 ${isActive ? "block ml-2 md:ml-0" : "hidden"}`}>
        {locale === 'vi' ? answer?.vi.map((paragraph: any, index: any) => (
          <p className="md:px-16 py-0.5 text-base text-justify" key={index}>{paragraph}</p>
        )) : answer?.en.map((paragraph: any, index: any) => (
          <p className="md:px-16 py-0.5 text-base text-justify" key={index}>{paragraph}</p>
        ))}
      </p>
    </div>
  );
};

export default FAQItem;
