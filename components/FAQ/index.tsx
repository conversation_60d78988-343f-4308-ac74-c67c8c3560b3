import { useState } from "react";
import FAQItem from "./FAQItem";
import faqData from "./faqData";
import LinkComponent from "../Link";
import { useRouter } from "next/router";
import React from "react";
const detectMob = () => {
  const toMatch = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,

    /Windows Phone/i
  ];

  return toMatch.some((toMatchItem) => {
    return navigator.userAgent.match(toMatchItem);
  });
}
const FAQ = () => {
  const [activeFaq, setActiveFaq] = useState(0);
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";
  const handleFaqToggle = (id: number) => {
    activeFaq === id ? setActiveFaq(0) : setActiveFaq(id);
  };
  const [cnt, setCnt] = useState(detectMob() ? 2 : 5);
  return (
    <>
      <section className="pb-20 mt-8">
        <h2 className="text-center font-bold md:text-[28px] text-2xl my-6 py-2 uppercase text-[#156634]">
          {locale === "en" ? "Frequently Asked Questions" : "Câu Hỏi Thường Gặp"}
        </h2>
        <div className="rounded-lg space-y-6">
          {faqData.slice(0, cnt == -1 ? faqData.length : cnt).map((faq, key) => (
            <FAQItem
              key={key}
              faqData={{ ...faq, activeFaq, handleFaqToggle }}
            />
          ))}
        </div>
        <div className="flex justify-center mt-8">
          <div className="py-2 text-center inline text-base border border-[#14813d] hover:bg-[#14813d] hover:text-white rounded-full text-[#156634] px-5">
            <LinkComponent
              href={"/question"}
              locale={locale}
              skipLocaleHandling={false}
            > {locale === "vi" ? "Xem tất cả" : "Load all"}</LinkComponent>
          </div>
        </div>
      </section>
    </>
  );
};

export default FAQ;
