import { useRouter } from 'next/router';
import React from 'react'
import Image from 'next/image';
import { shimmer, toBase64 } from '../../lib/ui';
import LinkComponent from '../Link';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper';
export default function FamilySeaction() {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    return (
        <>
         <section className="relative">
                <div className="w-full my-16">
                    <div className='flex items-start md:gap-12 gap-6 flex-col md:flex-row'>
                        <div className="md:w-1/2 w-full">
                            <div className='relative'>
                                <div>
                                    <Image
                                        src="https://api.echomedi.com/uploads/BG_Bac_Si_Gia_Dinh_1d69fcbfff.png"
                                        alt="Banner"
                                        width={600}
                                        height={400}
                                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(600, 400))}`}
                                        className="object-center absolute -z-10 -top-10 -left-10"
                                    />
                                </div>
                                <div>
                                    <Image
                                        src="https://api.echomedi.com/uploads/Bac_Si_Gia_Dinh_12279ffef3.png"
                                        alt="Banner"
                                        width={1920}
                                        height={300}
                                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 300))}`}
                                        className="object-cover"
                                    />
                                    <Image loading='lazy' width={65} height={32} className="absolute -top-1 -right-1" alt="Image Icon Sale" src={locale === "en" ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_0dadd0f5c5.svg" : "https://d3e4m6b6rxmux9.cloudfront.net/Uu_dai_6d4438cf56.svg"} />
                                </div>
                            </div>
                        </div>
                        <div className="md:p-8 md:w-1/2 w-full rounded-2xl">
                            <h3 className="font-bold md:text-[28px] text-2xl text-[#156634] uppercase mb-4 text-center md:text-left">{locale === "en" ? "Family Doctor" : "Bác sĩ gia đình"}</h3>
                            <section className="space-y-2">
                                {benefitsData.map((benefit, index) => (
                                    <div key={index} className="flex items-center gap-1">
                                        <div>
                                            <svg width="45" height="44" viewBox="0 0 45 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g filter="url(#filter0_d_9661_11508)">
                                                    <rect x="8.41016" y="4.99992" width="28.0089" height="28" rx="7.86426" fill="white" />
                                                </g>
                                                <path fillRule="evenodd" clipRule="evenodd" d="M30.1063 14.9764C30.5028 15.3608 30.5125 15.9939 30.1281 16.3904L22.8553 23.8904L22.1656 24.6017L21.4479 23.9186L16.7207 19.4186C16.3207 19.0378 16.3051 18.4048 16.6859 18.0048C17.0666 17.6048 17.6996 17.5892 18.0996 17.97L22.1093 21.7868L28.6923 14.9981C29.0767 14.6016 29.7098 14.5919 30.1063 14.9764Z" fill="#14813D" />
                                                <defs>
                                                    <filter id="filter0_d_9661_11508" x="0.545898" y="0.281369" width="43.7383" height="43.7285" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                                                        <feFlood floodOpacity="0" result="BackgroundImageFix" />
                                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                                        <feOffset dy="3.1457" />
                                                        <feGaussianBlur stdDeviation="3.93213" />
                                                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
                                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9661_11508" />
                                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9661_11508" result="shape" />
                                                    </filter>
                                                </defs>
                                            </svg>
                                        </div>
                                        <div>
                                            <p className='text-sm md:text-base'>{locale === "en" ? benefit.en : benefit.vi}</p>
                                        </div>
                                    </div>
                                ))}
                            </section>
                            <div className="flex gap-4 justify-center md:justify-start mt-4">
                                <section className='block md:hidden'>
                                    <button className="md:px-8 px-6 md:py-2 py-1 rounded-full bg-[#156634] hover:bg-[#14813d]">
                                        <a href="tel:1900638408" className="text-center text-white text-sm md:text-base font-medium">{locale === "en" ? "Contact" : "Tư vấn gói"}</a>
                                    </button>
                                </section>
                                <LinkComponent href={"/familydoctor"} locale={locale} skipLocaleHandling={false}>
                                    <div className="flex items-center justify-center py-2 px-6 text-sm font-medium gap-1">
                                        {locale === "en" ? "Learn More" : "Xem chi tiết"}
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M7.88675 2.91685C8.03978 2.9175 8.18643 2.97825 8.29508 3.08602L10.9726 5.76352C11.3003 6.09165 11.4844 6.53644 11.4844 7.00019C11.4844 7.46394 11.3003 7.90873 10.9726 8.23685L8.29508 10.9144C8.18579 11.023 8.03794 11.084 7.88383 11.084C7.72972 11.084 7.58188 11.023 7.47258 10.9144C7.41791 10.8601 7.37451 10.7956 7.34489 10.7245C7.31528 10.6534 7.30003 10.5772 7.30003 10.5002C7.30003 10.4232 7.31528 10.3469 7.34489 10.2759C7.37451 10.2048 7.41791 10.1402 7.47258 10.086L10.1501 7.41435C10.2048 7.36013 10.2482 7.29561 10.2778 7.22452C10.3074 7.15344 10.3226 7.0772 10.3226 7.00019C10.3226 6.92318 10.3074 6.84694 10.2778 6.77585C10.2482 6.70477 10.2048 6.64025 10.1501 6.58602L7.47258 3.91435C7.41791 3.86012 7.37451 3.79561 7.3449 3.72452C7.31528 3.65344 7.30003 3.57719 7.30003 3.50019C7.30003 3.42318 7.31528 3.34694 7.3449 3.27585C7.37451 3.20477 7.41791 3.14025 7.47258 3.08602C7.52709 3.03196 7.59173 2.98918 7.6628 2.96015C7.73387 2.93112 7.80998 2.91641 7.88675 2.91685Z" fill="#322F35" />
                                            <path d="M3.80194 2.91685C3.95497 2.9175 4.10162 2.97825 4.21027 3.08602L7.71026 6.58602C7.76494 6.64025 7.80834 6.70477 7.83795 6.77585C7.86757 6.84694 7.88281 6.92318 7.88281 7.00019C7.88281 7.0772 7.86757 7.15344 7.83795 7.22452C7.80834 7.29561 7.76494 7.36013 7.71026 7.41435L4.21027 10.9144C4.10097 11.023 3.95313 11.084 3.79902 11.084C3.64491 11.084 3.49707 11.023 3.38777 10.9144C3.3331 10.8601 3.2897 10.7956 3.26008 10.7245C3.23047 10.6534 3.21522 10.5772 3.21522 10.5002C3.21522 10.4232 3.23047 10.3469 3.26008 10.2759C3.2897 10.2048 3.3331 10.1402 3.38777 10.086L6.4736 7.00019L3.38777 3.91435C3.3331 3.86012 3.2897 3.79561 3.26009 3.72452C3.23047 3.65344 3.21522 3.57719 3.21522 3.50019C3.21522 3.42318 3.23047 3.34694 3.26009 3.27585C3.2897 3.20477 3.3331 3.14025 3.38777 3.08602C3.44228 3.03196 3.50692 2.98918 3.57799 2.96015C3.64906 2.93112 3.72517 2.91641 3.80194 2.91685Z" fill="#322F35" />
                                        </svg>
                                    </div>
                                </LinkComponent>
                            </div>
                        </div>

                    </div>
                </div>
            </section>
            <section className="relative my-12">
                <h3 className="text-center md:text-[26px] text-2xl text-[#156634] uppercase font-bold mb-2 md:mb-6">{locale === "en" ? "The only integrated healthcare package in ECHO MEDI: Physical - Psychological - Genetic" : "Gói Chăm Sóc Sức Khỏe tích hợp duy nhất tại ECHO MEDI:\n Thể Chất - Tâm Lý - Gen"}</h3>
                <p className="text-base md:text-start text-justify">{locale === "en" ? "ECHO MEDI offers a unique, premium healthcare solution in Vietnam, integrating three essential factors: physical health, psychological well-being, and genetic information. Each examination package is tailored to individual needs, helping customers better understand their health and body status. Based on the results, our medical team provides personalized care and treatment options, ensuring comprehensive and effective healthcare." : "Giải pháp chăm sóc sức khỏe vượt trội và duy nhất tại Việt Nam, kết hợp đồng thời ba yếu tố quan trọng: thể chất, tâm lý và gen. Mỗi gói khám được thiết kế riêng biệt, mang tính cá nhân hóa, giúp khách hàng hiểu rõ hơn về cơ thể và tình trạng sức khỏe của mình. Dựa trên kết quả phân tích, đội ngũ y tế tại ECHO MEDI sẽ đưa ra các biện pháp chăm sóc và điều trị phù hợp, đảm bảo sự chăm sóc toàn diện và hiệu quả."}</p>
            </section>
           
            <section className='hidden md:block'>
                <div className="grid max-w-md grid-cols-1 gap-6 lg:max-w-none lg:grid-cols-3 mb-20">
                    {dataPackage.map((link: any, index: number) => (
                        <div key={index} className="flex justify-center items-center">
                            <LinkComponent href={link.href} skipLocaleHandling={false} locale={locale}>
                                <div className="bg-white rounded-xl group">
                                    <div className="w-full relative rounded-t-[12px] overflow-hidden">
                                        <img src={locale === "en" ? link.imgSrc_EN : link.imgSrc} alt="Doctor examining a patient" className="w-full object-cover transition-opacity md:group-hover:opacity-80" />
                                    </div>
                                    <div className='relative bg-[#EBFFF2] z-10 bottom-8 flex items-center justify-center gap-4 mx-8 py-2 px-4 rounded-3xl'>
                                        <div className='flex items-center bg-white p-1 rounded-lg gap-2'>
                                            <svg className='w-4 h-4' width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM8.39762 5.28855C8.62936 5.05531 8.62936 4.67715 8.39762 4.4439C8.16588 4.21066 7.79016 4.21066 7.55842 4.4439L5.20879 6.80877L4.44158 6.03658C4.20984 5.80334 3.83412 5.80334 3.60238 6.03658C3.37064 6.26982 3.37064 6.64798 3.60238 6.88123L4.78919 8.07574C5.02093 8.30898 5.39665 8.30898 5.62839 8.07574L8.39762 5.28855Z" fill="#14813D" />
                                            </svg>
                                            <span className='text-xs md:text-sm text-[#156634] font-medium'>{locale === "en" ? "General Examination" : "Khám Tổng Quát"}</span>
                                        </div>
                                        <div className='flex items-center bg-white p-1 rounded-lg gap-2'>
                                            <svg className='w-4 h-4' width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM8.39762 5.28855C8.62936 5.05531 8.62936 4.67715 8.39762 4.4439C8.16588 4.21066 7.79016 4.21066 7.55842 4.4439L5.20879 6.80877L4.44158 6.03658C4.20984 5.80334 3.83412 5.80334 3.60238 6.03658C3.37064 6.26982 3.37064 6.64798 3.60238 6.88123L4.78919 8.07574C5.02093 8.30898 5.39665 8.30898 5.62839 8.07574L8.39762 5.28855Z" fill="#14813D" />
                                            </svg>
                                            <span className='text-xs md:text-sm text-[#156634] font-medium'>{locale === "en" ? "Gene decoding" : "Phân Tích Gen"}</span>
                                        </div>
                                        <div className='flex items-center bg-white p-1 rounded-lg gap-2'>
                                            <svg className='w-4 h-4' width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM8.39762 5.28855C8.62936 5.05531 8.62936 4.67715 8.39762 4.4439C8.16588 4.21066 7.79016 4.21066 7.55842 4.4439L5.20879 6.80877L4.44158 6.03658C4.20984 5.80334 3.83412 5.80334 3.60238 6.03658C3.37064 6.26982 3.37064 6.64798 3.60238 6.88123L4.78919 8.07574C5.02093 8.30898 5.39665 8.30898 5.62839 8.07574L8.39762 5.28855Z" fill="#14813D" />
                                            </svg>
                                            <span className='text-xs md:text-sm text-[#156634] font-medium'>{locale === "en" ? "Psychological Counseling" : "Tham Vấn Tâm Lý"}</span>
                                        </div>
                                    </div>
                                    <div className='px-4 relative bottom-4'>
                                        <p className='text-base font-semibold whitespace-pre-line uppercase'>{locale === "en" ? link.title_en : link.title}</p>
                                        <div className="flex items-center justify-end py-2 px-6 text-sm font-medium text-[#156634]">
                                            {locale === "en" ? "Learn More" : "Xem chi tiết"}
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.88675 2.91685C8.03978 2.9175 8.18643 2.97825 8.29508 3.08602L10.9726 5.76352C11.3003 6.09165 11.4844 6.53644 11.4844 7.00019C11.4844 7.46394 11.3003 7.90873 10.9726 8.23685L8.29508 10.9144C8.18579 11.023 8.03794 11.084 7.88383 11.084C7.72972 11.084 7.58188 11.023 7.47258 10.9144C7.41791 10.8601 7.37451 10.7956 7.34489 10.7245C7.31528 10.6534 7.30003 10.5772 7.30003 10.5002C7.30003 10.4232 7.31528 10.3469 7.34489 10.2759C7.37451 10.2048 7.41791 10.1402 7.47258 10.086L10.1501 7.41435C10.2048 7.36013 10.2482 7.29561 10.2778 7.22452C10.3074 7.15344 10.3226 7.0772 10.3226 7.00019C10.3226 6.92318 10.3074 6.84694 10.2778 6.77585C10.2482 6.70477 10.2048 6.64025 10.1501 6.58602L7.47258 3.91435C7.41791 3.86012 7.37451 3.79561 7.3449 3.72452C7.31528 3.65344 7.30003 3.57719 7.30003 3.50019C7.30003 3.42318 7.31528 3.34694 7.3449 3.27585C7.37451 3.20477 7.41791 3.14025 7.47258 3.08602C7.52709 3.03196 7.59173 2.98918 7.6628 2.96015C7.73387 2.93112 7.80998 2.91641 7.88675 2.91685Z" fill="#322F35" />
                                                <path d="M3.80194 2.91685C3.95497 2.9175 4.10162 2.97825 4.21027 3.08602L7.71026 6.58602C7.76494 6.64025 7.80834 6.70477 7.83795 6.77585C7.86757 6.84694 7.88281 6.92318 7.88281 7.00019C7.88281 7.0772 7.86757 7.15344 7.83795 7.22452C7.80834 7.29561 7.76494 7.36013 7.71026 7.41435L4.21027 10.9144C4.10097 11.023 3.95313 11.084 3.79902 11.084C3.64491 11.084 3.49707 11.023 3.38777 10.9144C3.3331 10.8601 3.2897 10.7956 3.26008 10.7245C3.23047 10.6534 3.21522 10.5772 3.21522 10.5002C3.21522 10.4232 3.23047 10.3469 3.26008 10.2759C3.2897 10.2048 3.3331 10.1402 3.38777 10.086L6.4736 7.00019L3.38777 3.91435C3.3331 3.86012 3.2897 3.79561 3.26009 3.72452C3.23047 3.65344 3.21522 3.57719 3.21522 3.50019C3.21522 3.42318 3.23047 3.34694 3.26009 3.27585C3.2897 3.20477 3.3331 3.14025 3.38777 3.08602C3.44228 3.03196 3.50692 2.98918 3.57799 2.96015C3.64906 2.93112 3.72517 2.91641 3.80194 2.91685Z" fill="#322F35" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </LinkComponent>
                        </div>
                    ))}
                </div>
            </section>
            <section className='block md:hidden'>
                <div className="slide">
                    <Swiper
                        slidesPerView={1.3}
                        spaceBetween={16}
                        pagination={{
                            clickable: true,
                        }}
                        modules={[Pagination]}
                        className="mySwiper slide"
                    >
                        {dataPackage.map((link: any, index: number) => (
                            <SwiperSlide>
                                <div key={index} className="flex justify-center items-center">
                                    <LinkComponent href={link.href} skipLocaleHandling={false} locale={locale}>
                                        <div className="bg-white rounded-xl">
                                            <img src={locale === "en" ? link.imgSrc_EN : link.imgSrc} alt="Doctor examining a patient" className="w-full rounded-t-xl" />
                                            <div className='relative bg-[#EBFFF2] z-10 bottom-6 flex items-center justify-center gap-2 mx-4 px-2 py-1 rounded-3xl'>
                                                <div className='flex items-center bg-white p-1 rounded-lg gap-1'>
                                                    <svg className='w-3 h-3' width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM8.39762 5.28855C8.62936 5.05531 8.62936 4.67715 8.39762 4.4439C8.16588 4.21066 7.79016 4.21066 7.55842 4.4439L5.20879 6.80877L4.44158 6.03658C4.20984 5.80334 3.83412 5.80334 3.60238 6.03658C3.37064 6.26982 3.37064 6.64798 3.60238 6.88123L4.78919 8.07574C5.02093 8.30898 5.39665 8.30898 5.62839 8.07574L8.39762 5.28855Z" fill="#14813D" />
                                                    </svg>
                                                    <span className='text-[10px] text-[#156634] font-medium'>{locale === "en" ? "General Examination" : "Khám Tổng Quát"}</span>
                                                </div>
                                                <div className='flex items-center bg-white p-1 rounded-lg gap-1'>
                                                    <svg className='w-3 h-3' width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM8.39762 5.28855C8.62936 5.05531 8.62936 4.67715 8.39762 4.4439C8.16588 4.21066 7.79016 4.21066 7.55842 4.4439L5.20879 6.80877L4.44158 6.03658C4.20984 5.80334 3.83412 5.80334 3.60238 6.03658C3.37064 6.26982 3.37064 6.64798 3.60238 6.88123L4.78919 8.07574C5.02093 8.30898 5.39665 8.30898 5.62839 8.07574L8.39762 5.28855Z" fill="#14813D" />
                                                    </svg>
                                                    <span className='text-[10px] text-[#156634] font-medium'>{locale === "en" ? "Gene decoding" : "Phân Tích Gen"}</span>
                                                </div>
                                                <div className='flex items-center bg-white p-1 rounded-lg gap-1'>
                                                    <svg className='w-3 h-3' width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM8.39762 5.28855C8.62936 5.05531 8.62936 4.67715 8.39762 4.4439C8.16588 4.21066 7.79016 4.21066 7.55842 4.4439L5.20879 6.80877L4.44158 6.03658C4.20984 5.80334 3.83412 5.80334 3.60238 6.03658C3.37064 6.26982 3.37064 6.64798 3.60238 6.88123L4.78919 8.07574C5.02093 8.30898 5.39665 8.30898 5.62839 8.07574L8.39762 5.28855Z" fill="#14813D" />
                                                    </svg>
                                                    <span className='text-[10px] text-[#156634] font-medium'>{locale === "en" ? "Psychological Counseling" : "Tham Vấn Tâm Lý"}</span>
                                                </div>
                                            </div>
                                            <div className='px-4 relative bottom-2'>
                                                <p className='text-base font-semibold whitespace-pre-line uppercase'>{locale === "en" ? link.title_en : link.title}</p>
                                                <div className="flex items-center justify-end py-2 px-6 text-sm font-medium text-[#156634]">
                                                    {locale === "en" ? "Learn More" : "Xem chi tiết"}
                                                </div>
                                            </div>
                                        </div>
                                    </LinkComponent>
                                </div>
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
            </section>
            
        </>
    )
}


const benefitsData = [
    {
        id: 1,
        en: "Pioneer Healthcare model in Vietnam",
        vi: "Mô hình y tế tiên phong tại Việt Nam",
    },
    {
        id: 2,
        en: "Save time and money, avoid medication abuse and minimize invasive treatment",
        vi: "Tiết kiệm thời gian, giảm chi phí, tránh lạm dụng thuốc và các xét nghiệm không cần thiết",
    },
    {
        id: 3,
        en: "Direct online examination and health consultation with medical professionals",
        vi: "Thăm khám trực tiếp và tư vấn sức khỏe trực tuyến cùng chuyên viên y tế",
    },
    {
        id: 4,
        en: "Special offers on products and in-clinic services",
        vi: "Ưu đãi một số sản phẩm và dịch vụ khác tại phòng khám"
    }
]



const dataPackage = [
    {
        id: 889,
        price: "",
        href: "/services/cham-soc-toan-dien-cho-nam-gioi/",
        imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/nam_a3815b58ee.png",
        imgSrc_EN: "https://d3e4m6b6rxmux9.cloudfront.net/nam_a3815b58ee.png",
        title: "Chăm sóc sức khỏe nam giới toàn diện",
        title_en: "Comprehensive male healthcare",
        desc: "Khám sức khỏe định kỳ là phương pháp bảo vệ sức khỏe hiệu quả, giúp phát hiện và ngăn ngừa bệnh từ sớm. Đội ngũ ECHO MEDI sẽ thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra nâng cao nhằm phát hiện bệnh tiềm ẩn và các chỉ số bất thường của cơ thể. Từ đó, giúp ngăn ngừa bệnh, đưa ra chẩn đoán và điều trị sớm để duy trì và cải thiện tình trạng sức khỏe.",
        desc_en: "Regular health check-ups are an effective way to protect your health by detecting and preventing diseases early. ECHO MEDI conducts comprehensive examinations and basic tests to identify potential diseases and abnormal indicators in your body. This process helps with disease prevention and enables early diagnosis and treatment, ultimately maintaining and improving your health."
    },
    {
        id: 888,
        price: "",
        href: "/services/cham-soc-toan-dien-cho-phu-nu/",
        imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Cham_soc_suc_khoe_toan_dien_aac2f78d5c.png",
        imgSrc_EN: "https://d3e4m6b6rxmux9.cloudfront.net/Cham_soc_suc_khoe_toan_dien_aac2f78d5c.png",
        title: "Chăm sóc sức khỏe nữ giới toàn diện",
        title_en: "Comprehensive female healthcare",
        desc: "Khám sức khỏe định kỳ là phương pháp bảo vệ sức khỏe hiệu quả, giúp phát hiện và ngăn ngừa bệnh từ sớm. Đội ngũ ECHO MEDI sẽ thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra cơ bản nhằm phát hiện bệnh tiềm ẩn và các chỉ số bất thường của cơ thể. Từ đó, giúp ngăn ngừa bệnh, đưa ra chẩn đoán và điều trị sớm để duy trì và cải thiện tình trạng sức khỏe. ",
        desc_en: "Regular health check-ups are an effective way to protect your health by detecting and preventing diseases early. ECHO MEDI provides comprehensive examinations and basic tests to identify potential diseases and abnormal indicators in your body. This process aids in disease prevention and enables early diagnosis and treatment, ultimately maintaining and improving your health."
    },
    {
        id: 890,
        price: "982,000",
        href: "/services/cham-soc-toan-dien-cho-tre-em-3-12-tuoi",
        imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Kham_suc_khoe_tong_quat_danh_cho_Tre_Em_demo_d9b83f8027.png",
        imgSrc_EN: "https://d3e4m6b6rxmux9.cloudfront.net/Kham_suc_khoe_tong_quat_danh_cho_Tre_Em_demo_d9b83f8027.png",
        title: "Chăm sóc sức khỏe trẻ em toàn diện",
        title_en: "Comprehensive child healthcare",
        desc: "ECHO MEDI tập trung đánh giá sức khỏe tổng quát, tầm soát thiếu hụt dinh dưỡng/khoáng chất, tham vấn tâm lý và phân tích gen để khám phá các đặc điểm di truyền của trẻ em từ 3-12 tuổi. Quy trình chăm sóc này được tùy chỉnh theo nhu cầu và đặc điểm của riêng từng trẻ, giúp phụ huynh xây dựng được kế hoạch chăm sóc phù hợp với sự phát triển toàn diện của trẻ.",
        desc_en: "ECHO MEDI focuses on general health assessments, screening for nutritional and mineral deficiencies, psychological consultations, and genetic analysis to identify the genetic traits of children aged 3 to 12. Our care process is tailored to each child’s unique needs, enabling parents to create a personalized care plan that supports their child’s comprehensive development."
    },
]
