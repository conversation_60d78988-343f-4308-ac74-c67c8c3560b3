import React from "react";
import { useRouter } from 'next/router';
import LinkComponent from "../Link";
import Image from 'next/image'
import { shimmer, toBase64 } from "../../lib/ui";

const contact = () => {
  const router = useRouter();
  const locale = router.query.locale as string || 'vi';
  const getLogoUrl = (locale: String) => {
    if (locale == "vi") return "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Echo_Medi_Vn_d24b035bef.svg";
    return "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Echo_Medi_En_f8cece4129.svg";
  }
  return (
    <footer style={{
      backgroundColor: "#F0F0F0"
    }}
      aria-label="Site Footer">
      <div className="lg:pb-16 md:py-8 pb-9 px-4 sm:px-12 w-full ">
        <div className="grid grid-cols-1 sm:gap-5 gap-y-8 lg:grid-cols-4 gap-6">
          <div>
            <div className="flex text-black justify-start max-sm:mt-5 relative">
              <Image
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(256, 44))}`}
                width={256}
                height={44}
                alt="ECHO MEDI"
                src={getLogoUrl(locale)}
              />
            </div>
            <p className="text-sm text-black text-left m-auto sm:m-0 !leading-6"
            >
              {locale === "en" ? "ECHO MEDI is a comprehensive healthcare provider to serve you and your family. We are at the forefront of the healthcare industry in Vietnam in enhancing the ‘family doctor’ concept found in advanced countries worldwide." : "ECHO MEDI là hệ thống y tế toàn diện cho bạn và gia đình. Chúng tôi tiên phong tại Việt Nam trong việc nâng cấp mô hình “Bác sĩ gia đình” của các nước tiên tiến trên thế giới."}
            </p>
            <section className="hidden md:block">
              <ul className="mt-6 flex justify-center items-center gap-2 sm:justify-start md:gap-4">
                <li>
                  <a
                    href="https://www.facebook.com/ECHO-MEDI-104159875780641?gidzl=VhMH36yZUJrohfK8RJvuENoibGDxQYauCFp53YbcVMXXeP0FV6SkRM6bobTyD25jO_N5MZ4fezq3PY9vE0"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Facebook</span>
                    <svg
                      className="h-9 w-9"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.tiktok.com/@echomedi?is_from_webapp=1&sender_device=pc"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Tiktok</span>
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 30 30">
                      <path d="M24,4H6C4.895,4,4,4.895,4,6v18c0,1.105,0.895,2,2,2h18c1.105,0,2-0.895,2-2V6C26,4.895,25.104,4,24,4z M22.689,13.474 c-0.13,0.012-0.261,0.02-0.393,0.02c-1.495,0-2.809-0.768-3.574-1.931c0,3.049,0,6.519,0,6.577c0,2.685-2.177,4.861-4.861,4.861 C11.177,23,9,20.823,9,18.139c0-2.685,2.177-4.861,4.861-4.861c0.102,0,0.201,0.009,0.3,0.015v2.396c-0.1-0.012-0.197-0.03-0.3-0.03 c-1.37,0-2.481,1.111-2.481,2.481s1.11,2.481,2.481,2.481c1.371,0,2.581-1.08,2.581-2.45c0-0.055,0.024-11.17,0.024-11.17h2.289 c0.215,2.047,1.868,3.663,3.934,3.811V13.474z"></path>
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.instagram.com/echomedi_"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Instagram</span>
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 30 30">
                      <path d="M 9.9980469 3 C 6.1390469 3 3 6.1419531 3 10.001953 L 3 20.001953 C 3 23.860953 6.1419531 27 10.001953 27 L 20.001953 27 C 23.860953 27 27 23.858047 27 19.998047 L 27 9.9980469 C 27 6.1390469 23.858047 3 19.998047 3 L 9.9980469 3 z M 22 7 C 22.552 7 23 7.448 23 8 C 23 8.552 22.552 9 22 9 C 21.448 9 21 8.552 21 8 C 21 7.448 21.448 7 22 7 z M 15 9 C 18.309 9 21 11.691 21 15 C 21 18.309 18.309 21 15 21 C 11.691 21 9 18.309 9 15 C 9 11.691 11.691 9 15 9 z M 15 11 A 4 4 0 0 0 11 15 A 4 4 0 0 0 15 19 A 4 4 0 0 0 19 15 A 4 4 0 0 0 15 11 z"></path>
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.youtube.com/channel/UCJ7phE7dq0lbhdOzu5R0_nw/featured"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Youtube</span>
                    <svg
                      className="h-19 w-9"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                </li>
                {/* <li>
                  <a
                    href="https://www.linkedin.com/company/echo-medi"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">LinkedIn</span>
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 30 30">
                      <path d="M24,4H6C4.895,4,4,4.895,4,6v18c0,1.105,0.895,2,2,2h18c1.105,0,2-0.895,2-2V6C26,4.895,25.105,4,24,4z M10.954,22h-2.95 v-9.492h2.95V22z M9.449,11.151c-0.951,0-1.72-0.771-1.72-1.72c0-0.949,0.77-1.719,1.72-1.719c0.948,0,1.719,0.771,1.719,1.719 C11.168,10.38,10.397,11.151,9.449,11.151z M22.004,22h-2.948v-4.616c0-1.101-0.02-2.517-1.533-2.517 c-1.535,0-1.771,1.199-1.771,2.437V22h-2.948v-9.492h2.83v1.297h0.04c0.394-0.746,1.356-1.533,2.791-1.533 c2.987,0,3.539,1.966,3.539,4.522V22z"></path>
                    </svg>
                  </a>
                </li> */}
              </ul>
            </section>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-4 lg:col-span-3">
            <div className="text-left">
              <section className="block md:hidden">
                <h3 className="font-bold text-black">{locale === "en" ? "CLINIC HOURS" : "LỊCH LÀM VIỆC "}</h3>
                <nav aria-label="Footer Services Nav" className="mt-4 mb-4">
                  <ul className="space-y-2 text-sm">
                    <li>
                      {locale === "en" ? "Monday - Saturday:" : "Thứ hai - Thứ bảy:"} 8:00 - 20:00
                    </li>
                    <li>
                      {locale === "en" ? "Sunday: Closed" : "Chủ nhật: Nghỉ"}
                    </li>
                  </ul>
                </nav>
              </section>
              <h3 className="font-bold text-black">{locale === "en" ? " ADDRESS" : "ĐỊA CHỈ"}</h3>
              <ul className="my-2 space-y-4 text-sm mt-6">
                <li
                  className="flex flex-col items-start justify-center gap-1.5  sm:items-start"
                >
                  <p className="-mt-0.5 not-italic text-black">
                    {locale === "en" ? "No. 1026 Nguyen Van Linh Street, Sky Garden I (R1-1), Tan Hung Ward, Ho Chi Minh City" : "Số 1026 Nguyễn Văn Linh, khu phố Sky Garden I (R1-1), Phường Tân Hưng, TP Hồ Chí Minh"}
                    &ensp;
                  </p>
                  <a style={{
                    color: "#406D48"
                  }}
                    target="_blank"
                    href="https://www.google.com/maps/place/ECHO+MEDI+-+Healthcare+Evolved/@10.7292756,106.7035192,17z/data=!3m1!4b1!4m6!3m5!1s0x31752f1a90aeb161:0x6723f8e04c8e3433!8m2!3d10.7292756!4d106.7057079!16s%2Fg%2F11sb5r_5yt?hl=vi-VN"
                    className="text-xs !text-[#0077FF] font-bold hover:underline">{locale === "en" ? "View Maps" : "Xem bản đồ"}</a>
                </li>
                <li
                  className="flex flex-col  items-start justify-center gap-1.5 sm:items-start"
                >
                  <address className="-mt-0.5 not-italic text-black">
                    {locale === "en" ? "No. 46 Nguyen Thi Dinh Street, Binh Trung Ward, Ho Chi Minh City" : "46 Nguyễn Thị Định, Phường Bình Trưng, TP Hồ Chí Minh"}
                    &ensp;
                  </address>
                  <a style={{
                    color: "#406D48"
                  }}
                    target="_blank"
                    href="https://www.google.com/maps/place/ECHO+MEDI/@10.7916213,106.7512846,17z/data=!3m1!4b1!4m6!3m5!1s0x31752764abde92c7:0x5e0ef8d1067c35f!8m2!3d10.7916213!4d106.7534733!16s%2Fg%2F11sk4sppjf?hl=vi-VN"
                    className="text-xs font-bold !text-[#0077FF] hover:underline">{locale === "en" ? "View Maps" : "Xem bản đồ"}</a>
                </li>
                <li
                  className="flex flex-col  items-start justify-center gap-1.5 sm:justify-start sm:items-start"
                >
                  <address className="-mt-0.5 not-italic text-black text-left">
                    {locale === "en" ? "Unit 0102, Ground Floor, Canary Plaza Shopping Mall, No. 5 Binh Duong Boulevard, Binh Hoa Ward, Ho Chi Minh City" : "Căn 0102 tầng trệt TTTM Canary Plaza số 5 Đại lộ Bình Dương, Phường Bình Hoà, TP Hồ Chí Minh"}
                    &ensp;
                  </address>
                  <a style={{
                    color: "#406D48"
                  }}
                    href="https://www.google.com/maps/place/ECHO+MEDI+-+Healthcare+Evolved/@10.9286844,106.7094019,17z/data=!3m1!4b1!4m6!3m5!1s0x3174d729ae7d0e51:0xe53ac856559d1703!8m2!3d10.9286844!4d106.7119768!16s%2Fg%2F11stjtr2hy"
                    target="_blank"
                    className="text-xs font-bold !text-[#0077FF] hover:underline">{locale === "en" ? "View Maps" : "Xem bản đồ"}</a>
                </li>
                <div className="text-left block md:hidden">
                  <h3 className="font-bold text-black text-base">{locale === "en" ? " CONTACT" : "LIÊN HỆ"}</h3>
                  <ul className="mt-4 space-y-4 text-sm">
                    <li>

                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 shrink-0 text-black inline mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>

                      <span className="text-black"><EMAIL></span>
                    </li>
                    <li>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 shrink-0 text-black inline mr-2"
                        fill="#0077FF"
                        viewBox="0 0 24 24"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      <a href="tel:1900638408" className="!text-[#0077FF]">1900 638 408</a>
                    </li>
                    <li>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 shrink-0 text-black inline mr-2"
                        fill="#0077FF"
                        viewBox="0 0 24 24"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      <a href="tel:0768638408" className="!text-[#0077FF]">{locale === "en" ? "(84) 76 8638 408" : "0768 638 408"}</a>
                    </li>
                    <li>
                      <span className="text-black block mb-2">{locale === "en" ? "Customer service in Ho Chi Minh: " : "Chăm sóc khách hàng Hồ Chí Minh"}
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 shrink-0 text-black inline mr-2"
                        fill="#0077FF"
                        viewBox="0 0 24 24"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      <a href="tel:02873009801" className="!text-[#0077FF]">{locale === "en" ? "(84) 28 7300 9801" : "028 7300 9801"}</a>
                    </li>
                    <li>
                      <span className="text-black block mb-2">{locale === "en" ? "Customer service in Thu Duc: " : "Chăm sóc khách hàng Thủ Đức"}
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 shrink-0 text-black inline mr-2"
                        fill="#0077FF"
                        viewBox="0 0 24 24"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      <a href="tel:02873009802" className="!text-[#0077FF]">{locale === "en" ? "(84) 28 7300 9802" : "028 7300 9802"}</a>
                    </li>
                    <li>
                      <span className="text-black block mb-2">{locale === "en" ? "Customer service Binh Duong city: " : "Chăm sóc khách hàng Bình Dương"}
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 shrink-0 text-black inline mr-2"
                        fill="#0077FF"
                        viewBox="0 0 24 24"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      <a href="tel:02873009803" className="!text-[#0077FF]">{locale === "en" ? "(84) 28 7300 9803" : "028 7300 9803"}</a>
                    </li>
                  </ul>
                </div>
                <section className="hidden md:block">
                  <h3 className="font-bold text-black">{locale === "en" ? "CLINIC HOURS" : "LỊCH LÀM VIỆC "}</h3>
                  <nav aria-label="Footer Services Nav" className="mt-6">
                    <ul className="space-y-2 text-sm">
                      <li>

                        {locale === "en" ? "Monday - Saturday:" : "Thứ hai - Thứ bảy:"} 8:00 - 20:00
                      </li>
                      <li>
                      {locale === "en" ? "Sunday: Closed" : "Chủ nhật: Nghỉ"}
                      </li>
                    </ul>
                  </nav>
                </section>
              </ul>
            </div>
            <section className="block md:hidden">
              <h3 className="font-bold text-black text-base">{locale === "en" ? "KẾT NỐI VỚI CHÚNG TÔI" : "KẾT NỐI VỚI CHÚNG TÔI"}</h3>
              <ul className="mt-4 mb-4 flex items-center gap-6 justify-start md:gap-8">
                <li>
                  <a
                    href="https://www.facebook.com/ECHO-MEDI-104159875780641?gidzl=VhMH36yZUJrohfK8RJvuENoibGDxQYauCFp53YbcVMXXeP0FV6SkRM6bobTyD25jO_N5MZ4fezq3PY9vE0"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Facebook</span>
                    <svg
                      className="h-9 w-9"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.tiktok.com/@echomedi?is_from_webapp=1&sender_device=pc"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Tiktok</span>
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 30 30">
                      <path d="M24,4H6C4.895,4,4,4.895,4,6v18c0,1.105,0.895,2,2,2h18c1.105,0,2-0.895,2-2V6C26,4.895,25.104,4,24,4z M22.689,13.474 c-0.13,0.012-0.261,0.02-0.393,0.02c-1.495,0-2.809-0.768-3.574-1.931c0,3.049,0,6.519,0,6.577c0,2.685-2.177,4.861-4.861,4.861 C11.177,23,9,20.823,9,18.139c0-2.685,2.177-4.861,4.861-4.861c0.102,0,0.201,0.009,0.3,0.015v2.396c-0.1-0.012-0.197-0.03-0.3-0.03 c-1.37,0-2.481,1.111-2.481,2.481s1.11,2.481,2.481,2.481c1.371,0,2.581-1.08,2.581-2.45c0-0.055,0.024-11.17,0.024-11.17h2.289 c0.215,2.047,1.868,3.663,3.934,3.811V13.474z"></path>
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.instagram.com/echomedi_"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Instagram</span>
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 30 30">
                      <path d="M 9.9980469 3 C 6.1390469 3 3 6.1419531 3 10.001953 L 3 20.001953 C 3 23.860953 6.1419531 27 10.001953 27 L 20.001953 27 C 23.860953 27 27 23.858047 27 19.998047 L 27 9.9980469 C 27 6.1390469 23.858047 3 19.998047 3 L 9.9980469 3 z M 22 7 C 22.552 7 23 7.448 23 8 C 23 8.552 22.552 9 22 9 C 21.448 9 21 8.552 21 8 C 21 7.448 21.448 7 22 7 z M 15 9 C 18.309 9 21 11.691 21 15 C 21 18.309 18.309 21 15 21 C 11.691 21 9 18.309 9 15 C 9 11.691 11.691 9 15 9 z M 15 11 A 4 4 0 0 0 11 15 A 4 4 0 0 0 15 19 A 4 4 0 0 0 19 15 A 4 4 0 0 0 15 11 z"></path>
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.youtube.com/channel/UCJ7phE7dq0lbhdOzu5R0_nw/featured"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">Youtube</span>
                    <svg
                      className="h-19 w-9"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                </li>
                {/* <li>
                  <a
                    href="https://www.linkedin.com/company/echo-medi"
                    rel="noreferrer"
                    target="_blank"
                    className="text-black"
                  >
                    <span className="sr-only">LinkedIn</span>
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 30 30">
                      <path d="M24,4H6C4.895,4,4,4.895,4,6v18c0,1.105,0.895,2,2,2h18c1.105,0,2-0.895,2-2V6C26,4.895,25.105,4,24,4z M10.954,22h-2.95 v-9.492h2.95V22z M9.449,11.151c-0.951,0-1.72-0.771-1.72-1.72c0-0.949,0.77-1.719,1.72-1.719c0.948,0,1.719,0.771,1.719,1.719 C11.168,10.38,10.397,11.151,9.449,11.151z M22.004,22h-2.948v-4.616c0-1.101-0.02-2.517-1.533-2.517 c-1.535,0-1.771,1.199-1.771,2.437V22h-2.948v-9.492h2.83v1.297h0.04c0.394-0.746,1.356-1.533,2.791-1.533 c2.987,0,3.539,1.966,3.539,4.522V22z"></path>
                    </svg>
                  </a>
                </li> */}
              </ul>
            </section>
            <div className="text-left">
              <h3 className=" font-bold text-black">{locale === "en" ? "FAQs" : "Thông tin & Hỏi đáp"}</h3>
              <nav aria-label="Footer Services Nav" className="my-6">
                <ul className="space-y-4 text-sm">
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/articles_preventive_care"><p className="sm:m-0 m-auto">{locale == "vi" ? "Bài viết sức khoẻ" : "Healthcare Articles"}</p></LinkComponent>
                  </li>
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/articles_news"><p className="sm:m-0 m-auto"> {locale == "vi" ? "Tin tức, Hoạt động và Ưu đãi" : "News, Events & Special Offers"}</p></LinkComponent>
                  </li>
                  {/* <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/video_short"><p className="sm:m-0 m-auto">Videos</p></LinkComponent>
                  </li> */}
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/question"><p className="sm:m-0 m-auto">{locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}</p></LinkComponent>
                  </li>
                </ul>
              </nav>
              <h3 className=" font-bold text-black">{locale === "en" ? "POLICY" : "CHÍNH SÁCH"}</h3>
              <nav aria-label="Footer Services Nav" className="mt-6">
                <ul className="space-y-4 text-sm">
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/policy/chinh-sach-bao-mat"><p className="sm:m-0 m-auto">{locale === "en" ? "Privacy Policy" : "Chính sách bảo mật"}</p></LinkComponent>
                  </li>
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/policy/chinh-sach-thanh-toan"><p className="sm:m-0 m-auto">{locale === "en" ? "Payment Policy" : "Chính sách thanh toán"}</p></LinkComponent>
                  </li>
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/policy/chinh-sach-van-chuyen"><p className="sm:m-0 m-auto">{locale === "en" ? "Shipping Policy" : "Chính sách vận chuyển"}</p></LinkComponent>
                  </li>
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/policy/chinh-sach-doi-tra"><p className="sm:m-0 m-auto">{locale === "en" ? "Return Policy" : "Chính sách đổi trả"}</p></LinkComponent>
                  </li>
                  <li className="hover:underline flex justify-start">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/policy/dieu-khoan-va-chinh-sach-hoat-dong"><p className="sm:m-0 m-auto">{locale === "en" ? "Operating Policies" : "Điều khoản và chính sách hoạt động"}</p></LinkComponent>
                  </li>
                </ul>
              </nav>
            </div>
            <div className="text-center sm:text-left hidden md:block">
              <h3 className="font-bold text-black">{locale === "en" ? " CONTACT" : "LIÊN HỆ"}</h3>
              <ul className="mt-6 space-y-4 text-sm">
                <li>

                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 shrink-0 text-black inline mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>

                  <span className="text-black"><EMAIL></span>
                </li>
                <li>

                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 shrink-0 text-black inline mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>

                  <a href="tel:1900638408" className="text-black">1900 638 408</a>
                </li>
                <li>

                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 shrink-0 text-black inline mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  <a href="tel:0768638408" className="text-black">{locale === "en" ? "(84) 76 8638 408" : "0768 638 408"}</a>

                </li>
                <li>
                  <span className="text-black block mb-2">{locale === "en" ? "Customer service in Ho Chi Minh: " : "Chăm sóc khách hàng Hồ Chí Minh"}
                  </span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 shrink-0 text-black inline mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  <a href="tel:02873009801" className="text-black">{locale === "en" ? "(84) 28 7300 9801" : "028 7300 9801"}</a>
                </li>
                <li>
                  <span className="text-black block mb-2">{locale === "en" ? "Customer service in Thu Duc: " : "Chăm sóc khách hàng Thủ Đức"}
                  </span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 shrink-0 text-black inline mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  <a href="tel:02873009802" className="text-black">{locale === "en" ? "(84) 28 7300 9802" : "028 7300 9802"}</a>
                </li>
                <li>
                  <span className="text-black block mb-2">{locale === "en" ? "Customer service Binh Duong city: " : "Chăm sóc khách hàng Bình Dương"}
                  </span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 shrink-0 text-black inline mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  <a href="tel:02873009803" className="text-black">{locale === "en" ? "(84) 28 7300 9803" : "028 7300 9803"}</a>
                </li>
              </ul>
            </div>
            <div className="md:text-center flex flex-col items-start md:items-end">
              <h3 className="font-bold text-black mb-6 mt-2">{locale === "en" ? "DOWNLOAD APP" : "TẢI ỨNG DỤNG"}</h3>
              <Image alt="ECHO MEDI" width={150} height={150} loading="lazy" src="data:image/gif;base64,R0lGODdhfQB9AIAAAAAAAP///ywAAAAAfQB9AAAC/4SPqbvhDxOcgdlD4928e5s9Ulh9CFma6qqiY8gCbkzXyoyRMW73u34Crni+DeqoyYmCsKLS8UISo9LmM2WY9ibUKuXiJXKZGee1G14y0tZs0t0ujq9sKKg+h9vpb3kfn3fTB/Y3yBLoRfURqOiBaDhUKNXYwUiGZSS5V/M4ebkIqYdJqCbq04lEyWF55qg5evgqFipYWruZaau1VkWKy9pAC3yLuRvsyatLq0oMqhznexTNPH1nW813bZ26Xbz82e38K7TaGw5eKfzdyi6KJ+6tnYxLfd4uAyh/PK7fbF/+jF8+ePXmwbqHzdSld64CHtz3EF8/ZqgAEUQHkN7COv8FFWYrs9EhQ4MjPUJEE2dWvogrBbKURU4iyI9hopWU+VJkTJUDu90cVpHnl5BpzGSM55LiK6NMcyXFiFPpxKZmKkp9ipAqVatQuWbVytSryahd13FqmcjnUqJo27rtJxTl27loba6lSTcvR7UO5er9W5MvVneAjZGlaVfnVIMXGf/DCXRnzMZHE/pDLBjp0EiLx3Y8TBiuZGgNNTo+fZJtYpfDLLtWvQWm6NkzIf4061RzZNqbbe/92kK2ac+wSf4mzpm1Ot67ewI3EXT5cMjSLT4WS5356Ol1S2u+2ptY3Mxp0/UtSxrvXd9sKIN+b7g88tBFvYP3uzp8c+vma8P/Fx6YcSnNhR96yrVHHoC3DXieesiw5x9dBd4THYICRkhgcWNVWN+F+hXWEniGnbJdTm6JOJlR43m44IH+hVVifi3qlmJVMSZo4YbmaLVias/JRwOHEX2G3X4j2qdhbpZh51drSGLWn4nfQdUkbkvW6GOW/2mXXnCKTUdkdUDeZ+CUz5HJIpZbDrammZVRyVZ867WJ5ps/HneZjg3qCWaZMqpZ5JxMXidooQ9m1yeFO9JH455tCuWkoodCaqiLH3aZJ6KXYsggm0AeKSk3UDJap6ZCuvfpjahlWuqEYI3KKpyhShmpE3LydiWXidoKaHW5cqcmjJju9yutVsYGoqh8/25q7LAlFoaio1Xq2qhe0bI5LbCdgnitm+L1ui20ZQ7qoLYvghVoZ0rKOt+rdLIL3ZyfvZruruveqa6wnh67KrHu9itvLNLCm600SRbcba2aFkwptfUaHKWQqTo7qYK4NdwhqTjue2h+DD8rpqqMKtzjvSMHvCqJjpJs8cApI8uxvRKjrKXKMQ85rsgm+7HyxS1j+5jN3rLcc8cbhwcPuVrOrO5NSdOcKdMy4/m0y0v/jHO5SHundNRYV61svNuiCnLTABb7GsDh+vyllGGa3barO98K9Jk5x/2kxmqbm/Wyp+a9MKEOL/revxF/HSfhChuu98ltT2wv44Fr/fHYki5zjbjWJV+eGcLoVsps2o5HriLoXRsZ7OdFc8r3n/mWvnroQSf+uo2xnx4ypgUAADs=" className="" />
              <a href="https://apps.apple.com/vn/app/echo-medi/id6448538294" target="blank" className="cursor-pointer mt-2"><Image alt="ECHO MEDI" width={150} height={50} loading="lazy" src="https://upload.wikimedia.org/wikipedia/commons/thumb/9/91/Download_on_the_App_Store_RGB_blk.svg/1920px-Download_on_the_App_Store_RGB_blk.svg.png" className="mt-4" /></a>
              <a href="https://play.google.com/store/apps/details?id=com.echomedi.echomedi&hl=vi" target="blank" className="cursor-pointer"><Image alt="ECHO MEDI" width={150} height={50} loading="lazy" src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg" className="mt-2" /></a>
              <a href="https://appgallery.huawei.com/app/C109669075" target="blank" className="cursor-pointer"><Image alt="ECHO MEDI" width={150} height={50} loading="lazy" src="https://d3e4m6b6rxmux9.cloudfront.net/dc89f3f0f72d0a31db408a245d65_a12841c7a5.svg?updated_at=2023-12-16T08:02:22.421Z" className="mt-2" /></a>
              <a href="http://online.gov.vn/Home/WebDetails/121433" target="blank" className="cursor-pointer"><Image alt="ECHO MEDI" width={150} height={50} loading="lazy" src="/BCT/Logo-BCT.webp" className="mt-2" /></a>
            </div>
          </div>
        </div>

        <div className="mt-12 mb-12 border-t border-black-100 pt-6">
          <div className="sm:flex sm:justify-between sm:text-left">
            <p className="text-center mt-2 font-bold text-base text-black sm:order-first sm:mt-0">
              {locale === "en" ? "ECHO MEDI LIMITED LIABILITY COMPANY" : "CÔNG TY TNHH ECHO MEDI"}
            </p>
          </div>
          <div
            className="sm:flex sm:justify-between sm:text-left">
            <p className="mt-4 text-xs text-black sm:order-first sm:mt-0">
              {locale === "en" ? "Business registration number: 0317439798, first registered on August 19th, 2022, and the first amendment was registered on October 31st, 2022, issued by the HCMC D.P.I" : "Mã số ĐKKD: 0317439798 đăng ký lần đầu ngày 19/08/2022, đăng ký thay đổi lần thứ 1 ngày 31/10/2022, được cấp bởi Sở KHĐT TP.HCM"}
            </p>
          </div>
          <div
            className="sm:flex sm:justify-between sm:text-left">
            <p className="mt-4 text-xs text-black sm:order-first sm:mt-0">
              {locale === "en" ? "" : "Mã số CNĐĐKKD: 12175/DKKDD-HCM được cấp bởi Sở Y tế TP.HCM"}
            </p>
          </div>
          <div
            className="sm:flex sm:justify-between sm:text-left">
            <p className="mt-4 text-xs text-black sm:order-first sm:mt-0">
              {locale === "en" ? "" : "Mã số ĐKHĐ: 09269/HCM-GPHĐ được cấp bởi Sở Y tế TP.HCM"}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default contact;
