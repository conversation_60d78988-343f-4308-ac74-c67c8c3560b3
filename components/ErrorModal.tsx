"use client"

import Link from "next/link"
import { useEffect } from "react"

interface ErrorModalProps {
    message: string
    isOpen: boolean
    onClose: () => void
}

const ErrorModal = ({ message, isOpen, onClose }: ErrorModalProps) => {
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = "hidden"
        } else {
            document.body.style.overflow = "auto"
        }

        return () => {
            document.body.style.overflow = "auto"
        }
    }, [isOpen])

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
            <div className="bg-white rounded-xl shadow-lg max-w-md w-full px-6 py-12 relative animate-in fade-in zoom-in duration-500">
                <div className="flex flex-col items-center">
                    <div className="!w-32 !h-32 mx-auto mb-4 rounded-full overflow-hidden bg-gray-100">
                        <img
                            src="https://api.echomedi.com/uploads/error_e325de0aae.png"
                            className="!w-32 !h-32 object-cover"
                        />
                    </div>
                    <p className="text-center pb-4">{message}</p>
                    <div className="flex items-center justify-between gap-4">
                        <button
                            onClick={onClose}
                            className=" bg-[#156634] hover:bg-[#14813d] text-white py-2.5 px-10 rounded-full transition-colors"
                        >
                            Đóng
                        </button>
                        {message === "Vui lòng kiểm tra lại số điện thoại hoặc đảm bảo bạn đã đăng ký tham gia chương trình." && (
                            <>
                                <Link href="/vi/event-phu-my-hung-add" className="flex items-center justify-center gap-2 bg-[#156634] hover:bg-[#14813d] text-white py-2.5 px-4 rounded-full transition-colors">Đăng ký<img src="https://api.echomedi.com/uploads/Right_arrow_c6b76d157d.svg" alt="icon" /></Link>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ErrorModal
