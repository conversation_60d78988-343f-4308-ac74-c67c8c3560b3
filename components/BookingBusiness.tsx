import React, { useState } from 'react';
import dayjs from 'dayjs';
import toast from 'react-hot-toast';
import axios from 'axios';
import Image from 'next/image';
import { shimmer, toBase64 } from '../lib/ui';

interface ModalBookingProps {
    onClose: () => void;
    locale: string;
}

const BookingBusiness: React.FC<ModalBookingProps> = ({ onClose, locale }) => {
    const [formState, setFormState] = useState({
        name: '',
        email: '',
        phone_number: '',
        address: '',
        company: '',
        gender: 'male',
        message: 'Website: <PERSON><PERSON><PERSON><PERSON> hàng cần tư vấn gói doanh nghiệp',
        branch: 'q7',
        bd: null as Date | null,
        timeSlot: '',
        phone_number_warning_msg: '',
    });

    const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = event.target;
        setFormState(prevState => ({ ...prevState, [name]: value }));
    };

    const handleBooking = async (event: any) => {
        event.preventDefault();
        if (formState.phone_number === '' || !validatePhone(formState.phone_number)) {
            setFormState(prevState => ({
                ...prevState,
                phone_number_warning_msg: locale === 'vi'
                    ? 'Yêu cầu nhập số điện thoại hợp lệ'
                    : 'Please enter your phone number',
            }));
            return;
        }

        const payload = {
            data: {
                createNewPatient: true,
                full_name: formState.name,
                contactFullName: formState.name,
                gender: formState.gender,
                email: formState.email,
                contactEmail: formState.email,
                phone: formState.phone_number,
                contactPhoneNumber: formState.phone_number,
                message: formState.message,
                birthday: formState.bd ? dayjs(formState.bd).toISOString() : null,
                address: {
                    address: formState.address,
                },
                contactAddress: formState.address,
                branch: formState.branch,
                bookingDate: formState.timeSlot,
                note: formState.message,
            },
        };

        try {
            const response = await axios.post('https://api.echomedi.com/api/bookings/createBookingFromWeb', payload);
            toast.success(locale === 'vi' ? 'Liên hệ tư vấn thành công' : 'Consultation request successful');
            window.location.href = `/booking_detail/?code=${response.data.booking.id}`;
            setFormState({
                name: '',
                email: '',
                phone_number: '',
                address: '',
                company: '',
                gender: 'male',
                message: 'Website: Khách hàng cần tư vấn gói doanh nghiệp',
                branch: 'q7',
                bd: null,
                timeSlot: '',
                phone_number_warning_msg: '',
            });
        } catch (error) {
            toast.error(locale === 'vi' ? 'Liên hệ tư vấn không thành công' : 'Consultation request failed');
        }
    };

    function validatePhone(phone: string) {
        return /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
    }
    const getLogoUrl = (locale: String) => {
        if (locale == "vi") return "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Echo_Medi_Vn_d24b035bef.svg";
        return "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Echo_Medi_En_f8cece4129.svg";
    }

    return (
        <>
            <section className='m-auto justify-center items-center flex overflow-hidden fixed inset-0 z-[1001] outline-none focus:outline-none transition-all duration-300 w-[100%] top-0 bg-slate-50 bg-opacity-50'>
            <div className="flex items-center justify-center max-w-5xl px-4 md:mx-auto">
                <div className="grid md:grid-cols-2 items-center gap-8 bg-[#FAFFFC] rounded-[40px]">
                    <section className='hidden md:block'>
                        <div className="max-md:order-1 px-8 py-6 space-y-2">
                            <Image
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(256, 44))}`}
                                width={161}
                                height={51}
                                alt="ECHO MEDI"
                                src={getLogoUrl(locale)}
                            />
                            <Image
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(256, 201))}`}
                                width={369}
                                height={201}
                                alt="ECHO MEDI"
                                src="https://d3e4m6b6rxmux9.cloudfront.net/Booking_Coporate_40c18f01ec.png"
                            />
                            <h3 className="font-bold text-black">{locale === "en" ? "CLINIC HOURS" : "LỊCH LÀM VIỆC "}</h3>
                            <ul className=" space-y-2 text-sm">
                                <li
                                    className="flex flex-col items-start justify-center gap-1.5  sm:items-start"
                                >
                                    <p className="-mt-0.5 not-italic text-black">
                                        {locale === "en" ? "Monday - Saturday:" : "Thứ hai - Thứ bảy:"} 7:00 - 21:00
                                        &ensp;
                                    </p>

                                </li>
                                <li
                                    className="flex flex-col  items-start justify-center gap-1.5 sm:items-start"
                                >
                                    <address className="-mt-0.5 not-italic text-black">
                                        {locale === "en" ? "Sunday:" : "Chủ nhật:"} 7:00 - 15:00
                                        &ensp;
                                    </address>

                                </li>
                            </ul>
                            <h3 className="font-bold text-black">{locale === "en" ? " ADDRESS" : "ĐỊA CHỈ"}</h3>
                            <ul className="space-y-2 text-sm">
                                <li
                                    className="flex flex-col items-start justify-center gap-1.5  sm:items-start"
                                >
                                    <address className="-mt-0.5 not-italic text-black text-left">
                                        <span className='font-bold'>{locale === "en" ? "District 7:" : "Quận 7:"}</span> {locale === "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7, HCMC" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7, TP.HCM"}
                                        &ensp;
                                    </address>

                                </li>
                                <li
                                    className="flex flex-col  items-start justify-center gap-1.5 sm:items-start"
                                >
                                    <address className="-mt-0.5 not-italic text-black text-left">
                                        <span className='font-bold'>{locale === "en" ? "Thu Duc City:" : "TP. Thủ Đức:"}</span> {locale === "en" ? "46 Nguyen Thi Dinh, An Phu Ward, Thu Duc City, HCMC" : "46 Nguyễn Thị Định, P.An Phú, TP.Thủ Đức, TP.HCM"}
                                        &ensp;
                                    </address>

                                </li>
                                <li
                                    className="flex flex-col  items-start justify-center gap-1.5 sm:justify-start sm:items-start"
                                >
                                    <address className="-mt-0.5 not-italic text-black text-left whitespace-pre-line">
                                        <span className='font-bold'>Bình Dương:</span> {locale === "en" ? "Canary Plaza, #0102, 5 Binh Duong Highway, Binh Hoa Ward, Thuan An City" : "Canary Plaza, Căn 0102, Số 5, Đại lộ Bình Dương,\n P. Bình Hoà, TP. Thuận An"}
                                        &ensp;
                                    </address>
                                </li>
                            </ul>
                        </div>
                    </section>
                    <div className="flex items-center px-8 bg-white md:rounded-[40px] rounded-3xl my-4 md:my-0 h-full border border-[#156634]">
                        <form className="max-w-lg w-full mx-auto relative">
                            <div onClick={onClose} className='absolute -right-1 md:-top-4'>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20px" height="20px" fill="black">    <path d="M 7 4 C 6.744125 4 6.4879687 4.0974687 6.2929688 4.2929688 L 4.2929688 6.2929688 C 3.9019687 6.6839688 3.9019687 7.3170313 4.2929688 7.7070312 L 11.585938 15 L 4.2929688 22.292969 C 3.9019687 22.683969 3.9019687 23.317031 4.2929688 23.707031 L 6.2929688 25.707031 C 6.6839688 26.098031 7.3170313 26.098031 7.7070312 25.707031 L 15 18.414062 L 22.292969 25.707031 C 22.682969 26.098031 23.317031 26.098031 23.707031 25.707031 L 25.707031 23.707031 C 26.098031 23.316031 26.098031 22.682969 25.707031 22.292969 L 18.414062 15 L 25.707031 7.7070312 C 26.098031 7.3170312 26.098031 6.6829688 25.707031 6.2929688 L 23.707031 4.2929688 C 23.316031 3.9019687 22.682969 3.9019687 22.292969 4.2929688 L 15 11.585938 L 7.7070312 4.2929688 C 7.5115312 4.0974687 7.255875 4 7 4 z" /></svg>
                            </div>
                            <h3 className="text-xs md:text-xl ">{locale == "en" ? "Contact us for consultation" : "Liên hệ tư vấn:"}</h3>
                            <h2 className="md:text-[28px] text-lg font-bold text-[#156634] mb-4 mt-1">{locale == "en" ? "Corporate Partnership" : "Khách Hàng Doanh Nghiệp"}</h2>
                            <div className="flex flex-col justify-center mt-2">
                                <button
                                    className="m-auto w-full rounded-full font-normal bg-[#156634] border border-[#156634] flex items-center justify-center h-10">
                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_8765_9887)">
                                            <path d="M7.67126 2.70532C8.13095 2.79501 8.55342 3.01983 8.8846 3.35101C9.21578 3.68219 9.4406 4.10467 9.53029 4.56436M7.67126 0.822754C8.62632 0.928854 9.51692 1.35654 10.1968 2.0356C10.8767 2.71466 11.3056 3.60472 11.4129 4.55965M10.9422 8.31538V9.72731C10.9428 9.85838 10.9159 9.98812 10.8634 10.1082C10.8109 10.2283 10.7339 10.3361 10.6373 10.4247C10.5407 10.5133 10.4267 10.5808 10.3025 10.6228C10.1783 10.6648 10.0468 10.6804 9.91622 10.6686C8.46797 10.5112 7.07683 10.0163 5.85458 9.22372C4.71742 8.50112 3.75332 7.53702 3.03072 6.39986C2.23533 5.17205 1.74034 3.77415 1.58585 2.3194C1.57409 2.18925 1.58956 2.05808 1.63127 1.93423C1.67298 1.81039 1.74002 1.69659 1.82812 1.60008C1.91622 1.50356 2.02346 1.42645 2.14299 1.37365C2.26253 1.32085 2.39175 1.29352 2.52243 1.2934H3.93436C4.16276 1.29115 4.38419 1.37203 4.55737 1.52097C4.73056 1.6699 4.84367 1.87673 4.87564 2.1029C4.93523 2.55475 5.04575 2.9984 5.20509 3.42541C5.26841 3.59386 5.28212 3.77694 5.24458 3.95294C5.20704 4.12894 5.11984 4.2905 4.9933 4.41846L4.39559 5.01618C5.06557 6.19445 6.04116 7.17004 7.21944 7.84003L7.81715 7.24231C7.94512 7.11578 8.10667 7.02857 8.28268 6.99104C8.45868 6.9535 8.64175 6.9672 8.81021 7.03052C9.23721 7.18986 9.68087 7.30038 10.1327 7.35997C10.3613 7.39223 10.5701 7.50738 10.7194 7.68354C10.8686 7.85969 10.9479 8.08456 10.9422 8.31538Z" stroke="white" stroke-width="0.941284" stroke-linecap="round" stroke-linejoin="round" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_8765_9887">
                                                <rect width="11.2954" height="11.2954" fill="white" transform="translate(0.587891 0.352051)" />
                                            </clipPath>
                                        </defs>
                                    </svg>

                                    <p className="mx-2 text-sm text-white">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                                </button>
                                <div className="w-[270px] mx-auto flex mt-4 text-xs items-center text-gray-500">
                                    <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                                    {locale == "en" ? "Or" : "Hoặc"}
                                    <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                                </div>
                                <p className='text-sm my-4 text-center'>{locale == "en" ? "Simply provide the necessary information, and we will reach out to you to provide detailed advice" : "Chỉ cần điền thông tin, chúng tôi sẽ liên hệ bạn để tư vấn chi tiết"}</p>
                                <section>
                                    <div className="w-full max-w-7xl p-4 md:p-5 lg:p-5 mx-auto bg-[#FAFAFA] rounded-3xl">
                                        <div className="w-full flex-col justify-start items-start gap-4 inline-flex">
                                            <div className="w-full flex-col justify-start items-start gap-4 flex">
                                                <div className="w-full flex-col justify-start items-start gap-4 flex">
                                                    <div className="w-full justify-start items-start flex sm:flex-row flex-col">
                                                        <div className="w-full flex-col justify-start items-start flex">
                                                            <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Your name" : "Tên của quý khách"}:
                                                            </label>
                                                            <input type="text" id="name" name="name" value={formState.name} onChange={handleChange}
                                                                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-lg text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                                                placeholder={locale == "en" ? "Fill in your information" : "Nhập thông tin"}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="w-full justify-start items-start flex sm:flex-row flex-col">
                                                        <div className="w-full flex-col justify-start items-start flex">
                                                            <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Your Corporate" : "Tên công ty"}:
                                                            </label>
                                                            <input type="text" id="company" name="company" value={formState.company} onChange={handleChange}
                                                                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-lg text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                                                placeholder={locale == "en" ? "Fill in your information" : "Nhập thông tin"}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="w-full justify-start items-start flex sm:flex-row flex-col">
                                                        <div className="w-full flex-col justify-start items-start flex">
                                                            <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                                                                    <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                                                                </svg>
                                                            </label>
                                                            <input type="text" id="phone_number" name="phone_number" value={formState.phone_number} onChange={handleChange}
                                                                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-lg focus:outline-none focus:border-[#156634] text-xs lg:text-sm h-10"
                                                                placeholder={locale == "en" ? "Fill in your information" : "Nhập thông tin"}
                                                                required />
                                                            {formState.phone_number_warning_msg && (
                                                                <p className="text-red-500 text-sm mt-1">{formState.phone_number_warning_msg}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='w-full'>
                                                <div className='mt-auto flex items-center justify-center md:justify-end gap-12'>
                                                    <button onClick={handleBooking} className="border border-[#156634] text-[#156634] rounded-3xl py-2 px-5 text-sm font-semibold">
                                                        {locale == "en" ? "Get consultation" : "Nhận tư vấn"}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            </section>
        </>
    );
};

export default BookingBusiness;
