import React from "react";
import { useRouter } from 'next/router';
import Image from 'next/image';
import LinkComponent from "../Link";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
export const blogNews = [
    {
        id: 1,
        date: "16 tháng 4, 2025",
        date_en: "April 16, 2025",
        title: "INSMART và ECHO MEDI chính thức thiết lập quan hệ đối tác chiến lược nhằm chuyển đổi cách tiếp cận dịch vụ chăm sóc sức khỏe tại Việt Nam",
        title_en: "INSMART and ECHO MEDI Officially Establish Strategic Partnership to Transform Healthcare Service Delivery in Vietnam",
        linkUrl: "/hoat_dong/insmart-va-echo-medi-chinh-thuc-thiet-lap-quan-he-doi-tac-chien-luoc-nham-chuyen-doi-cach-tiep-can-dich-vu-cham-soc-suc-khoe-tai-viet-nam",
        imageUrl: "https://api.echomedi.com/uploads/z6509968271687_5b8e12bc9986ef6c62c3afdf0e148726_b1d43911df.jpg"
    },
    {
        id: 2,
        date: "11 tháng 4, 2025",
        date_en: "April 4, 2025",
        title: "MoMo nâng tầm hệ sinh thái tài chính số thông qua hợp tác ECHO MEDI",
        title_en: "MoMo elevates its digital financial ecosystem through a partnership with ECHO MEDI",
        linkUrl: "/hoat_dong//mo-mo-nang-tam-he-sinh-thai-tai-chinh-so-thong-qua-hop-tac-cung-echo-medi",
        imageUrl: "https://api.echomedi.com/uploads/Momo_Echo_Medi_34fd42d204.jpg"
    },
    {
        id: 3,
        date: "16 tháng 7, 2024",
        date_en: "July 16, 2024",
        title: "Miễn Phí Khám Sức Khỏe Và Test Tâm Lý Tại Hùng Vương Plaza",
        title_en: "Free Health Checkup and Psychological Test at Hung Vuong Plaza",
        linkUrl: "/hoat_dong/mien-phi-kham-suc-khoe-va-test-tam-ly-tai-hung-vuong-plaza/",
        imageUrl: "https://api.echomedi.com/uploads/IMG_20240716_WA_0002_e7772eface.jpg"
    },
    {
        id: 3,
        date: "4 tháng 6, 2024",
        date_en: "June 4, 2024",
        title: "ECHO MEDI Và Ngày Hội Chơi Để Học Tại Phú Mỹ Hưng",
        title_en: "ECHO MEDI and Play to Learn Festival in Phu My Hung",
        linkUrl: "/hoat_dong/echo-medi-va-ngay-hoi-choi-de-hoc/",
        imageUrl: "https://api.echomedi.com/uploads/doi_ngu_y_te_1d97787546.jpg"
    },
    // {
    //     id: 4,
    //     date: "3 tháng 5, 2024",
    //     date_en: "May 3, 2024",
    //     title: "Tọa Đàm Quản Lý Căng Thẳng Tại Nơi Làm Việc Cùng Công Ty AA",
    //     title_en: "Managing Stress in the Workplace health talk at AA Corporation",
    //     linkUrl: "/hoat_dong/toa-dam-suc-khoe-quan-ly-cang-thang-tai-noi-lam-viec/",
    //     imageUrl: "https://api.echomedi.com/uploads/11111_8e90dabb12.png"
    // },
    // {
    //     id: 4,
    //     date: "16 tháng 5, 2024",
    //     date_en: "May 16, 2024",
    //     title: "Chăm Sóc Sức Khỏe Đặc Biệt Dành Cho Cư Dân C-SkyView",
    //     title_en: "Exclusive Healthcare Program for C-Sky View Residents",
    //     linkUrl: "/hoat_dong/chuong-trinh-cham-soc-suc-khoe-dac-biet-danh-cho-cu-dan-chung-cu/",
    //     imageUrl: "https://api.echomedi.com/uploads/1_a266bac7b8.jpg"
    // }
];
const BlogMainSession = () => {
    const router = useRouter();
    const locale = router.query.locale as string || 'vi';
    

    return (
        <>
            <div className="mx-auto">
                <h2 className="text-center font-bold md:text-[28px] text-2xl py-6 uppercase text-[#156634]">{locale == "en" ? "News and Activities" : "TIN TỨC VÀ HOẠT ĐỘNG"}</h2>
                <section>
                    <div className="flex justify-center flex-wrap md:flex-wrap lg:flex-nowrap lg:flex-row lg:justify-between lg:gap-x-6">
                        {blogNews.map(blog => (
                            <div key={blog.id} className="group w-full max-lg:max-w-xl lg:w-1/3 bg-white rounded-xl">
                                <LinkComponent href={blog.linkUrl} skipLocaleHandling={false} locale={locale}>
                                    <div className="h-[180px] w-full  relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                                        <Image loading='lazy' width={200} height={180} className="w-full object-cover rounded-t-[12px] transition-opacity group-hover:opacity-30" alt="Image Tọa Đàm Sức Khỏe" src={blog.imageUrl} />
                                        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                                            <button className="px-4 py-2 text-base flex items-center gap-1 text-white transition-all duration-5000 ease-in-out transform translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-100">
                                                {locale === "en" ? "Learn More" : "Xem chi tiết"}
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M9.01343 3.62499C9.18832 3.62568 9.35592 3.69078 9.48009 3.80624L12.5401 6.67499C12.9146 7.02656 13.125 7.50312 13.125 7.99999C13.125 8.49687 12.9146 8.97343 12.5401 9.32499L9.48009 12.1937C9.35518 12.3101 9.18622 12.3755 9.01009 12.3755C8.83397 12.3755 8.665 12.3101 8.54009 12.1937C8.47761 12.1356 8.42801 12.0665 8.39417 11.9904C8.36032 11.9142 8.34289 11.8325 8.34289 11.75C8.34289 11.6675 8.36032 11.5858 8.39417 11.5096C8.42801 11.4335 8.47761 11.3643 8.54009 11.3062L11.6001 8.44374C11.6626 8.38564 11.7122 8.31651 11.746 8.24035C11.7799 8.16419 11.7973 8.0825 11.7973 7.99999C11.7973 7.91748 11.7799 7.83579 11.746 7.75963C11.7122 7.68347 11.6626 7.61434 11.6001 7.55624L8.54009 4.69374C8.47761 4.63564 8.42801 4.56651 8.39417 4.49035C8.36032 4.41419 8.3429 4.3325 8.3429 4.24999C8.3429 4.16748 8.36032 4.08579 8.39417 4.00963C8.42801 3.93347 8.47761 3.86434 8.54009 3.80624C8.60239 3.74832 8.67626 3.70249 8.75749 3.67138C8.83871 3.64028 8.92569 3.62452 9.01343 3.62499Z" fill="white" />
                                                    <path d="M4.34689 3.62499C4.52178 3.62568 4.68938 3.69078 4.81355 3.80624L8.81354 7.55624C8.87603 7.61434 8.92563 7.68347 8.95947 7.75963C8.99332 7.83579 9.01074 7.91748 9.01074 7.99999C9.01074 8.0825 8.99332 8.16419 8.95947 8.24035C8.92563 8.31651 8.87603 8.38564 8.81354 8.44374L4.81355 12.1937C4.68864 12.3101 4.51967 12.3755 4.34355 12.3755C4.16743 12.3755 3.99846 12.3101 3.87355 12.1937C3.81107 12.1356 3.76147 12.0665 3.72762 11.9904C3.69378 11.9142 3.67635 11.8325 3.67635 11.75C3.67635 11.6675 3.69378 11.5858 3.72762 11.5096C3.76147 11.4335 3.81107 11.3643 3.87355 11.3062L7.40021 7.99999L3.87355 4.69374C3.81107 4.63564 3.76147 4.56651 3.72762 4.49035C3.69378 4.41419 3.67635 4.3325 3.67635 4.24999C3.67635 4.16748 3.69378 4.08579 3.72762 4.00963C3.76147 3.93347 3.81107 3.86434 3.87355 3.80624C3.93585 3.74832 4.00972 3.70249 4.09095 3.67138C4.17217 3.64028 4.25915 3.62452 4.34689 3.62499Z" fill="white" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div className="p-3">
                                        <span className="font-medium text-xs text-[#8E8E8E] mt-3">{locale == "en" ? blog.date_en : blog.date}</span>
                                        <h4 className="text-base font-medium mb-2 whitespace-pre-line text-left line-clamp-2">{locale == "en" ? blog.title_en : blog.title}</h4>
                                    </div>
                                </LinkComponent>
                            </div>
                        ))}
                    </div>
                    <div className="flex justify-center mt-8">
                        <div className="py-2 text-center inline text-base border border-[#14813d] hover:bg-[#14813d] hover:text-white rounded-full text-[#156634] px-5">
                            <LinkComponent
                                href={"/articles_news"}
                                locale={locale}
                                skipLocaleHandling={false}
                            > {locale === "vi" ? "Xem tất cả" : "Load all"}</LinkComponent>
                        </div>
                    </div>
                </section>
            </div>
        </>
    );
}

export default BlogMainSession;
