import React from "react";
import { useRouter } from 'next/router';
import Image from 'next/image';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper';
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import LinkComponent from "../Link";
export const blogsNewsPaper = [
    {
        id: 1,
        title: "Chương trình khám sức khỏe miễn phí tại ECHO MEDI",
        title_en: "Free Health Check-up at ECHO MEDI clinics",
        linkUrl: "https://1thegioi.vn/chuong-trinh-kham-suc-khoe-mien-phi-tai-echo-medi-co-hoi-de-nguoi-dan-tiep-can-mo-hinh-cham-soc-suc-khoe-toan-dien-219700.html",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/IMG_20240716_WA_0004_dbf05dba95.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/mot_the_gioi_d04d2599f5.svg",
    },
    {
        id: 2,
        title: "Cơ hội để người dân tiếp cận mô hình chăm sóc sức khỏe toàn diện",
        title_en: "Opportunity for people to access a comprehensive healthcare model",
        linkUrl: "https://ngaymoionline.com.vn/co-hoi-de-nguoi-dan-tiep-can-mo-hinh-cham-soc-suc-khoe-toan-dien-53749.html",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/hinh_1_chuong_trinh_kham_suc_khoe_mien_phi_tai_echo_medi20240710161929_56fc2e4074.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/ngay_moi_24613fbfe3.svg",
    },
    {
        id: 3,
        title: "Hệ thống Phòng khám và Nhà thuốc ECHO MEDI ưu đãi hấp dẫn",
        title_en: "Attractive offers from ECHO MEDI",
        linkUrl: "https://phumyhungngaynay.com/tin-tuc/he-thong-phong-kham-va-nha-thuoc-echo-medi-uu-dai-hap-dan-trong-thang-3",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/ECHO_MEDI_Q_7_768x576_0a80dcb64f.jpeg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/phu_my_hung_72f8129ba4.svg",
    },
    {
        id: 4,
        title: "Ưu đãi gói khám sức khỏe tổng quát",
        title_en: "Discount on general health check-up packages",
        linkUrl: "https://hungvuongplaza.com.vn/%f0%9f%8c%9f-echo-medi-uu-dai-goi-kham-suc-khoe-tong-quat",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/ECHO_MEDI_Q_7_768x576_3c38a518cb.jpeg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/hung_vuong_2fa68e10c4.svg",
    },
    {
        id: 5,
        title: "Chăm sóc sức khỏe toàn diện để phòng ngừa bệnh tật",
        title_en: "Comprehensive healthcare for disease prevention",
        linkUrl: "https://afamily.vn/chu-dong-cham-soc-suc-khoe-toan-dien-la-cach-tot-nhat-de-phong-ngua-benh-tat-20241010110530772.chn",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Cham_soc_suc_khoe_toan_dien_de_phong_ngua_benh_tat_952f01a6f3.webp",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/logo_afamily_3_e94e9cdbd1.jpg",
    },
    {
        id: 6,
        title: "MoMo hợp tác cùng ECHO MEDI, mở rộng tiện ích chăm sóc sức khỏe cho người dùng Việt",
        title_en: "MoMo partners with ECHO MEDI to expand healthcare services for Vietnamese users",
        linkUrl: "https://afamily.vn/momo-hop-tac-cung-echo-medi-mo-rong-tien-ich-cham-soc-suc-khoe-cho-nguoi-dung-viet-20250410114032028.chn",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Momo_Echo_Medi_34fd42d204.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/logo_afamily_3_e94e9cdbd1.jpg",
    },
    {
        id: 7,
        title: "MoMo nâng tầm hệ sinh thái tài chính số thông qua hợp tác ECHO MEDI",
        title_en: "MoMo Elevates Digital Finance Ecosystem, Partnering with Echo Medi to Enhance Healthcare Solutions to Millions in Vietnam",
        linkUrl: "https://kinhtemoitruong.vn/momo-nang-tam-he-sinh-thai-tai-chinh-so-thong-qua-hop-tac-echo-medi-97790.html",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Momo_Echo_Medi_34fd42d204.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/logo_2_33f1cbaf5a.png",
    },
    {
        id: 8,
        title: "MoMo nâng tầm hệ sinh thái tài chính số thông qua hợp tác cùng ECHO MEDI",
        title_en: "MoMo Elevates Digital Finance Ecosystem, Partnering with Echo Medi to Enhance Healthcare Solutions to Millions in Vietnam",
        linkUrl: "https://nhipcaudautu.vn/quang-ba-san-pham-dich-vu/momo-nang-tam-he-sinh-thai-tai-chinh-so-thong-qua-hop-tac-cung-echo-medi-3362107",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Momo_Echo_Medi_34fd42d204.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/logo_1_246b670dfd.png",
    },
    {
        id: 9,
        title: "MoMo nâng tầm hệ sinh thái tài chính số thông qua hợp tác cùng ECHO MEDI",
        title_en: "MoMo Elevates Digital Finance Ecosystem, Partnering with Echo Medi to Enhance Healthcare Solutions to Millions in Vietnam",
        linkUrl: "https://www.openpr.com/news/3950951/momo-elevates-digital-finance-ecosystem-partnering-with-echo",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Momo_Echo_Medi_34fd42d204.jpg",
        imgTags: "https://api.echomedi.com/uploads/openpr_logo_slogan_1c0d534f1b.svg",
    },
    {
        id: 10,
        title: "MoMo nâng tầm hệ sinh thái tài chính số thông qua hợp tác cùng ECHO MEDI",
        title_en: "MoMo Elevates Digital Finance Ecosystem, Partnering with Echo Medi to Enhance Healthcare Solutions to Millions in Vietnam",
        linkUrl: "https://www.1888pressrelease.com/momo-elevates-digital-finance-ecosystem-partnering-with-ech-pr-748754.html",
        imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Momo_Echo_Medi_34fd42d204.jpg",
        imgTags: "https://api.echomedi.com/uploads/1888pressrelease_ae54e93246.png",
    },
    {
        id: 11,
        title: "ECHO MEDI ra mắt gói khám tiết kiệm 499.000: Rõ ràng – minh bạch – không phát sinh chi phí.",
        title_en: "ECHO MEDI launches a budget-friendly health check-up package for 499,000 VND: Clear – Transparent – No Hidden Costs.",
        linkUrl: "https://kinhtemoitruong.vn/echo-medi-ra-mat-goi-kham-tiet-kiem-499000-ro-rang-minh-bach-khong-phat-sinh-chi-phi-98959.html",
        imageUrl: "https://api.echomedi.com/uploads/z6616595822959_13ddffbcf849314ed444edb07fb01479_aa2b5bfbe4.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/logo_2_33f1cbaf5a.png",
    },
    {
        id: 12,
        title: "Chuyên gia đồng hành cùng sinh viên VJIT để \"bật mood\" học hành và chăm sóc sức khỏe hiệu quả",
        title_en: "Experts accompany VJIT students to boost their study motivation and take better care of their health.",
        linkUrl: "https://www.hutech.edu.vn/homepage/hoat-dong-sinh-vien/14626048-chuyen-gia-dong-hanh-cung-sinh-vien-vjit-de-bat-mood-hoc-hanh-va-cham-soc-suc-khoe-hieu-qua",
        imageUrl: "https://api.echomedi.com/uploads/Chuyen_gia_dong_hanh_cung_sinh_vien_VJIT_de_bat_mood_hoc_hanh_va_cham_soc_suc_khoe_hieu_qua_af5f492605.jpg",
        imgTags: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hutech_74db5a463b.jpg",
    },

];

const BlogTheMedia = () => {
    const router = useRouter();
    const locale = router.query.locale as string || 'vi';
    return (
        <>
            <article>
                <div className="mx-auto pt-6">
                    <h2 className="text-center font-bold md:text-[28px] text-2xl py-6 uppercase text-[#156634]">{locale == "en" ? "MEDIA COVERAGE" : "TRUYỀN THÔNG NÓI VỀ CHÚNG TÔI"}</h2>
                    <Swiper
                        slidesPerView={4}
                        spaceBetween={24}
                        centeredSlides={true}
                        autoplay={{
                            delay: 6000,
                            disableOnInteraction: false,
                        }}
                        pagination={{
                            clickable: true,
                        }}
                        loop={true}
                        navigation={true}
                        modules={[Autoplay, Pagination, Navigation]}
                        className="mySwiper slideblog"
                    >
                        {blogsNewsPaper.sort((a, b) => b.id - a.id).map(blog => (
                            <>
                                <SwiperSlide>
                                    <div key={blog.id} className="group w-full bg-white rounded-xl h-80">
                                        <a key={blog.id} href={blog.linkUrl} target="_blank" rel="noopener noreferrer">
                                            <div className="h-[180px] w-full  relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                                                <Image loading='lazy' width={200} height={180} className="w-full object-cover rounded-t-[12px] transition-opacity group-hover:opacity-30" alt="Image Tọa Đàm Sức Khỏe" src={blog.imageUrl} />
                                                <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                                                    <button className="px-4 py-2 text-base flex items-center gap-1 text-white transition-all duration-5000 ease-in-out transform translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-100">
                                                        {locale === "en" ? "Learn More" : "Xem chi tiết"}

                                                    </button>
                                                </div>
                                            </div>
                                            <div className='p-3'>
                                                <Image loading='lazy' width={110} height={32} className="!h-12 !w-32 mt-2 !object-fill" alt="Image Tọa Đàm Sức Khỏe" src={blog.imgTags} />
                                                <h4 className="text-base font-medium mb-2 whitespace-pre-line text-left">{locale == "en" ? blog.title_en : blog.title}</h4>
                                            </div>
                                        </a>
                                    </div>
                                </SwiperSlide>
                            </>
                        ))}
                    </Swiper>
                </div>
                <div className="flex justify-center mt-2">
                    <div className="py-2 text-center inline text-base border border-[#14813d] hover:bg-[#14813d] hover:text-white rounded-full text-[#156634] px-5">
                        <LinkComponent
                            href={"/newspapers"}
                            locale={locale}
                            skipLocaleHandling={false}
                        > {locale === "vi" ? "Xem tất cả" : "Load all"}</LinkComponent>
                    </div>
                </div>
            </article>
        </>
    );
}

export default BlogTheMedia;
