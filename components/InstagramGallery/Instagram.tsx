import React from "react";

import { useRouter } from 'next/router';

const Instagram = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';

  return (
    <div
      className="bg-white">
      <div className="gap-16 items-center py-8 px-4 mx-auto max-w-screen-xl lg:grid lg:grid-cols-2 lg:py-6 lg:px-6">
        <div className="font-light text-gray-500 sm:text-lg">
          <h2 className="text-xl text-gray-900 font-bold md:text-2xl">
            {locale === "en" ? "A comprehensive healthcare system for you and your family" :
              "Hệ thống y tế toàn diện cho bạn và gia đình"
            }
          </h2>
          <p style={{
            textAlign: "justify"
          }}
          className="text-sm my-4 !leading-6"> 
            {
              locale === "en" ? "ECHO MEDI members have immediate access to our healthcare professionals and wellness care team, allowing us to deliver the ultimate wellbeing to members whom we know personally and to whom we hold ourselves accountable to every single member, so members can rest assure to entrust their health to us." :
                "<PERSON>ồng hành cùng một đội ngũ y tế lành nghề gồm các bác sĩ, dược sĩ, điều dưỡng và chuyên gia dinh dưỡng, khách hàng sẽ được chăm sóc sức khoẻ toàn diện với phương châm “Phòng bệnh hơn chữa bệnh”."}
          </p>
          <button className="bg-green-800 p-2 text-sm text-white rounded-lg hover:bg-green-900"><a href="./about">{locale === "en" ? "More" :
              "Xem thêm"
            }</a></button>
        </div>
        <div className="grid grid-cols-2 gap-4 mt-8">
          <img className="w-full rounded-lg" src="https://d3e4m6b6rxmux9.cloudfront.net/T6xx0_Pb_eb5f4df5a0_2_4577a3a985.jpg?updated_at=2023-07-16T02:33:44.520Z" alt="office content 1" />
          <img className="mt-4 w-full lg:mt-10 rounded-lg" src="https://d3e4m6b6rxmux9.cloudfront.net/YR_Ah_D_En_463b4f35d8_1_410af91a10.jpg?updated_at=2023-07-16T02:34:38.158Z" alt="office content 2" />
        </div>
      </div>
    </div>
  );
};

export default Instagram;
