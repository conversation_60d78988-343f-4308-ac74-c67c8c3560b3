import React from "react";
import { useRouter } from 'next/router';
import Image from 'next/image'
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper';
import SliderOurPartners from "../Slider/SliderOurPartners";

export const dataImageEnterrrise = [
    {
        id: 1,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Logo_PMH_9d355f31f3.png",
    },
    {
        id: 2,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/vina11_removebg_preview_1_7aa2938013.png",
    },
    {
        id: 3,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_8_removebg_preview_773a13ff11.png",
    },
    {
        id: 4,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_9_removebg_preview_25ff51d7b4.png",
    },
    {
        id: 5,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_10_removebg_preview_911055b222.png",
    },
    {
        id: 6,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_11_removebg_preview_ca46dcaf2d.png",
    },
    {
        id: 7,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_12_removebg_preview_2aa0b9fdca.png",
    },
    {
        id: 8,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_13_removebg_preview_1_20449b61c5.png",
    },
    {
        id: 9,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_14_removebg_preview_232f3b331d.png",
    },
    {
        id: 10,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_15_removebg_preview_1_0898d10844.png",
    },
    {
        id: 11,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_16_removebg_preview_9cd9d6cfe4.png",
    },
    {
        id: 12,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_17_removebg_preview_0d2191c7e7.png",
    }
];
export const dataImageHeathyWorkshop = [
    {
        id: 1,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/vina11_removebg_preview_1_7aa2938013.png",
    },
    {
        id: 2,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Coats_Phong_Phu_Logo_10e5f89e86.png",
    },
    {
        id: 3,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Kuehne_Logo_fb71ee19b2.png",
    },
    {
        id: 4,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Tyme_Logo_bfb616cbce.png",
    },
    {
        id: 5,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Central_Retail_Logo_1a5d516317.png",
    },
    {
        id: 6,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Onsemi_Logo_e49d193536.png",
    },
    {
        id: 7,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Kimberly_Clark_Logo_0b526a3c8d.png",
    },
    {
        id: 8,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Ablnbev_Logo_0ee10eb443.png",
    },
    {
        id: 9,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Bel_Logo_23b489748c.png",
    },
    {
        id: 10,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Cargiall_Logo_0c29351aa3.png",
    },
    {
        id: 11,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thiet_ke_chua_co_ten_12_removebg_preview_2aa0b9fdca.png",
    },
    {
        id: 12,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/LDC_removebg_preview_8b3f0dfdf4.png",
    },
    {
        id: 13,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/coats_504a3da1a6.png",
    },
    {
        id: 14,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Shinhan_Bank_b3169b1232.png"
    },
    {
        id: 15,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/MB_Bank_41af18bce6.png"
    }
];
const Partner = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const dataImageCommunity = [
        {
            id: 1,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Phumyhung_9eea2b886b.png",
        },
        {
            id: 2,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Hung_Vuong_c93fb4ced7.png",
        },
        {
            id: 3,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Van_Hanh_c132ad80df.png",
        },

        {
            id: 4,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/joona_baby_7dedd7707c.png",
        },
        {
            id: 5,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_08_5a7cda32c9.png?updated_at=2023-05-07T04:10:28.238Z",
        },
        {
            id: 6,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_01_b501bc6435.png?updated_at=2023-05-07T04:03:02.252Z",
        },
        {
            id: 7,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_05_b6bc8b62f0.png?updated_at=2023-05-07T04:10:28.213Z",
        },
        {
            id: 8,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_10_48b428f1d1.png?updated_at=2023-05-07T04:10:28.206Z",
        },
        {
            id: 9,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_12_0f11460cc3.png?updated_at=2023-05-07T04:10:28.876Z",
        },
        {
            id: 10,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_13_9bb94e49a7.png?updated_at=2023-05-07T04:10:28.964Z",
        },
        {
            id: 11,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_14_49f3071356.png?updated_at=2023-05-07T04:10:29.011Z",
        },
        {
            id: 12,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_17_3c5ad041a1.png?updated_at=2023-05-07T04:10:29.972Z",
        },
        {
            id: 13,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Group_2_c3131620b4.png?updated_at=2023-08-31T03:09:11.209Z",
        },
        {
            id: 14,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Livin_a61c3fa0ea.png",
        },
        {
            id: 15,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Sky_View_e95d242a5b.png",
        },
        {
            id: 16,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/larcade_phu_my_hung_2d1e640237.png",
        },



    ];

    const dataImageInurance = [
        {
            id: 1,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/cathay_052ae47fd2.png?updated_at=2023-05-07T04:18:59.930Z",
        },
        {
            id: 2,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/fubon_745f77fc2a.jpg?updated_at=2023-05-07T04:18:59.955Z",
        },
        {
            id: 3,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/generali_29fd185aca.png?updated_at=2023-11-09T08:57:08.496Z",
        },
        {
            id: 4,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/uic_2abb0ed392.png?updated_at=2023-05-07T04:19:00.565Z",
        },
        {
            id: 5,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/pacific_b6b59b916d.png?updated_at=2023-05-07T04:19:00.308Z",
        },
        {
            id: 6,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/msig_35956811c0.png?updated_at=2023-05-07T04:19:00.158Z",
        },
        {
            id: 7,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/download_cca835550c.png?updated_at=2023-05-07T04:19:00.335Z",
        },
        {
            id: 8,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/bsh_c3097c6abf.png?updated_at=2023-05-07T04:19:00.328Z",
        },
        {
            id: 9,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/a_d2d5ef22c8.png?updated_at=2023-05-07T04:18:59.570Z",
        },
        {
            id: 10,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/bao_hiem_hung_vuong_7b61586d20.png?updated_at=2023-05-07T04:22:42.713Z",
        },
        {
            id: 11,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/bidv_28f890e516.png?updated_at=2023-05-07T04:18:59.699Z",
        },
        {
            id: 12,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Sokxay_3a214da084.png?updated_at=2023-06-08T05:22:48.693Z",
        },
        {
            id: 13,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/sun_life_financial_1_logo_png_transparent_ae074dbe05.png?updated_at=2023-06-08T05:22:48.678Z",
        },
        {
            id: 14,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/pti_3510ee8e70.png?updated_at=2023-06-08T05:22:48.322Z",
        },
        {
            id: 15,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/prudential_c29afb3896.png?updated_at=2023-06-08T05:52:17.214Z",
        },
        {
            id: 16,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/MB_ageas_03055b5b2e.png?updated_at=2023-06-08T05:22:48.300Z",
        },
        {
            id: 17,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/MIC_daa2f45c9c.png?updated_at=2023-06-08T05:22:48.293Z",
        },
        {
            id: 18,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/marine_aa5ea5c018.png?updated_at=2023-06-08T05:22:48.277Z",
        },
        {
            id: 19,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Manulife_1e630cf5af.png?updated_at=2023-06-08T05:22:47.785Z",
        },
        {
            id: 20,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Bao_hiem_hang_khong_1_95d6df0f63.png?updated_at=2023-06-08T05:22:47.777Z",
        },
        {
            id: 21,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Insmart_52de86a824.png?updated_at=2023-06-08T05:22:47.770Z",
        },
        {
            id: 22,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Hanwha_4f6788c8cb.png?updated_at=2023-06-08T05:22:47.758Z",
        },
        {
            id: 23,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/FWD_8e0a212101.png?updated_at=2023-06-08T05:22:47.750Z",
        },
        {
            id: 24,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/abic_9decb510c1.jpeg?updated_at=2023-06-08T05:22:47.277Z",
        },
        {
            id: 25,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/bao_long_4b625fcc90.png?updated_at=2023-06-08T05:22:47.270Z",
        },
        {
            id: 26,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/AIA_357c34e82c.png?updated_at=2023-06-08T05:22:47.260Z",
        },
        {
            id: 27,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Bao_minh_9ebbfb2c9a.png?updated_at=2023-06-08T05:22:47.244Z",
        },
        {
            id: 28,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Chubb_a50ad459d9.png?updated_at=2023-06-08T06:00:08.879Z",
        },
        {
            id: 29,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/allianz_49440241e1.png?updated_at=2023-06-08T05:22:47.236Z",
        },
        {
            id: 30,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/bh_toan_cau_41b536ae8f.png?updated_at=2023-06-08T06:06:58.707Z",
        },
        {
            id: 31,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Bao_hiem_PTI_5c406b308e.jpg?updated_at=2023-06-08T07:38:23.456Z"
        },
        {
            id: 32,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/luma_572ce8e4c4.png?updated_at=2023-06-26T05:55:00.636Z"
        },
        {
            id: 33,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/tokio_002352610d.jpg?updated_at=2023-05-07T04:19:00.580Z"
        },
        {
            id: 34,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/b_2_9bb446fcde.png"
        },

    ]
    const dataImageMedical = [
        {
            id: 1,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/800px_Columbia_Asia_Logo_a74f15c672.jpg?updated_at=2023-11-20T13:57:51.651Z",
        },
        {
            id: 2,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_09_43b88b6384.png?updated_at=2023-05-07T04:10:28.308Z",
        },
        {
            id: 3,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/becamex_976aa63ca5.png",
        },
        {
            id: 4,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_04_17eb5583d6.png?updated_at=2023-05-07T04:10:27.616Z",
        },
        {
            id: 5,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_18_f8b5d38e80.png?updated_at=2023-05-07T04:10:29.576Z",
        },
        {
            id: 6,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_15_0b1162654d.png?updated_at=2023-05-07T04:10:29.003Z",
        },
        {
            id: 7,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_02_2f44a1740d.png?updated_at=2023-05-07T04:04:35.409Z",
        },
        {
            id: 8,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_06_e97829f472.png?updated_at=2023-05-07T04:10:28.179Z",
        },
        {
            id: 9,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_07_d22cb65a07.png?updated_at=2023-05-07T04:10:28.196Z",
        },
        {
            id: 10,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/logo_hop_TAC_11_ab0d6f3c97.png?updated_at=2023-05-07T04:10:28.884Z",
        },
        {
            id: 11,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Benh_vien_Medlatec_1f3f7fb26d.png",
        },
        {
            id: 12,
            image: "https://d3e4m6b6rxmux9.cloudfront.net/benh_vien_quan_6_7cdfe055fd.png",
        },
    ];

    return (
        <>
            <div className="md:space-y-10 space-y-6">
                <h2 className="text-center font-bold md:text-[28px] text-2xl md:mb-4 py-2 uppercase text-[#156634]">
                    {locale == "en" ? "Partners and other collaborations" : " ĐỐI TÁC VÀ CÁC ĐƠN VỊ LIÊN KẾT KHÁC"}
                </h2>
                <div className="flex flex-col md:flex-row gap-6">
                    <div className="relative border border-[#14813D] p-2 rounded-lg md:w-1/2">
                        <div className="flex items-center justify-center text-xl absolute -top-4 left-1/2 transform -translate-x-1/2 bg-white px-4 font-semibold text-[#156634]">
                            {locale == "en" ? "Medical" : "Y tế"}
                        </div>
                        <div className="hidden md:block">
                            <div className="grid">
                                <Swiper
                                    slidesPerView={6}
                                    spaceBetween={5}
                                    loop={true}
                                    pagination={{
                                        clickable: true,
                                    }}
                                    autoplay={{
                                        delay: 600,
                                        disableOnInteraction: false,
                                    }}
                                    speed={600}
                                    effect="slide"
                                    modules={[Autoplay, Pagination]}
                                    className="mySwiper slide"
                                >
                                    {dataImageMedical.map((urlImage, index) => (
                                        <SwiperSlide key={index}>
                                            <div key={index} className="flex justify-center items-center h-12 rounded-2xl relative">
                                                <Image loading='lazy' width={100} height={48} alt="Image Y Tế" src={urlImage.image} />
                                            </div>
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                        </div>
                        <div className="block md:hidden">
                            <div className="grid">
                                <Swiper
                                    slidesPerView={4}
                                    spaceBetween={5}
                                    loop={true}
                                    pagination={{
                                        clickable: true,
                                    }}
                                    autoplay={{
                                        delay: 600,
                                        disableOnInteraction: false,
                                    }}
                                    effect="slide"
                                    speed={600}
                                    modules={[Autoplay, Pagination]}
                                    className="mySwiper slide"
                                >
                                    {dataImageMedical.map((urlImage, index) => (
                                        <SwiperSlide key={index}>
                                            <div key={index} className="flex justify-center items-center h-12 rounded-2xl relative">
                                                <Image loading='lazy' width={100} height={48} alt="Image Y Tế" src={urlImage.image} />
                                            </div>
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                        </div>
                    </div>
                    <div className="relative border border-[#14813D] p-2 rounded-lg md:w-1/2">
                        <div className="flex items-center justify-center text-xl absolute -top-4 left-1/2 transform -translate-x-1/2 bg-white px-4 font-semibold text-[#156634]">
                            {locale == "en" ? "Corporate" : "Doanh nghiệp"}
                        </div>
                        <div className="block md:hidden">
                            <div className="grid">
                                <Swiper
                                    slidesPerView={4}
                                    spaceBetween={5}
                                    loop={true}
                                    pagination={{
                                        clickable: true,
                                    }}
                                    autoplay={{
                                        delay: 600,
                                        disableOnInteraction: false,
                                    }}
                                    modules={[Autoplay, Pagination]}
                                    className="mySwiper slide"
                                >
                                    {dataImageEnterrrise.map((urlImage, index) => (
                                        <SwiperSlide key={index}>
                                            <div className="flex justify-center items-center h-12 rounded-2xl relative">
                                                <Image loading='lazy' width={100} height={48} alt="Image Doanh Nghiệp" src={urlImage.image} />
                                            </div>
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                        </div>
                        <div className="hidden md:block">
                            <div className="grid">
                                <Swiper
                                    slidesPerView={6}
                                    spaceBetween={5}
                                    loop={true}
                                    pagination={{
                                        clickable: true,
                                    }}
                                    autoplay={{
                                        delay: 600,
                                        disableOnInteraction: false,
                                    }}
                                    modules={[Autoplay, Pagination]}
                                    className="mySwiper slide"
                                >
                                    {dataImageEnterrrise.map((urlImage, index) => (
                                        <SwiperSlide key={index}>
                                            <div className="flex justify-center items-center h-12 rounded-2xl relative">
                                                <Image loading='lazy' width={100} height={48} alt="Image Doanh Nghiệp" src={urlImage.image} />
                                            </div>
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="relative border border-[#14813D] p-4 rounded-lg">
                    <div className="flex items-center justify-center text-xl absolute -top-4 left-1/2 transform -translate-x-1/2 bg-white px-4 font-semibold text-[#156634]">
                        {locale == "en" ? "Insurance" : "Bảo hiểm"}
                    </div>
                    <div className="hidden md:block">
                        <div className="grid grid-cols-12 py-8">
                            {
                                dataImageInurance.map((urlImage, index) => (
                                    <div key={index} className="flex justify-center items-center bg-white h-12 rounded-md relative">
                                        <Image loading='lazy' width={100} height={48} alt="Image Bảo Hiểm" src={urlImage.image} />
                                    </div>
                                ))
                            }
                        </div>
                    </div>
                    <div className="block md:hidden">
                        <div className="grid grid-cols-5 py-4">
                            {
                                dataImageInurance.map((urlImage, index) => (
                                    <div key={index} className="flex justify-center items-center bg-white h-12 rounded-md relative">
                                        <Image loading='lazy' width={100} height={48} alt="Image Bảo Hiểm" src={urlImage.image} />
                                    </div>
                                ))
                            }
                        </div>
                    </div>

                </div>
            </div>

        </>
    );
};

export default Partner;