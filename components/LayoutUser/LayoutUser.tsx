import { useState } from 'react';
import { useRouter } from 'next/router';
import useToken from '../../hooks/useToken';
import React from 'react';

interface MenuItem {
  id: string;
  label: string;
  en_label: string;
  linkURL: string;
}

interface MenuSliderProps {
  menuItems: MenuItem[];
  defaultActiveId: string;
}

const MenuSlider = ({ menuItems, defaultActiveId }: MenuSliderProps) => {
  const router = useRouter();
  const [activeMenuItem, setActiveMenuItem] = useState(defaultActiveId);
  const locale = router.query.locale as string || 'vi';
  const { removeToken } = useToken();
  const handleSelectMenuItem = (id: string, link: string) => {
    setActiveMenuItem(id);
    router.push(`/${locale}/${link}/`);
  };
  const handleLogout = () => {
    removeToken();
    window.location.href = "/"
  };
  return (
    <>
      <nav>
        {menuItems.map((item) => (
          <button
            key={item.id}
            className={`w-full text-left p-4 flex items-center justify-between ${activeMenuItem === item.id ? 'bg-[#EAF6EE] text-[#156634] font-semibold' : 'text-gray-600'}`}
            onClick={() => handleSelectMenuItem(item.id, item.linkURL)}
          >
            {locale === "en" ? item.en_label : item.label}
            <svg
              className="ml-4 h-3 w-3 cursor-pointer -rotate-90"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              fill={activeMenuItem === item.id ? '#156634' : 'black'}
            >
              <path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z" />
            </svg>
          </button>
        ))}
        <button onClick={() => handleLogout()} className="w-full text-left p-4 flex items-center text-gray-600 justify-between">
          {locale === "en" ? "Log out" : "Đăng xuất"}
          <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M17.8061 10.4797C17.9495 10.3485 18.0312 10.1631 18.0312 9.96875C18.0312 9.77438 17.9495 9.58896 17.8061 9.45781L13.7676 5.7655C13.4854 5.5075 13.0475 5.52711 12.7895 5.8093C12.5315 6.09148 12.5511 6.52939 12.8333 6.78739L15.5557 9.27645L5.33894 9.27644C4.95659 9.27644 4.64663 9.5864 4.64663 9.96875C4.64663 10.3511 4.95659 10.6611 5.33894 10.6611L15.5557 10.6611L12.8333 13.1501C12.5511 13.4081 12.5315 13.846 12.7895 14.1282C13.0475 14.4104 13.4854 14.43 13.7676 14.172L17.8061 10.4797Z" fill="#1C274C" />
            <path d="M6.26202 2.35337C6.64437 2.35337 6.95433 2.04341 6.95433 1.66106C6.95433 1.27871 6.64437 0.96875 6.26202 0.96875L6.21136 0.96875C4.94898 0.968731 3.93145 0.968716 3.13117 1.07631C2.3003 1.18802 1.60073 1.427 1.04511 1.98261C0.489496 2.53823 0.250517 3.2378 0.138811 4.06867C0.0312136 4.86895 0.0312307 5.88648 0.0312497 7.14887L0.0312495 12.7886C0.0312304 14.051 0.0312131 15.0685 0.13881 15.8688C0.250516 16.6997 0.489496 17.3993 1.04511 17.9549C1.60072 18.5105 2.3003 18.7495 3.13117 18.8612C3.93145 18.9688 4.94898 18.9688 6.21137 18.9688L6.26202 18.9688C6.64437 18.9688 6.95433 18.6588 6.95433 18.2764C6.95433 17.8941 6.64437 17.5841 6.26202 17.5841C4.93702 17.5841 4.01291 17.5827 3.31567 17.4889C2.63834 17.3979 2.27965 17.2313 2.02418 16.9758C1.76871 16.7203 1.60214 16.3617 1.51108 15.6843C1.41733 14.9871 1.41586 14.063 1.41586 12.738L1.41586 7.19952C1.41586 5.87452 1.41734 4.95042 1.51108 4.25317C1.60214 3.57584 1.76871 3.21716 2.02418 2.96168C2.27965 2.70621 2.63834 2.53964 3.31567 2.44858C4.01292 2.35484 4.93702 2.35337 6.26202 2.35337Z" fill="#1C274C" />
          </svg>

        </button>
      </nav>
    </>
  );
};

export default MenuSlider;
