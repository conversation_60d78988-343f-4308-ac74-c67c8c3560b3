import { useRouter } from 'next/router';
import React, { useState, useMemo } from "react";
import axios from "axios";
import toast from 'react-hot-toast';
import dayjs from "dayjs";
import moment from "moment";
import { DatePicker } from '../../lib/datePicker';
export default function BookingMain() {
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [bd, setBD] = useState<Date | null>(null);
  const [bookingDate, setBookingDate] = useState(moment().format('YYYY-MM-DD'));
  const [timeSlot, setTimeSlot] = useState("");
  const [gender, setGender] = useState("male");
  const [address, setAddress] = useState("");
  const [phone_number, setPhoneNumber] = useState("");
  const [phone_number_warning_msg, setPhoneNumberWarningMsg] = useState("");
  const [branch, setBranch] = useState("q7");
  const [selectedOptionMain, setSelectedOptionMain] = useState('clinicmain');
  const [message, setMessage] = useState("Khách hàng đặt lịch khám ở phòng khám");

  const handleOptionChangeMain = (event: any) => {
    const value = event.target.value;
    setSelectedOptionMain(value);
    setMessage(value === 'clinicmain' ? "Khách hàng đặt lịch khám ở phòng khám" : "Khách hàng đặt lịch khám tại nhà");
  };

  const isPastDate = (date: string) => {
    return dayjs(date).isBefore(dayjs().startOf("day"));
  };

  dayjs.locale(locale);

  const bookingSlots = useMemo(() => {
      let slots = [];
      const startTime = 9;
      let endTime = 19; // Kết thúc ở 19:00
      if (dayjs(bookingDate).day() === 0) {
        endTime = 14;
      }
      for (let i = startTime; i <= endTime; i++) {
        let slot = dayjs(bookingDate).set("hour", i).set("minute", 0);
        if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
          slots.push(slot);
        }
    
        if (i < endTime) {
          slot = dayjs(bookingDate).set("hour", i).set("minute", 30);
          if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
            slots.push(slot);
          }
        }
      }
    
      if (slots?.length) {
        setTimeSlot(slots[0].toISOString());
      }
    
      return slots;
    }, [bookingDate]);

  const handleBookingMain = () => {
    if (phone_number == "" || !validatePhone(phone_number)) {
      setPhoneNumberWarningMsg(locale == "vi" ? "Yêu cầu nhập số điện thoại hợp lệ." : "Please enter your phone number.")
      toast.error("Đặt lịch không thành công");
      return;
    }
    const payload = {
      data: {
        createNewPatient: true,
        full_name: name,
        contactFullName: name,
        gender,
        email,
        contactEmail: email,
        phone: phone_number,
        contactPhoneNumber: phone_number,
        message,
        birthday: bd ? dayjs(bd).toISOString() : null,
        address: {
          address
        },
        contactAddress: address,
        branch,
        bookingDate: timeSlot,
        note: message,
      }
    };

    axios
      .post("https://api.echomedi.com/api/bookings/createBookingFromWeb", payload)
      .then(function (response) {
        toast.success("Đặt lịch thành công");
        location.href = "/booking_detail/?code=" + response.data.booking.id;
      })
      .catch(function (error) {
        toast.error("Đặt lịch không thành công");
      });
  };

  function validatePhone(phone: string) {
    return /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
  }
  return (
    <>
      <section className=" bg-white p-6 rounded-lg">
        <div className="mx-auto">
          <div className="flex justify-between items-center px-4 md:px-5 md:py-2 lg:px-5 mx-auto">
            <div className="w-full justify-start items-center flex gap-4 ">
              <div className="flex items-center">
                <label htmlFor="radio-medium" className="flex items-center cursor-pointer text-[#14813d] text-base font-bold">
                  {locale === "en" ? "Booking:" : "Đặt Lịch:"}
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="radio-medium-3"
                  type="radio"
                  name="radio-size"
                  value="clinicmain"
                  className="hidden"
                  checked={selectedOptionMain === 'clinicmain'}
                  onChange={handleOptionChangeMain}
                />
                <label htmlFor="radio-medium-3" className={` flex items-center cursor-pointer  text-base ${selectedOptionMain === 'clinicmain' ? 'font-bold' : 'font-normal'}`}>
                  <span
                    className={`border border-gray-300 rounded-full mr-2 w-5 h-5 flex items-center justify-center`}
                  >
                    {selectedOptionMain === 'clinicmain' && <span className="bg-green-900 p-1 w-2.5 h-2.5 rounded-full"></span>}
                  </span>
                  {locale === "en" ? "Clinic Visit:" : "Tại phòng khám"}
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="radio-medium-4"
                  type="radio"
                  name="radio-size"
                  value="homemain"
                  className="hidden"
                  checked={selectedOptionMain === 'homemain'}
                  onChange={handleOptionChangeMain}
                />
                <label htmlFor="radio-medium-4" className={`flex items-center cursor-pointer  text-base ${selectedOptionMain === 'homemain' ? 'font-bold' : 'font-normal'}`}>
                  <span
                    className={`border border-gray-300 rounded-full mr-2 w-5 h-5 flex items-center justify-center`}
                  >
                    {selectedOptionMain === 'homemain' && <span className={`bg-green-900 p-1 w-2.5 h-2.5 rounded-full`}></span>}
                  </span>
                  {locale === "en" ? "Home Visit:" : "Khám tại nhà"}
                </label>
              </div>
            </div>
            <div className="w-full justify-end items-end flex">
              <button onClick={handleBookingMain} type="submit" className="font-bold flex px-6 py-2 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]">
                <span className="text-center text-white text-sm font-medium">{locale === "en" ? "Book now" : "Đặt lịch khám"}</span>
              </button>
            </div>
          </div>
        </div>
        <div className="w-full px-4 md:px-5 md:py-2 lg:px-5 mx-auto">
          <div className="flex items-center justify-between gap-6">
            <div className="w-full flex-col justify-start items-start flex ">
              <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Full name" : "Họ và tên"}:
              </label>
              <input type="text" id="name" name="name" onChange={(e) => {
                setName(e.target.value);
              }}
                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-md text-sm focus:outline-none focus:border-[#14813d] h-10"
                required placeholder={locale == "en" ? "Full name" : "Họ và tên"} />
            </div>
            <div className="border-l border-[#D0D5DD] h-12 relative -bottom-1.5"></div>
            <div className="w-full flex-col justify-start items-start flex ">
              <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                  <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                </svg>
              </label>
              <input type="tel" id="phone" name="phone" onChange={(e) => {
                setPhoneNumber(e.target.value);
              }}
                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-md focus:outline-none focus:border-[#14813d] text-sm h-10"
                required
                placeholder={locale == "en" ? "Phone" : "Số điện thoại"}
              />
              <span className="text-sm text-red-500">{phone_number_warning_msg}</span>
            </div>
            <div className="border-l border-[#D0D5DD] h-12 relative -bottom-1.5"></div>
            <div className={`w-full flex-col justify-start items-start  ${selectedOptionMain === 'homemain' ? 'flex' : 'hidden'}`}>
              <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Address" : "Địa chỉ"}:
              </label>
              <input type="text" id="address" name="address" onChange={(e) => {
                setAddress(e.target.value);
              }}
                className="h-auto border border-[#D0D5DD]  text-sm rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                required placeholder={locale == "en" ? "Address" : "Địa chỉ"} />
            </div>
            {selectedOptionMain !== 'clinicmain' && 
            <div className="border-l border-[#D0D5DD] h-12 relative -bottom-1.5"></div>
              }
            <div className="w-full flex-col justify-start items-start flex">
              <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Booking date" : "Ngày đặt lịch"}:
              </label>
              <input
                type="date"
                id="booking-date"
                name="booking-date"
                onChange={(e) => {
                  setBookingDate(e.target.value);
                }}
                value={bookingDate}
                min={dayjs().format("YYYY-MM-DD")}
                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-md text-sm focus:outline-none focus:border-[#14813d] h-10"
                required
              />
            </div>
            <div className="border-l border-[#D0D5DD] h-12 relative -bottom-1.5"></div>
            <div className="w-full flex-col justify-start items-start flex">
              <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Booking time" : "Giờ đặt lịch"}:
              </label>
              <select
                value={timeSlot}
                name="timeSlot"
                className="w-full px-4 py-2 border border-[#D0D5DD] rounded-md text-sm focus:outline-none focus:border-[#14813d] h-10" required
                onChange={(e) => setTimeSlot(e.target.value)}
              >
                {bookingSlots?.map((slot) => (
                  <option value={dayjs(slot).toISOString()}>
                    {dayjs(slot).format("HH:mm")}
                  </option>
                ))}
              </select>
            </div>
            <div className="border-l border-[#D0D5DD] h-12 relative -bottom-1.5"></div>
            <div className={`w-full flex-col justify-start items-start  ${selectedOptionMain === 'clinicmain' ? 'flex' : 'hidden'}`}>
              <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Location" : "Chi nhánh"}:
              </label>
              <select id="branch" name="branch" value={branch}
                onChange={(e) => setBranch(e.target.value)}
                className="h-auto border border-[#D0D5DD] focus:border-[#14813d]  text-sm rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                required>
                <option value="q7">{locale == "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7"}</option>
                <option value="q2">{locale == "en" ? "46 Nguyen Thi Dinh, An Phu Ward, District 2" : "46 Nguyễn Thị Định, P. An Phú, Quận 2"}</option>
                <option value="binhduong">{locale == "en" ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong" : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương"}</option>
              </select>
            </div>

          </div>
        </div>
      </section>
    </>
  )
}
