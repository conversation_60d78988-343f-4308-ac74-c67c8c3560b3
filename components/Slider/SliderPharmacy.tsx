import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper';
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { useRouter } from 'next/router';

const SliderPharmacy = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  return (
    <>
      <div className='swiper slide'>
        <Swiper
          slidesPerView={1}
          spaceBetween={30}
          loop={true}
          autoplay={{
            delay: 10000,
            disableOnInteraction: false,
          }}
          // centeredSlides={true}
          pagination={{
            clickable: true,
          }}
          modules={[Autoplay, Pagination]}
          className="mySwiper"
        >
          <SwiperSlide>
            <img className="object-cover w-full bg-[#F8F9FB]" src={locale == "vi" ? "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_2_aed9afe726.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_2_En_9f5af92a73.png"} />
          </SwiperSlide>
          <SwiperSlide>
            <img className="object-cover w-full bg-[#F8F9FB]" src={locale == "vi" ? "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_7_e1068e97e5.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_7_En_f731ff26b6.png"} />
          </SwiperSlide>
          <SwiperSlide>
            <img className="object-cover w-full bg-[#F8F9FB]" src={locale == "vi" ? "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_7_1_90861b0f2a.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_7_1_En_9c7711faa7.png"} />
          </SwiperSlide>
          <SwiperSlide>
            <img className="object-cover w-full bg-[#F8F9FB]" src={locale == "vi" ? "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_2_1_7ea23f7b93.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Co_So_Vat_Chat_Quan_2_1_En_e2ead6701e.png"} />
          </SwiperSlide>
        </Swiper>
      </div>
    </>
  );
};

export default SliderPharmacy;
