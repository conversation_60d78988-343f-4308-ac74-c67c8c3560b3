import React from "react";
import { useRouter } from 'next/router';
import Image from 'next/image';
import LinkComponent from "../Link";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper';
import { shimmer, toBase64 } from "../../lib/ui";
const MainSlide = () => {
  const router = useRouter();
  const locale = router.query.locale as string || 'vi';
  return (
    <>
      <article>
        <div className="mx-auto py-6 bg-[#FBFFFB]">
          {/* <h2 className="text-center font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale == "en" ? "4 Core Services" : "4 NỀN TẢNG CHÍNH"}</h2> */}
          <h2 className="text-center font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale == "en" ? "Core Services" : "NỀN TẢNG CHÍNH"}</h2>
          <section>
            <div className="slide flex justify-center gap-y-4 lg:gap-y-0 flex-wrap md:flex-wrap lg:flex-nowrap lg:flex-row lg:justify-between">
              <Swiper
                slidesPerView={1.5}
                spaceBetween={16}
                autoplay={{
                  delay: 10000,
                  disableOnInteraction: false,
                }}
                pagination={{
                  clickable: true,
                }}
                modules={[Autoplay, Pagination]}
                className="mySwiper"
              >
                <SwiperSlide>
                  <LinkComponent href="/goi_cham_soc/goi-cham-soc-phong-ngua/" skipLocaleHandling={false} locale={locale}>
                    <div className={`rounded-xl bg-[#EEFEEE]`}>
                      <div className=" flex items-center justify-end p-2">
                        <div>
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="-rotate-45" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.9385 6L20.9999 12.0613L14.9385 18.1227" stroke="#10853D" stroke-width="null" stroke-linecap="round" stroke-linejoin="round" className="my-path"></path>
                            <path d="M3 12.061L21 12.061" stroke="#10853D" stroke-width="null" stroke-linecap="round" className="my-path"></path>
                          </svg>
                        </div>
                      </div>
                      <div className={`px-3 h-28`}>
                        <h3 className="text-base text-[#156634] font-extrabold mb-1 text-left uppercase">{locale == "en" ? "1. Preventive Care" : "1. Chăm sóc phòng ngừa"}</h3>
                        <h4 className="text-xs font-normal mb-2 text-justify">{locale == "en" ? "Proposing solutions to ensure proactive maintenance and protection of both physical and mental health." : "Đề xuất giải pháp để đảm bảo chủ động trong việc gìn giữ, bảo vệ sức khỏe thể chất và tinh thần."}</h4>
                      </div>
                      <Image
                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(576, 384))}`}
                        layout="intrinsic" width={576} height={384} loading='lazy' alt={locale == "en" ? "Preventive Care" : "Chăm Sóc Phòng Ngừa"}
                        src="https://d3e4m6b6rxmux9.cloudfront.net/Cham_Soc_Phong_Ngua_7bedb2d2b8.png" />
                    </div>
                  </LinkComponent>
                </SwiperSlide>
                <SwiperSlide>
                  <LinkComponent href="/initial_treatment/goi-dieu-tri-ban-dau/" skipLocaleHandling={false} locale={locale}>
                    <div className={`rounded-xl bg-[#F4FFF4]`}>
                      <div className=" flex items-center justify-end p-2">
                        <div>
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="-rotate-45" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.9385 6L20.9999 12.0613L14.9385 18.1227" stroke="#10853D" stroke-width="null" stroke-linecap="round" stroke-linejoin="round" className="my-path"></path>
                            <path d="M3 12.061L21 12.061" stroke="#10853D" stroke-width="null" stroke-linecap="round" className="my-path"></path>
                          </svg>
                        </div>
                      </div>
                      <div className={`px-3 h-28`}>
                        <h3 className="text-base text-[#156634] font-extrabold mb-1 text-left uppercase">{locale == "en" ? "2. Primary care" : "2. Điều trị ban đầu"}</h3>
                        <h4 className="text-xs font-normal mb-2 text-justify">{locale == "en" ? "We recommend the appropriate treatment based on diagnostics and tests." : "Thực hiện các chẩn đoán và xét nghiệm, chúng tôi sẽ đưa ra phương pháp điều trị cần thiết."}</h4>
                      </div>
                      <Image
                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(576, 384))}`}
                        layout="intrinsic" width={576} height={384} loading='lazy' alt={locale == "en" ? "Primary care" : "Điều Trị Ban Đầu"}
                        src="https://d3e4m6b6rxmux9.cloudfront.net/Dieu_Tri_Ban_Dau_daa4b99029.png" />
                    </div>
                  </LinkComponent>
                </SwiperSlide>
                <SwiperSlide>
                  <LinkComponent href="/chronicdiseases/goi-quan-ly-benh-man-tinh/" skipLocaleHandling={false} locale={locale}>
                    <div className={`rounded-xl bg-[#EEFEEE]`}>
                      <div className=" flex items-center justify-end p-2">
                        <div>
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="-rotate-45" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.9385 6L20.9999 12.0613L14.9385 18.1227" stroke="#10853D" stroke-width="null" stroke-linecap="round" stroke-linejoin="round" className="my-path"></path>
                            <path d="M3 12.061L21 12.061" stroke="#10853D" stroke-width="null" stroke-linecap="round" className="my-path"></path>
                          </svg>
                        </div>
                      </div>
                      <div className={`px-3 h-28`}>
                        <h3 className="text-base text-[#156634] font-extrabold mb-1 text-left uppercase">{locale == "en" ? "3. Chronic Diseases" : "3. Quản lý bệnh mạn tính"}</h3>
                        <h4 className="text-xs font-normal mb-2 text-justify">{locale == "en" ? "Close personal companionship, effective disease management, and the optimization of safe medication use." : "Đồng hành chăm sóc chặt chẽ, kiểm soát bệnh và tối ưu hóa việc dùng thuốc an toàn, hiệu quả."}</h4>
                      </div>
                      <Image
                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(576, 384))}`}
                        layout="intrinsic" width={576} height={384} loading='lazy' alt={locale == "en" ? "Chronic Diseases" : "Quản Lý Bệnh Mạn Tính"}
                        src="https://d3e4m6b6rxmux9.cloudfront.net/Quan_Ly_Benh_Man_Tinh_17175e9705.png" />
                    </div>
                  </LinkComponent>
                </SwiperSlide>
                {/* <SwiperSlide>
                  <LinkComponent href="/comprehensive_health/goi-suc-khoe-toan-dien/" skipLocaleHandling={false} locale={locale}>
                    <div className={`rounded-xl bg-[#F4FFF4]`}>
                      <div className=" flex items-center justify-end p-2">
                        <div>
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="-rotate-45" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.9385 6L20.9999 12.0613L14.9385 18.1227" stroke="#10853D" stroke-width="null" stroke-linecap="round" stroke-linejoin="round" className="my-path"></path>
                            <path d="M3 12.061L21 12.061" stroke="#10853D" stroke-width="null" stroke-linecap="round" className="my-path"></path>
                          </svg>
                        </div>
                      </div>
                      <div className={`px-3 h-28`}>
                        <h3 className="text-base text-[#156634] font-extrabold mb-1 text-left uppercase">{locale == "en" ? "4. Wellness" : "4. Sức khoẻ toàn diện"}</h3>
                        <h4 className="text-xs font-normal mb-2 text-justify">{locale == "en" ? "Keep track of your overall health and recommend the best nutrition and psychological care to maintain both your physical and mental well-being." : "Theo dõi sức khỏe toàn diện, đề xuất dinh dưỡng và chăm sóc tinh thần tối ưu để bạn luôn khỏe mạnh cả thể chất lẫn tinh thần."}</h4>
                      </div>
                      <Image
                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(576, 384))}`}
                        layout="intrinsic" width={576} height={384} loading='lazy' alt={locale == "en" ? "Wellness" : "Sức Khoẻ Toàn Diện"}
                        src="https://d3e4m6b6rxmux9.cloudfront.net/Suc_Khoe_Toan_Dien_072abd4579.png" />
                    </div>
                  </LinkComponent>
                </SwiperSlide> */}
              </Swiper>
            </div>
          </section>
        </div>
      </article>
    </>
  );
}

export default MainSlide;
