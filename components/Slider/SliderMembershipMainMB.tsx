import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper';
import { useRouter } from 'next/router';
import Image from 'next/image';
import LinkComponent from '../Link';

const SliderMembershipMainMB = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const data = [
    {
      id: 740,
      name: locale === "en" ? "Gold Membership" : "Thành viên vàng",
      description: locale === "en" ? "Gold membership offers members an enhanced healthcare package, including free clinic check-ups and monthly telemedicine examinations. Members also receive discounts when using clinic services and purchasing medications. Join as a Gold member for a low, fixed yearly price." : "Gói thành viên vàng là gói chăm sóc sức khỏe nâng cao cho thành viên với các đặc quyền như: <PERSON><PERSON><PERSON> ph<PERSON> khám bệnh tại phòng khám và tư vấn sức khỏe từ xa 1 lần mỗi tháng, và nhận được các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên vàng với mức chi phí tiết kiệm và cố định hằng năm.",
      priceBeforeDiscount: "8.000.000 VND",
      priceAfterDiscount: "4.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_vien_vang_5c5f60adab.svg",
      link: "/membergold",
    },
    {
      id: 741,
      name: locale === "en" ? "Platinum Membership" : "Thành viên bạch kim",
      description: locale === "en" ? "Platinum membership offers a comprehensive healthcare package, including free unlimited clinic evaluations and telemedicine, a complimentary general health check, as well as discounts on services and medications at the clinic. Become a Platinum member for a low, fixed yearly price." : "Gói thành viên bạch kim là gói chăm sóc sức khỏe toàn diện với các đặc quyền: Miễn phí không giới hạn khám tại phòng khám và tư vấn sức khỏe từ xa, miễn phí kiểm tra sức khỏe tổng quát, kèm các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên bạch kim với chi phí tiết kiệm và cố định hàng năm.",
      priceBeforeDiscount: "16.000.000 VND",
      priceAfterDiscount: "8.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_vien_bach_kim_b1f7b331a3.svg",
      link: "/memberplatinum",
    },
    {
      id: 839,
      name: locale === "en" ? "Family Doctor" : "Bác sĩ gia đình",
      description: locale === "en" ? "In today's fast-paced world, people are seeking healthcare solutions that are both efficient and reliable. ECHO MEDI has come up with an innovative service that is tailored to meet this need. The Family Doctor package allows customers to avail telehealth consultation service using cutting-edge technology. Customers can be assured that they are receiving high-quality healthcare services that are both convenient and secure." : "Đời sống ngày càng phát triển, nhu cầu về chất lượng và độ tiện dụng của dịch vụ ngày càng tăng cao. ECHO MEDI tạo ra gói Bác sĩ gia đình, cung cấp cho khách hàng dịch vụ tư vấn sức khỏe từ xa bằng cách ứng dụng công nghệ tiên tiến. Khách hàng không còn cần phải đến phòng khám để khám bệnh mà có thể tiếp cận đội ngũ y tế mọi lúc mọi nơi.",
      priceBeforeDiscount: "5.000.000 VND",
      priceAfterDiscount: "1.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Bac_si_gia_dinh_6448f2333d.svg",
      link: "/personal_medical_provider",
    },
    {
      id: 859,
      name: locale === "en" ? "Children's membership" : "Thành viên trẻ em",
      description: locale === "en" ? "The child membership package offers comprehensive care for children, including unlimited clinic visits and online consultations. It also includes two free mental health consultations or therapy sessions, a free consultation on genetic decoding test results, a personalized child health monitoring handbook, and discounts for other services and when purchasing medicine at the clinic." : "Gói thành viên trẻ em giúp trẻ nhận được sự chăm sóc tối ưu với số lần khám tại phòng khám cũng như tư vấn trực tuyến không giới hạn, được tham vấn/ trị liệu tâm lý miễn phí 2 lần, miễn phí tư vấn sử dụng kết quả xét nghiệm gen, nhận được sổ tay theo dõi và chăm sóc thiết kế riêng cho trẻ cùng với các ưu đãi khi sử dụng những dịch vụ khác và mua thuốc tại phòng khám.",
      priceAfterDiscount: "3.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_vien_nhi_59f30946ca.svg",
      link: "/memberchildren",
      contactNumber: "",
      priceBeforeDiscount: "",
      contactText: locale === "en" ? "" : "",
    },
    {
      id: 2,
      name: locale === "en" ? "Corporate Membership" : "Gói doanh nghiệp",
      description: locale === "en" ? "Package for the corporate members will change depending on the needs of each company. Please contact us directly for more information to purchase the most suitable offer for your company." : "Gói thành viên cho doanh nghiệp sẽ thay đổi phụ thuộc vào nhu cầu của từng doanh nghiệp. Hãy liên hệ trực tiếp với chúng tôi để được tư vấn và nhận ưu đãi phù hợp nhất dành cho doanh nghiệp của bạn.",
      priceBeforeDiscount: "",
      textPrice: locale === "en" ? "Contact" : "Liên hệ",
      contactText: locale === "en" ? "Contact" : "Liên hệ",
      contactNumber: "1900 638 408",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Goi_doanh_nghiep_f646e2fa19.png",
    },


  ];
  return (
    <>
        <Swiper
          slidesPerView={1.3}
          spaceBetween={16}
          pagination={{
            clickable: true,
          }}
          modules={[Pagination]}
          className="mySwiper slide"
        >
          {data.map((item, index) => (
            <SwiperSlide key={item.id}>
              <>
                {index === data.length - 1 ? (
                  <div key={index} className='flex flex-col relative pb-[75px] rounded-xl group w-full'>
                    <div className="h-[250px] w-full  relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                      <Image loading='lazy' layout="fill" className="w-full rounded-t-[12px] transition-opacity" alt="Image Tọa Đàm Sức Khỏe" src={item.imageSrc} />
                    </div>
                    <div className='px-2'>
                      <p className="text-base font-semibold whitespace-pre-line mt-4">{item.name}</p>
                    </div>
                    <div className='absolute bottom-6 w-full'>
                      <div className='mt-auto flex items-end justify-end px-4'>
                        <div className="flex items-end justify-end">
                          {item.contactText && (
                            <a href={`tel:${item.contactNumber}`}>
                              <div className="flex items-center text-sm font-medium gap-1">
                                {item.contactText}
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M7.88675 2.91685C8.03978 2.9175 8.18643 2.97825 8.29508 3.08602L10.9726 5.76352C11.3003 6.09165 11.4844 6.53644 11.4844 7.00019C11.4844 7.46394 11.3003 7.90873 10.9726 8.23685L8.29508 10.9144C8.18579 11.023 8.03794 11.084 7.88383 11.084C7.72972 11.084 7.58188 11.023 7.47258 10.9144C7.41791 10.8601 7.37451 10.7956 7.34489 10.7245C7.31528 10.6534 7.30003 10.5772 7.30003 10.5002C7.30003 10.4232 7.31528 10.3469 7.34489 10.2759C7.37451 10.2048 7.41791 10.1402 7.47258 10.086L10.1501 7.41435C10.2048 7.36013 10.2482 7.29561 10.2778 7.22452C10.3074 7.15344 10.3226 7.0772 10.3226 7.00019C10.3226 6.92318 10.3074 6.84694 10.2778 6.77585C10.2482 6.70477 10.2048 6.64025 10.1501 6.58602L7.47258 3.91435C7.41791 3.86012 7.37451 3.79561 7.3449 3.72452C7.31528 3.65344 7.30003 3.57719 7.30003 3.50019C7.30003 3.42318 7.31528 3.34694 7.3449 3.27585C7.37451 3.20477 7.41791 3.14025 7.47258 3.08602C7.52709 3.03196 7.59173 2.98918 7.6628 2.96015C7.73387 2.93112 7.80998 2.91641 7.88675 2.91685Z" fill="#322F35" />
                                  <path d="M3.80194 2.91685C3.95497 2.9175 4.10162 2.97825 4.21027 3.08602L7.71026 6.58602C7.76494 6.64025 7.80834 6.70477 7.83795 6.77585C7.86757 6.84694 7.88281 6.92318 7.88281 7.00019C7.88281 7.0772 7.86757 7.15344 7.83795 7.22452C7.80834 7.29561 7.76494 7.36013 7.71026 7.41435L4.21027 10.9144C4.10097 11.023 3.95313 11.084 3.79902 11.084C3.64491 11.084 3.49707 11.023 3.38777 10.9144C3.3331 10.8601 3.2897 10.7956 3.26008 10.7245C3.23047 10.6534 3.21522 10.5772 3.21522 10.5002C3.21522 10.4232 3.23047 10.3469 3.26008 10.2759C3.2897 10.2048 3.3331 10.1402 3.38777 10.086L6.4736 7.00019L3.38777 3.91435C3.3331 3.86012 3.2897 3.79561 3.26009 3.72452C3.23047 3.65344 3.21522 3.57719 3.21522 3.50019C3.21522 3.42318 3.23047 3.34694 3.26009 3.27585C3.2897 3.20477 3.3331 3.14025 3.38777 3.08602C3.44228 3.03196 3.50692 2.98918 3.57799 2.96015C3.64906 2.93112 3.72517 2.91641 3.80194 2.91685Z" fill="#322F35" />
                                </svg>
                              </div>
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className='flex flex-col relative pb-[75px] rounded-xl group w-full'>
                    <LinkComponent href={item.link} skipLocaleHandling={false} locale={locale}>
                      <div className="h-[250px] w-full relative rounded-t-[12px] overflow-hidden">
                        <div className="h-[250px] w-full rounded-t-[12px] overflow-hidden">
                          <Image
                            loading="lazy"
                            layout="fill"
                            objectFit="cover"
                            className="!w-full !h-full rounded-t-[12px] transition-opacity group-hover:opacity-30"
                            alt="Image Tọa Đàm Sức Khỏe"
                            src={item.imageSrc}
                          />
                        </div>

                        {(item.id === 740 || item.id === 741) && (
                          <Image
                            loading="lazy"
                            width={65}
                            height={32}
                            className="!w-16 !h-8 absolute top-1 -left-1"
                            alt="Image Icon Sale"
                            src={
                              locale === "en"
                                ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_14e4417fba.png"
                                : "https://d3e4m6b6rxmux9.cloudfront.net/Sale_952366e44c.svg"
                            }
                          />
                        )}

                        {/* {item.id === 839 && (
                        <Image
                          loading="lazy"
                          width={65}
                          height={32}
                          className="!w-16 !h-8 absolute top-1 -left-1"
                          alt="Image Icon Sale"
                          src={
                            locale === "en"
                              ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_8b7570c7d0.svg"
                              : "https://d3e4m6b6rxmux9.cloudfront.net/Sale80_63954d67d0.svg"
                          }
                        />
                      )} */}
                        {item.priceAfterDiscount && (
                          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                            <button className="px-4 py-2 text-base flex items-center gap-1 text-white transition-all duration-5000 ease-in-out transform translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-100">
                              {locale === "en" ? "Learn More" : "Xem chi tiết"}
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.01343 3.62499C9.18832 3.62568 9.35592 3.69078 9.48009 3.80624L12.5401 6.67499C12.9146 7.02656 13.125 7.50312 13.125 7.99999C13.125 8.49687 12.9146 8.97343 12.5401 9.32499L9.48009 12.1937C9.35518 12.3101 9.18622 12.3755 9.01009 12.3755C8.83397 12.3755 8.665 12.3101 8.54009 12.1937C8.47761 12.1356 8.42801 12.0665 8.39417 11.9904C8.36032 11.9142 8.34289 11.8325 8.34289 11.75C8.34289 11.6675 8.36032 11.5858 8.39417 11.5096C8.42801 11.4335 8.47761 11.3643 8.54009 11.3062L11.6001 8.44374C11.6626 8.38564 11.7122 8.31651 11.746 8.24035C11.7799 8.16419 11.7973 8.0825 11.7973 7.99999C11.7973 7.91748 11.7799 7.83579 11.746 7.75963C11.7122 7.68347 11.6626 7.61434 11.6001 7.55624L8.54009 4.69374C8.47761 4.63564 8.42801 4.56651 8.39417 4.49035C8.36032 4.41419 8.3429 4.3325 8.3429 4.24999C8.3429 4.16748 8.36032 4.08579 8.39417 4.00963C8.42801 3.93347 8.47761 3.86434 8.54009 3.80624C8.60239 3.74832 8.67626 3.70249 8.75749 3.67138C8.83871 3.64028 8.92569 3.62452 9.01343 3.62499Z" fill="white" />
                                <path d="M4.34689 3.62499C4.52178 3.62568 4.68938 3.69078 4.81355 3.80624L8.81354 7.55624C8.87603 7.61434 8.92563 7.68347 8.95947 7.75963C8.99332 7.83579 9.01074 7.91748 9.01074 7.99999C9.01074 8.0825 8.99332 8.16419 8.95947 8.24035C8.92563 8.31651 8.87603 8.38564 8.81354 8.44374L4.81355 12.1937C4.68864 12.3101 4.51967 12.3755 4.34355 12.3755C4.16743 12.3755 3.99846 12.3101 3.87355 12.1937C3.81107 12.1356 3.76147 12.0665 3.72762 11.9904C3.69378 11.9142 3.67635 11.8325 3.67635 11.75C3.67635 11.6675 3.69378 11.5858 3.72762 11.5096C3.76147 11.4335 3.81107 11.3643 3.87355 11.3062L7.40021 7.99999L3.87355 4.69374C3.81107 4.63564 3.76147 4.56651 3.72762 4.49035C3.69378 4.41419 3.67635 4.3325 3.67635 4.24999C3.67635 4.16748 3.69378 4.08579 3.72762 4.00963C3.76147 3.93347 3.81107 3.86434 3.87355 3.80624C3.93585 3.74832 4.00972 3.70249 4.09095 3.67138C4.17217 3.64028 4.25915 3.62452 4.34689 3.62499Z" fill="white" />
                              </svg>
                            </button>
                          </div>
                        )}
                      </div>
                      <div className='px-2'>
                        <p className="text-base font-semibold whitespace-pre-line mt-4">{item.name}</p>
                      </div>
                    </LinkComponent>
                    <div className='absolute bottom-6 w-full'>
                      <div className='mt-auto flex justify-end px-4'>
                        {item.buttonText && (
                          <div className="flex items-center text-sm font-medium gap-1">
                            {locale == "vi" ? "Chi tiết gói" : "Learn More"}
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M7.88675 2.91685C8.03978 2.9175 8.18643 2.97825 8.29508 3.08602L10.9726 5.76352C11.3003 6.09165 11.4844 6.53644 11.4844 7.00019C11.4844 7.46394 11.3003 7.90873 10.9726 8.23685L8.29508 10.9144C8.18579 11.023 8.03794 11.084 7.88383 11.084C7.72972 11.084 7.58188 11.023 7.47258 10.9144C7.41791 10.8601 7.37451 10.7956 7.34489 10.7245C7.31528 10.6534 7.30003 10.5772 7.30003 10.5002C7.30003 10.4232 7.31528 10.3469 7.34489 10.2759C7.37451 10.2048 7.41791 10.1402 7.47258 10.086L10.1501 7.41435C10.2048 7.36013 10.2482 7.29561 10.2778 7.22452C10.3074 7.15344 10.3226 7.0772 10.3226 7.00019C10.3226 6.92318 10.3074 6.84694 10.2778 6.77585C10.2482 6.70477 10.2048 6.64025 10.1501 6.58602L7.47258 3.91435C7.41791 3.86012 7.37451 3.79561 7.3449 3.72452C7.31528 3.65344 7.30003 3.57719 7.30003 3.50019C7.30003 3.42318 7.31528 3.34694 7.3449 3.27585C7.37451 3.20477 7.41791 3.14025 7.47258 3.08602C7.52709 3.03196 7.59173 2.98918 7.6628 2.96015C7.73387 2.93112 7.80998 2.91641 7.88675 2.91685Z" fill="#322F35" />
                              <path d="M3.80194 2.91685C3.95497 2.9175 4.10162 2.97825 4.21027 3.08602L7.71026 6.58602C7.76494 6.64025 7.80834 6.70477 7.83795 6.77585C7.86757 6.84694 7.88281 6.92318 7.88281 7.00019C7.88281 7.0772 7.86757 7.15344 7.83795 7.22452C7.80834 7.29561 7.76494 7.36013 7.71026 7.41435L4.21027 10.9144C4.10097 11.023 3.95313 11.084 3.79902 11.084C3.64491 11.084 3.49707 11.023 3.38777 10.9144C3.3331 10.8601 3.2897 10.7956 3.26008 10.7245C3.23047 10.6534 3.21522 10.5772 3.21522 10.5002C3.21522 10.4232 3.23047 10.3469 3.26008 10.2759C3.2897 10.2048 3.3331 10.1402 3.38777 10.086L6.4736 7.00019L3.38777 3.91435C3.3331 3.86012 3.2897 3.79561 3.26009 3.72452C3.23047 3.65344 3.21522 3.57719 3.21522 3.50019C3.21522 3.42318 3.23047 3.34694 3.26009 3.27585C3.2897 3.20477 3.3331 3.14025 3.38777 3.08602C3.44228 3.03196 3.50692 2.98918 3.57799 2.96015C3.64906 2.93112 3.72517 2.91641 3.80194 2.91685Z" fill="#322F35" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </>
            </SwiperSlide>
          ))}
        </Swiper>
    </>
  );
};

export default SliderMembershipMainMB;



