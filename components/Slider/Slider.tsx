import React from "react";
import { useRouter } from 'next/router'
import Image from 'next/image'
import { shimmer, toBase64 } from "../../lib/ui";
import BookingMain from "../BookingMain/BookingMain";
const Slider = () => {
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";
  return (
    <>
      <div id="gallery" className="w-full mx-auto z-1" data-carousel="slide">
        {/* <div className="relative flex justify-center">
          <div className="md:block hidden">
            <Image
              placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 580))}`}
              alt="Echo Medi banner" src={locale === "en" ? "/newbanner/Desk-ENG.webp" : "/newbanner/Desk-VIE.webp"} width={1920} height={580}
              priority />
          </div>
          <div className="block md:hidden">
            <Image
              placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(500, 312))}`}
              alt="Echo Medi banner" src={locale === "en" ? "/newbanner/MOBILE-eng.webp" : "/newbanner/MOBILE-VIE.png"} width={500} height={312}
              priority />
          </div>
        </div> */}
        <div className="relative z-10 bottom-4 mx-auto max-w-screen-2xl md:block hidden">
          <div className="px-16">
          <BookingMain />
          </div>
        </div>
      </div>
    </>

  );
};
export default Slider;
