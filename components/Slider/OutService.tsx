import React from "react";
import { useRouter } from 'next/router';
import Image from 'next/image';
import LinkComponent from "../Link";
import { shimmer, toBase64 } from "../../lib/ui";

const OutService = () => {
  const router = useRouter();
  const locale = router.query.locale as string || 'vi';

  const services = [
    {
      title: "1. Chăm sóc phòng ngừa",
      title_en: "1. Preventive Care",
      description: {
        vi: "Đề xuất giải pháp để đảm bảo chủ động trong việc gìn giữ, bảo vệ sức khỏe thể chất và tinh thần.",
        en: "Proposing solutions to ensure proactive maintenance and protection of both physical and mental health."
      },
      imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Cham_Soc_Phong_Ngua_7bedb2d2b8.png",
      icon: "https://d3e4m6b6rxmux9.cloudfront.net/icon_1_9eaf36e71f.png",
      link: "/goi_cham_soc/goi-cham-soc-phong-ngua/"
    },
    {
      title: "2. Điều trị ban đầu",
      title_en: "2. Primary Care",
      description: {
        vi: "Thực hiện các chẩn đoán và xét nghiệm, chúng tôi sẽ đưa ra phương pháp điều trị cần thiết.",
        en: "We recommend the appropriate treatment based on diagnostics and tests."
      },
      imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Dieu_Tri_Ban_Dau_daa4b99029.png",
      icon: "https://d3e4m6b6rxmux9.cloudfront.net/icon_2_18db2bb3a9.png",
      link: "/initial_treatment/goi-dieu-tri-ban-dau/"
    },
    {
      title: "3. Quản lý bệnh mạn tính",
      title_en: "3. Chronic Diseases",
      description: {
        vi: "Đồng hành chăm sóc chặt chẽ, kiểm soát bệnh và tối ưu hóa việc dùng thuốc an toàn, hiệu quả.",
        en: "Close personal companionship, effective disease management, and the optimization of safe medication use."
      },
      imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Quan_Ly_Benh_Man_Tinh_17175e9705.png",
      icon: "https://d3e4m6b6rxmux9.cloudfront.net/icon_3_f7fb908455.png",
      link: "/chronicdiseases/goi-quan-ly-benh-man-tinh/"
    },
    // {
    //   title: "4. Sức khoẻ toàn diện",
    //   title_en: "4. Wellness",
    //   description: {
    //     vi: "Theo dõi sức khỏe toàn diện, đề xuất dinh dưỡng và chăm sóc tinh thần tối ưu để bạn luôn khỏe mạnh cả thể chất lẫn tinh thần.",
    //     en: "Keep track of your overall health and recommend the best nutrition and psychological care to maintain both your physical and mental well-being."
    //   },
    //   imageUrl: "https://d3e4m6b6rxmux9.cloudfront.net/Suc_Khoe_Toan_Dien_072abd4579.png",
    //   icon: "https://d3e4m6b6rxmux9.cloudfront.net/icon_4_07cf4f05c6.png",
    //   link: "/comprehensive_health/goi-suc-khoe-toan-dien/"
    // }
  ];

  return (
    <>
      <h2 className="text-center font-bold md:text-[28px] text-2xl text-[#156634] my-8 uppercase">
        {/* {locale === "en" ? "4 Core Services" : "4 NỀN TẢNG CHÍNH"} */}
        {locale === "en" ? "Core Services" : "NỀN TẢNG CHÍNH"}
      </h2>
      {/* <div className="grid grid-cols-4 gap-6"> */}
      <div className="grid grid-cols-3 gap-6">
        {services.map((pkg, index) => (
          <div key={index}>
            <LinkComponent href={pkg.link} skipLocaleHandling={false} locale={locale}>
              <div className={`rounded-xl flex flex-col h-full bg-[#EEFEEE]`}>
                <div className="flex items-center justify-end p-2">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="-rotate-45"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.9385 6L20.9999 12.0613L14.9385 18.1227"
                      stroke="#10853D"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="my-path"
                    ></path>
                    <path
                      d="M3 12.061L21 12.061"
                      stroke="#10853D"
                      strokeWidth="2"
                      strokeLinecap="round"
                      className="my-path"
                    ></path>
                  </svg>
                </div>
                <div className="px-3 flex-grow h-20">
                  <h3 className="text-base text-[#156634] font-extrabold text-left uppercase">
                    {locale === 'en' ? pkg.title_en : pkg.title}
                  </h3>
                  <h4 className="text-xs font-normal mb-2 text-justify">
                    {locale === 'vi' ? pkg.description.vi : pkg.description.en}
                  </h4>
                </div>
                <div className="mt-auto">
                  <Image
                    placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(576, 384))}`}
                    layout="intrinsic"
                    width={576}
                    height={384}
                    loading="lazy"
                    className="object-cover rounded-xl"
                    alt="Image Core Services"
                    src={pkg.imageUrl}
                  />
                </div>
              </div>
            </LinkComponent>
          </div>
        ))}
      </div>
    </>
  );
};

export default OutService;
