import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper';
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { useRouter } from 'next/router';
const SliderGiftMain = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  return (
    <>
      <Swiper
        slidesPerView={1.5}
        spaceBetween={10}
        centeredSlides={true}
        autoplay={{
          delay: 6000,
          disableOnInteraction: false,
        }}
        pagination={{
          clickable: true,
        }}
        loop={true}
        // navigation={true}
        modules={[Autoplay, Pagination]}
        className="mySwiper slide"
      >
          <SwiperSlide>
            <div className='bg-[#F8F9FB]'>
              <img className="object-cover rounded-t-3xl" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_270_589964988b.png" />
              <p className="text-base pt-1 h-14 bg-white rounded-b-3xl">{locale === "en" ? "For Grandpa/Father" : "Dành tặng Ông/Bố"}</p>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className='bg-[#F8F9FB]'>
              <img className="object-cover rounded-t-3xl" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_361_1f3c5401ae.png" />
              <p className="text-base pt-1 h-14 bg-white rounded-b-3xl">{locale == "en" ? "For Grandma/Mom" : "Dành Tặng Bà/Mẹ"}</p>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className='bg-[#F8F9FB]'>
              <img className="object-cover rounded-t-3xl" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_363_518fba836c.png" />
              <p className="text-base pt-1 h-14 bg-white rounded-b-3xl">{locale === "en" ? "For Siblings/Relatives/Friends" : "Dành tặng Anh chị em/Họ hàng/Bạn bè"}</p>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className='bg-[#F8F9FB]'>
              <img className="object-cover rounded-t-3xl" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_369_74de40c2fb.png" />
              <p className="text-base pt-1 h-14 bg-white rounded-b-3xl">{locale === "en" ? "For Corporate/Employees" : "Dành tặng Doanh nghiệp/Nhân viên"}</p>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className='bg-[#F8F9FB]'>
              <img className="object-cover rounded-t-3xl" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_370_793b55d932.png" />
              <p className="text-base pt-1 h-14 bg-white rounded-b-3xl">{locale === "en" ? "For Members" : "Dành tặng thành viên"}</p>
            </div>
          </SwiperSlide>
        </Swiper>
    </>
  );
};

export default SliderGiftMain;
