import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css/effect-coverflow';
import { EffectCoverflow, Pagination, Navigation } from 'swiper';
import { useRouter } from 'next/router';

const images = [
    { id: 1, src: 'https://api.echomedi.com/uploads/9_498c944ce1.png', text: "Ung thư vú - Hiểu biết đúng để phòng ngừa", texten: "Breast Cancer: Understanding for Effective Prevention" },
    { id: 2, src: 'https://api.echomedi.com/uploads/10_d12c342a85.png', text: "<PERSON>uản lý căng thẳng", texten: "Stress Management" },
    { id: 3, src: 'https://api.echomedi.com/uploads/11_44815f7a05.png', text: "TIM KHỎE - TÂM AN", texten: "HEALTHY HEART - HEALTHY MIND" },
    { id: 4, src: 'https://api.echomedi.com/uploads/1_a90b06a2d3.png', text: "Thời trang và Sức khỏe", texten: "Fashion and Health" },
    { id: 5, src: 'https://api.echomedi.com/uploads/2_c50c89b324.png', text: "Chuyên đề về dinh dưỡng + Chuyên đề về Công thái học", texten: "Nutrition and Ergonomics" },
    { id: 6, src: 'https://api.echomedi.com/uploads/3_b6ffba574f.png', text: "Thời trang và Sức khỏe", texten: "Fashion and Health" },
    { id: 7, src: 'https://api.echomedi.com/uploads/4_a59ae46d89.png', text: "Vấn đề cơ xương khớp và Phương pháp Chiropractic", texten: "Musculoskeletal Issue and Chiropractic" },
    { id: 8, src: 'https://api.echomedi.com/uploads/5_914d540ac2.png', text: "Phòng ngừa ung thư", texten: "Cancer Prevention" },
    { id: 9, src: 'https://api.echomedi.com/uploads/6_125c69537d.png', text: "Quản lý mối quan hệ", texten: "Relationship Management" },
    { id: 10, src: 'https://api.echomedi.com/uploads/7_834d9bdd21.png', text: "Ung thư vú và biện pháp phòng ngừa", texten: "Breast Cancer and Effective Prevention" },
    { id: 11, src: 'https://api.echomedi.com/uploads/8_ce9a592f99.png', text: "TIM KHỎE - TÂM AN", texten: "HEALTHY HEART - HEALTHY MIND" },
];

export default function ImageHealthTalksSlider() {
    const router = useRouter();
    const locale = (router.query.locale as string) || "vi";
    return (
        <>
            <div className='hidden md:block'>
                <Swiper
                    navigation
                    pagination={{ clickable: true }}
                    effect="coverflow"
                    spaceBetween={200}
                    coverflowEffect={{
                        rotate: 0,
                        stretch: 50,
                        depth: 300,
                        modifier: 1,
                        slideShadows: true,
                    }}
                    slidesPerView={2}
                    centeredSlides
                    initialSlide={1}
                    loop
                    modules={[EffectCoverflow, Pagination, Navigation]}
                    className="mySwiper slideblog"
                >
                    {images.map((image) => (
                        <SwiperSlide key={image.id} className='rounded-3xl overflow-hidden'>
                            <div className="w-full h-[400px] relative overflow-hidden rounded-3xl">
                                <img
                                    src={image.src}
                                    alt="Image Tọa Đàm Sức Khỏe"
                                    className="w-full h-full object-right-top rounded-2xl"
                                />
                                <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black via-gray-900 to-transparent text-start p-2 text-sm md:text-base">
                                    <p className="leading-tight relative z-10">
                                        <span className="text-sm font-bold text-white">
                                            {locale === "en" ? "" : "Tọa Đàm Sức Khỏe"}
                                        </span>
                                        <br />
                                        <span className="text-base font-bold text-white uppercase">
                                            {locale === 'en' ? image.texten : image.text}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            <div className='block md:hidden'>
                <Swiper
                    slidesPerView={1.3}
                    spaceBetween={16}
                    pagination={{
                        clickable: true,
                    }}
                    modules={[Pagination]}
                    className="mySwiper slide"
                >
                    {images.map((image) => (
                        <SwiperSlide key={image.id} className='rounded-3xl'>
                            <div className="w-full h-[400px] relative overflow-hidden rounded-3xl">
                                <img
                                    src={image.src}
                                    alt="Image Tọa Đàm Sức Khỏe"
                                    className="w-full h-full object-right-bottom rounded-t-3xl"
                                />
                                <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black via-gray-900 to-transparent text-start p-2 text-sm md:text-base">
                                    <p className="leading-tight relative z-10">
                                        <span className="text-sm font-bold text-white">
                                            {locale === "en" ? "" : "Tọa Đàm Sức Khỏe"}
                                        </span>
                                        <br />
                                        <span className="text-base font-bold text-white uppercase">
                                            {locale === 'en' ? image.texten : image.text}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>

        </>
    )
}


