import React from "react";
import { useRouter } from 'next/router';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper';
const SliderOurPartners = () => {
  const router = useRouter();
  const locale = router.query.locale as string || 'vi';
  return (
    <>
      <section>
        <div className="mx-auto bg-[#FBFFFB]">
          <h2 className="text-center font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale == "en" ? "MEDICAL Strategic Partners" : "ĐỐI TÁC CHIẾN LƯỢC"}</h2>
          <section className="hidden md:block">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Cathay General Hospital */}
              <div className="rounded-lg overflow-hidden bg-white shadow-sm">
                <div className="h-24">
                  <img
                    src="https://d3e4m6b6rxmux9.cloudfront.net/Cathay_32c1b74ee1.png"
                    alt="Cathay General Hospital Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="px-4 pb-6 flex flex-col items-center">
                  <section className="h-32 w-full">
                    <div className="bg-[#009043] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                      {locale == "en" ? "Top 10 Best Hospitals 2023 in Taiwan" : "Một trong 10 bệnh viện tốt nhất tại Đài Loan"}
                    </div>
                    <div className="bg-[#009043] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                      {locale == "en" ? "Smart Hospital Award by the National Healthcare Quality Award in 2022" : "Giải Chất lượng Y tế Quốc gia hạng mục Bệnh viện thông minh năm 2022"}
                    </div>
                  </section>
                  <ul className="space-y-4 text-sm text-start">
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Providing superior medical services and conducting patient referrals" : "Hội chẩn và điều trị cho bệnh nhân trong trường hợp cần can thiệp sâu hơn cùng với ECHO MEDI"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Collaborating in researching, and providing training for doctors, pharmacists, nurses, nutritionists, and mental counselors" : "Hợp tác, nghiên cứu, đào tạo và phát triển chuyên môn cho đội ngũ y tế bao gồm: Bác sĩ, Dược sĩ, Điều dưỡng, Chuyên gia về dinh dưỡng và tâm lý"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Organizing seminars and conferences with leading experts" : "Hợp tác tổ chức chuyên sâu tổ chức các hội nghị và hội thảo với các chuyên gia y tế"}</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* EC Healthcare */}
              <div className="rounded-lg overflow-hidden bg-white shadow-sm">
                <div className="h-24">
                  <img
                    src="https://d3e4m6b6rxmux9.cloudfront.net/EC_48b568921b.png"
                    alt="Cathay General Hospital Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="px-4 pb-6 flex flex-col items-center">
                  <section className="h-32 w-full">
                    <div className="bg-[#233162] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                      {locale == "en" ? "Listed on the Hong Kong Stock Exchange" : "Niêm yết trên Sở giao dịch chứng khoán Hồng Kông"}
                    </div>
                    <div className="bg-[#233162] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                      {locale == "en" ? "Hong Kong's largest  medical/wellness clinics" : "Hệ thống phòng khám lớn nhất tại Hồng Kông"}
                    </div>
                  </section>
                  <ul className="space-y-4 text-sm text-start">
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Focusing on preventive and precision medicine to build the leading one-stop healthcare system" : "Tập trung vào y học dự phòng hướng đến mục tiêu trở thành hệ thống chăm sóc sức khỏe toàn diện hàng đầu"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Providing a full range of medical services including health checkups, vaccinations, lab testing, imaging diagnostics, primary care, specialist consultations, dental care, pain management, aesthetic medical, beauty and wellness" : "Cung cấp đầy đủ các dịch vụ y tế bao gồm các hoạt động thăm khám sức khỏe, tiêm chủng, xét nghiệm, chẩn đoán hình ảnh, chăm sóc ban đầu, tư vấn chuyên khoa, chăm sóc nha khoa, quản lý và theo dõi bệnh, y tế thẩm mỹ (sắc đẹp và sức khỏe)"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Owning 46 diverse brands to provide both luxury and affordable options, catering to a wide range of customers" : "Sở hữu 46 thương hiệu mang đến những lựa chọn đa dạng với chi phí hợp lý, phục vụ nhiều đối tượng khách hàng khác nhau"}</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* BIONET */}
              <div className="rounded-lg overflow-hidden bg-white shadow-sm">
                <div className="h-24">
                  <img
                    src="https://d3e4m6b6rxmux9.cloudfront.net/Bionet_c77f193ebd.png"
                    alt="Cathay General Hospital Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="px-4 pb-6 flex flex-col items-center">
                  <section className="h-32 w-full">
                    <div className="bg-[#DD7443] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                      {locale == "en" ? "Listed on the Taiwan Stock Exchange" : "Niêm yết trên Sở giao dịch chứng khoán Đài Loan"}
                    </div>
                    <div className="bg-[#DD7443] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                      {locale == "en" ? "One of the largest stem cell banks in Asia" : "Một trong những ngân hàng lưu trữ tế bào gốc lớn nhất Châu Á"}
                    </div>
                  </section>
                  <ul className="space-y-2 text-sm text-start">
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "A leading organization specializing in stem cell research, development, and digital healthcare applications for the treatment of over 60 diseases, including osteoarthritis, chronic wounds, and various cancers" : "Tập đoàn hàng đầu chuyên nghiên cứu, phát triển tế bào gốc và ứng dụng chăm sóc sức khỏe kỹ thuật số (Digital Healthcare) để điều trị hơn 60 loại bệnh, bao gồm viêm xương khớp, vết thương mạn tính và nhiều loại ung thư"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Applied Exosome in cell regeneration, skin and hair care, precision medicine, regenerative medicine, and prenatal testing" : "Ứng dụng Exosome vào Tái Tạo Tế Bào, Chăm Sóc Da và Tóc, Y Học Chính Xác, Y Học Tái Tạo và Xét Nghiệm Gen Trước Sinh (Prenatal Testing)"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Provided services to over 50% of newborns and 95.5% of reproductive medicine centers in Taiwan, and has collaborated with over 500 medical institutions worldwide" : "Đã cung cấp dịch vụ cho hơn 50% trẻ sơ sinh và 95,5% trung tâm y học sinh sản tại Đài Loan và đã hợp tác với hơn 500 tổ chức y tế trên toàn thế giới"}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">&#9733;</span>
                      <span className="text-justify">{locale == "en" ? "Provided services to 17 countries globally, addressing the health needs of over 1.8 million customers at various stages of life" : "Cung cấp dịch vụ đến 17 quốc gia trên toàn thế giới, đáp ứng nhu cầu sức khỏe của hơn 1,8 triệu khách hàng ở nhiều giai đoạn khác nhau của cuộc đời"}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
          <section className="block md:hidden px-4">
            <div className="slide">
              <Swiper
                slidesPerView={1.3}
                spaceBetween={16}
                pagination={{
                  clickable: true,
                }}
                modules={[Pagination]}
                className="mySwiper slide"
              >
                <SwiperSlide>
                  <div className="rounded-lg overflow-hidden shadow-lg min-h-[770px]">
                    <div className="h-24">
                      <img
                        src="https://d3e4m6b6rxmux9.cloudfront.net/Cathay_32c1b74ee1.png"
                        alt="Cathay General Hospital Logo"
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <div className="px-4 pb-6 flex flex-col items-center">
                      <section className="h-36 w-full">
                        <div className="bg-[#009043] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                          {locale == "en" ? "Top 10 Best Hospitals 2023 in Taiwan" : "Một trong 10 bệnh viện tốt nhất tại Đài Loan"}
                        </div>
                        <div className="bg-[#009043] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                          {locale == "en" ? "Smart Hospital Award by the National Healthcare Quality Award in 2022" : "Giải Chất lượng Y tế Quốc gia hạng mục Bệnh viện thông minh năm 2022"}
                        </div>
                      </section>
                      <ul className="space-y-4 text-sm text-start">
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Providing superior medical services and conducting patient referrals" : "Hội chẩn và điều trị cho bệnh nhân trong trường hợp cần can thiệp sâu hơn cùng với ECHO MEDI"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Collaborating in researching, and providing training for doctors, pharmacists, nurses, nutritionists, and mental counselors" : "Hợp tác, nghiên cứu, đào tạo và phát triển chuyên môn cho đội ngũ y tế bao gồm: Bác sĩ, Dược sĩ, Điều dưỡng, Chuyên gia về dinh dưỡng và tâm lý"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Organizing seminars and conferences with leading experts" : "Hợp tác tổ chức chuyên sâu tổ chức các hội nghị và hội thảo với các chuyên gia y tế"}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </SwiperSlide>
                <SwiperSlide>
                  <div className="rounded-lg overflow-hidden shadow-lg min-h-[770px]">
                    <div className="h-24">
                      <img
                        src="https://d3e4m6b6rxmux9.cloudfront.net/EC_48b568921b.png"
                        alt="Cathay General Hospital Logo"
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <div className="px-4 pb-6 flex flex-col items-center">
                      <section className="h-36 w-full">
                        <div className="bg-[#233162] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                          {locale == "en" ? "Listed on the Hong Kong Stock Exchange" : "Niêm yết trên Sở giao dịch chứng khoán Hồng Kông"}
                        </div>
                        <div className="bg-[#233162] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                          {locale == "en" ? "Hong Kong's largest  medical/wellness clinics" : "Hệ thống phòng khám lớn nhất tại Hồng Kông"}
                        </div>
                      </section>
                      <ul className="space-y-4 text-sm text-start">
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Focusing on preventive and precision medicine to build the leading one-stop healthcare system" : "Tập trung vào y học dự phòng hướng đến mục tiêu trở thành hệ thống chăm sóc sức khỏe toàn diện hàng đầu"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Providing a full range of medical services including health checkups, vaccinations, lab testing, imaging diagnostics, primary care, specialist consultations, dental care, pain management, aesthetic medical, beauty and wellness" : "Cung cấp đầy đủ các dịch vụ y tế bao gồm các hoạt động thăm khám sức khỏe, tiêm chủng, xét nghiệm, chẩn đoán hình ảnh, chăm sóc ban đầu, tư vấn chuyên khoa, chăm sóc nha khoa, quản lý và theo dõi bệnh, y tế thẩm mỹ (sắc đẹp và sức khỏe)"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Owning 46 diverse brands to provide both luxury and affordable options, catering to a wide range of customers" : "Sở hữu 46 thương hiệu mang đến những lựa chọn đa dạng với chi phí hợp lý, phục vụ nhiều đối tượng khách hàng khác nhau"}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </SwiperSlide>
                <SwiperSlide>
                  <div className="rounded-lg overflow-hidden shadow-lg min-h-[770px]">
                    <div className="h-24">
                      <img
                        src="https://d3e4m6b6rxmux9.cloudfront.net/Bionet_c77f193ebd.png"
                        alt="Cathay General Hospital Logo"
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <div className="px-4 pb-6 flex flex-col items-center">
                      <section className="h-36 w-full">
                        <div className="bg-[#DD7443] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs">
                          {locale == "en" ? "Listed on the Taiwan Stock Exchange" : "Niêm yết trên Sở giao dịch chứng khoán Đài Loan"}
                        </div>
                        <div className="bg-[#DD7443] text-white px-1 py-3 w-full text-center mb-4 rounded-md text-xs whitespace-pre-line">
                          {locale == "en" ? "One of the largest stem cell banks in Asia" : "Một trong những ngân hàng lưu trữ tế bào gốc lớn nhất Châu Á"}
                        </div>
                      </section>
                      <ul className="space-y-2 text-sm text-start">
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "A leading organization specializing in stem cell research, development, and digital healthcare applications for the treatment of over 60 diseases, including osteoarthritis, chronic wounds, and various cancers" : "Tập đoàn hàng đầu chuyên nghiên cứu, phát triển tế bào gốc và ứng dụng chăm sóc sức khỏe kỹ thuật số (Digital Healthcare) để điều trị hơn 60 loại bệnh, bao gồm viêm xương khớp, vết thương mạn tính và nhiều loại ung thư"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Applied Exosome in cell regeneration, skin and hair care, precision medicine, regenerative medicine, and prenatal testing" : "Ứng dụng Exosome vào Tái Tạo Tế Bào, Chăm Sóc Da và Tóc, Y Học Chính Xác, Y Học Tái Tạo và Xét Nghiệm Gen Trước Sinh (Prenatal Testing)"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Provided services to over 50% of newborns and 95.5% of reproductive medicine centers in Taiwan, and has collaborated with over 500 medical institutions worldwide" : "Đã cung cấp dịch vụ cho hơn 50% trẻ sơ sinh và 95,5% trung tâm y học sinh sản tại Đài Loan và đã hợp tác với hơn 500 tổ chức y tế trên toàn thế giới"}</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">&#9733;</span>
                          <span className="text-justify text-sm">{locale == "en" ? "Provided services to 17 countries globally, addressing the health needs of over 1.8 million customers at various stages of life" : "Cung cấp dịch vụ đến 17 quốc gia trên toàn thế giới, đáp ứng nhu cầu sức khỏe của hơn 1,8 triệu khách hàng ở nhiều giai đoạn khác nhau của cuộc đời"}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </SwiperSlide>
              </Swiper>
            </div>
          </section>
          <div className="md:my-6 my-4 p-6 rounded-lg bg-white shadow-sm">
              <h2 className="text-xl font-bold text-center">{locale === "en" ? "Dr. Lim Cheok Peng" : "Dr. Lim Cheok Peng"}</h2>
              <p className="text-center text-gray-600 italic text-sm mb-4">{locale === "en" ? "Medical Advisor" : "Cố Vấn Y Khoa"}</p>
              <div className="grid grid-cols-1 md:grid-cols-2 md:gap-6 gap-4 text-start text-sm">
                <div className="flex items-start">
                  <span className="mr-2">&#9733;</span>
                  <span>{locale === "en" ? "One of the pioneers in the development of modern healthcare in Singapore" : "Một trong những người tiên phong xây dựng nền y học hiện đại tại Singapore"}</span>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">&#9733;</span>
                  <span>{locale === "en" ? "Former CEO of Parkway Holdings - a leading global healthcare provider across 80+ hospitals and medical clinics in 10 countries" : "Cựu Tổng giám đốc điều hành của Parkway Holdings - tập đoàn chăm sóc sức khỏe toàn cầu hàng đầu tại hơn 80 bệnh viện và phòng khám ở 10 quốc gia"}</span>
                </div>
              </div>
            </div>
        </div>
      </section>
    </>
  );
}

export default SliderOurPartners;
