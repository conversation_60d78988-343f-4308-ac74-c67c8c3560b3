import { Swiper, SwiperSlide } from 'swiper/react';
import {Pagination } from 'swiper';
import { useRouter } from 'next/router';
import useIsMobile from '../../utils/detectMob';

export default function HealthSlider() {
    const router = useRouter();
    const locale = (router.query.locale as string) || "vi";
    const isMobile = useIsMobile();
    return (
        <>
            <section>
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="flex-col justify-start items-center gap-2.5 flex md:my-8">
                        <h2 className="text-center font-bold md:text-2xl text-lg text-[#156634] uppercase">{locale === "vi" ? "Ý Nghĩa Tọa Đàm" : "Health Talk's Benefits"}</h2>
                        <p className='text-justify text-sm md:text-base'>{locale == "en" ? "We strive to provide individuals with thorough knowledge about their health and the necessary support to take proactive care of it." : "Chúng tôi mong muốn trang bị cho mọi người kiến thức toàn diện về sức khỏe của họ và hỗ trợ cần thiết để chủ động chăm sóc sức khỏe bản thân"}</p>
                    </div>
                    {isMobile ? (
                        <Swiper
                            slidesPerView={1}
                            spaceBetween={20}
                            pagination={{
                                clickable: true,
                            }}
                            modules={[Pagination]}
                            className="mySwiper slide"
                        >
                            {cardDatas.map((cardData, index) => (
                                <SwiperSlide key={cardData.id} className='rounded-3xl'>
                                    <div
                                        key={cardData.id}
                                        className={`flex items-center justify-center flex-col px-6 rounded-lg relative !h-[400px] ${index === 1 ? 'md:transform md:-translate-y-14' : ''} group`}
                                    >
                                        <div className="z-10 absolute top-10 left-10 rounded-full flex items-center justify-center">
                                            <img src="https://api.echomedi.com/uploads/icon_toa_dam_4554e151aa.svg" alt="Background" className="object-cover group-hover:hidden" />
                                        </div>
                                        <img src="https://api.echomedi.com/uploads/Toa_dam_da2a809c88.png" alt="Background" className="absolute inset-0 w-full h-full object-cover group-hover:hidden" />
                                        <img src="https://api.echomedi.com/uploads/Toa_dam_hover_30ee20a537.png" alt="Background" className="absolute inset-0 w-full h-full object-cover hidden group-hover:block" />
                                        <div className="relative z-10 text-center">
                                            <h2 className="text-lg font-semibold mb-2">{locale === "en" ? cardData.title_en : cardData.title}</h2>
                                            <p className="text-sm">{locale === "en" ? cardData.description_en : cardData.description}</p>
                                        </div>
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-20">
                            {cardDatas.map((cardData, index) => (
                                <div
                                    key={index}
                                    className={`flex items-center justify-center flex-col px-6 rounded-lg relative !h-[400px] ${index === 1 ? 'md:transform md:-translate-y-14' : ''} group`}
                                >
                                    <div className="z-10 absolute top-10 left-10 rounded-full flex items-center justify-center">
                                        <img src="https://api.echomedi.com/uploads/icon_toa_dam_4554e151aa.svg" alt="Background" className="object-cover group-hover:hidden" />
                                        <img src="https://api.echomedi.com/uploads/icon_toa_dam_hover_b227fc1a27.svg" alt="Background" className="object-cover hidden group-hover:block" />
                                    </div>
                                    <img src="https://api.echomedi.com/uploads/Toa_dam_da2a809c88.png" alt="Background" className="absolute inset-0 w-full h-full object-cover group-hover:hidden" />
                                    <img src="https://api.echomedi.com/uploads/Toa_dam_hover_30ee20a537.png" alt="Background" className="absolute inset-0 w-full h-full object-cover hidden group-hover:block" />
                                    <div className="relative z-10 text-center">
                                        <h2 className="text-lg font-semibold mb-2">{locale === "en" ? cardData.title_en : cardData.title}</h2>
                                        <p className="text-sm">{locale === "en" ? cardData.description_en : cardData.description}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                </div>
            </section>


        </>
    )
}

export const cardDatas = [
    {
        id: 1,
        title: "Cải thiện sức khỏe và hiệu suất làm việc",
        title_en: "Improve health and work performance",
        description_en: "Proactive health care supports employees in restoring and maintaining stable health, enhancing their long-term work capacity and increasing efficiency in the workplace",
        description: "Chăm sóc sức khỏe chủ động giúp nhân viên phục hồi và duy trì trạng thái sức khỏe ổn định, tăng khả năng làm việc và hiệu quả cao hơn trong công việc",
        imgDefault: "https://api.echomedi.com/uploads/icon_toa_dam_hover_b227fc1a27.svg",
        imgHover: "https://api.echomedi.com/uploads/icon_toa_dam_4554e151aa.svg",
        backgroundImg: "https://api.echomedi.com/uploads/Toa_dam_92ecf76ea0.png"
    },
    {
        id: 2,
        title: "Giảm chi phí chăm sóc sức khỏe và tình trạng vắng mặt",
        title_en: "Reduce Health Care Costs and Absenteeism",
        description_en: "Investing in health care reduces illness, medical costs, and absenteeism, saving money for the company.",
        description: "Đầu tư vào chăm sóc sức khỏe giúp ngăn ngừa bệnh tật, giảm thiểu chi phí điều trị và tình trạng vắng mặt do ốm đau, qua đó tiết kiệm được chi phí cho công ty.",
        imgDefault: "https://api.echomedi.com/uploads/icon_toa_dam_hover_b227fc1a27.svg",
        imgHover: "https://api.echomedi.com/uploads/icon_toa_dam_4554e151aa.svg",
        backgroundImg: "https://api.echomedi.com/uploads/Toa_dam_92ecf76ea0.png"
    },
    {
        id: 3,
        title: "Xây dựng văn hóa làm việc tích cực",
        title_en: "Building a positive work culture",
        description_en: "Ensuring employee health fosters a positive work culture by creating a supportive environment that enhances employee satisfaction and engagement.",
        description: "Chăm sóc sức khỏe nhân viên góp phần tạo ra một môi trường làm việc thân thiện, giúp mọi người cảm thấy được quan tâm, từ đó nâng cao sự hài lòng và sự gắn kết của nhân viên.",
        imgDefault: "https://api.echomedi.com/uploads/icon_toa_dam_hover_b227fc1a27.svg",
        imgHover: "https://api.echomedi.com/uploads/icon_toa_dam_4554e151aa.svg",
        backgroundImg: "https://api.echomedi.com/uploads/Toa_dam_92ecf76ea0.png"
    }
];
