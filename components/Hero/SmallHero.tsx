import React from "react";

interface Props {
  heading: string;
  message: string;
  image_url?: string;
  image_placeholder_url?: string;
  sub_message?: Array<string>;
}

const SmallHero = ({ heading, message, image_url, sub_message }: Props) => {

  return (
    <div>
      <img
        style={{
          width: "100%",
          objectFit: "cover",
          objectPosition: "top",
        }}
        className="sm:h-auto h-[300px]"
        src={image_url}
      />
      <div className="p-5 text-black z-[2] max-w-[1048px] text-center m-auto">
        {heading && <h2 className="text-4xl mt-2 text-green-900 font-bold">{heading}</h2>}
        {message && <p className="py-5 text-xl sm:text-lg">{message}</p>}
        {sub_message && sub_message.map((s => <p>- {s}</p>))}
      </div>
    </div>
  );
};

export default SmallHero;
