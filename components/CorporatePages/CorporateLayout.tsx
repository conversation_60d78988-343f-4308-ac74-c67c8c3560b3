import React from 'react'
import { dataImageEnterrrise } from '../InstagramGallery/Partner';
import Image from 'next/image';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper';
import { useRouter } from 'next/router';
import { dataCompanyIcon, dataEchoMediCard, dataStaffIcon } from '../../utils/dataBusiness';
import { shimmer, toBase64 } from '../../lib/ui';
import useIsMobile from '../../utils/detectMob';
import { menuItems } from '../../pages/[locale]/personal_information';

export const CorporatePagesLayout = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const companyData = dataCompanyIcon(locale);
    const staffData = dataStaffIcon(locale);
    const echoMediCard = dataEchoMediCard(locale);
    const isMobile = useIsMobile();
    return (
        <>
            <section className='bg-white'>
                <div className="mx-auto max-w-screen-2xl">
                    <section className='slide md:px-16 px-4 py-4'>
                        {isMobile ? (
                            <>
                                <h2 className="text-center font-bold md:text-2xl text-lg uppercase mb-2 text-[#156634]">{locale === "vi" ? "ĐỐI TÁC ĐỒNG HÀNH" : "Partner"}</h2>
                                <div className="grid grid-cols-4 md:gap-3 xl:grid-cols-4">
                                    {
                                        dataImageEnterrrise.map((urlImage, index) => (
                                            <div key={index} className="flex justify-center items-center h-12 rounded-2xl relative">
                                                <Image loading='lazy' width={100} height={48} alt="Image Doanh Nghiệp" src={urlImage.image} />
                                            </div>
                                        ))
                                    }
                                </div>
                            </>
                        ) : (
                            <Swiper
                                slidesPerView={6}
                                spaceBetween={20}
                                pagination={{
                                    clickable: true,
                                }}
                                modules={[Pagination]}
                                className="mySwiper"
                            >
                                {
                                    dataImageEnterrrise.map((urlImage, index) => (
                                        <SwiperSlide >
                                            <div key={index} className="flex justify-center items-center h-20 rounded-2xl relative">
                                                <Image loading='lazy' width={150} height={80} alt="Image Doanh Nghiệp" src={urlImage.image} />
                                            </div>
                                        </SwiperSlide>
                                    ))
                                }
                            </Swiper>
                        )}
                        <p className='text-sm text-center pt-2'>{locale === "vi" ? "Các doanh nghiệp tiêu biểu" : "Some of our notable corporate clients"}</p>
                    </section>
                </div>
            </section>
            <section className="relative py-16">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="w-full flex-col justify-start items-start md:items-center inline-flex">
                        <div className="flex-col justify-start items-start md:items-center flex">
                            <h2 className="md:text-center text-left font-bold md:text-2xl text-lg text-[#156634] uppercase md:mb-2">{locale === "vi" ? "LỢI ÍCH CHO DOANH NGHIỆP" : "Benefits TO CORPORATE"}</h2>
                        </div>
                        {isMobile ? (
                            <ul role="list" className="mt-8">
                                {companyData.map((item, index) => (
                                    <li key={item.id}>
                                        <div className="relative pb-8">
                                            {index < companyData.length - 1 && (
                                                <span className="absolute left-6 top-4 -ml-px h-full w-0.5 border-l border-dashed border-[#C8EAD4]"></span>
                                            )}
                                            <div className="relative flex space-x-3">
                                                <div>
                                                    <span className="h-12 w-12 rounded-full flex items-center justify-center bg-white">
                                                        <img src={item.image} alt="" className="w-8 h-8" />
                                                    </span>
                                                </div>
                                                <div className="flex-col">
                                                    <h4 className="text-base md:text-center font-medium">{item.title}</h4>
                                                    <p className="text-sm md:text-center text-justify">{item.desc}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            <div className="flex-col justify-start items-start gap-4 grid grid-cols-3">
                                {companyData.map((data) => (
                                    <div key={data.id} className="flex flex-col py-4 items-center gap-4">
                                        <div className='p-2.5 rounded-full bg-white'>
                                            <img src={data.image} alt="" className='object-fill w-12 h-12' />
                                        </div>
                                        <div className="flex-col">
                                            <h4 className="text-base text-center font-medium">{data.title}</h4>
                                            <p className="text-sm text-center">{data.desc}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </section>
            <section className="relative">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="w-full flex-col justify-start items-start md:items-center inline-flex bg-[#F9FFFC] pt-12 px-4 rounded-[32px]">
                        <div className="flex-col justify-start items-start md:items-center flex">
                            <h2 className="md:text-center text-left font-bold md:text-2xl text-lg text-[#156634] uppercase md:mb-2">{locale === "vi" ? "LỢI ÍCH CHO NHÂN VIÊN" : "Benefits For employee"}</h2>
                        </div>
                        <div className='flex justify-center flex-col md:flex-row'>
                            <Image
                                width={430}
                                height={497}
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(430, 497))}`}
                                className='object-cover h-[500px] md:h-full'
                                src={`${locale === "vi" ? "https://api.echomedi.com/uploads/staff_08_5fa7b0ebfd.png" : "https://api.echomedi.com/uploads/staff_07_f79af6531f.png"}`} alt="LỢI ÍCH CHO NHÂN VIÊN DOANH NGHIỆP"
                            />
                            {isMobile ? (
                                <ul role="list" className="mb-12">
                                    {staffData.map((item, index) => (
                                        <li key={item.id}>
                                            <div className="relative pb-8">
                                                {index < staffData.length - 1 && (
                                                    <span className="absolute left-6 top-4 -ml-px h-full w-0.5 border-l border-dashed border-[#C8EAD4]"></span>
                                                )}
                                                <div className="relative flex space-x-3">
                                                    <div>
                                                        <span className="h-12 w-12 rounded-full flex items-center justify-center bg-white shadow-xl">
                                                            <img src={item.image} alt="" className="w-8 h-8" />
                                                        </span>
                                                    </div>
                                                    <div className="flex-col">
                                                        <h4 className="text-base md:text-center font-medium">{item.title}</h4>
                                                        <p className="text-sm md:text-center text-justify">{item.desc}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <section className='mt-8'>
                                    <div className="flex-col justify-start items-start gap-4 grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1">
                                        {staffData.map((data) => (
                                            <div key={data.id} className="flex md:flex-col flex-row py-4 items-start gap-4">
                                                <div>
                                                    <div className='p-2 rounded-full shadow-2xl bg-[#E9F8EE]'>
                                                        <img src={data.image} alt="" className='object-fill' />
                                                    </div>
                                                </div>
                                                <div className="flex-col items-start justify-start">
                                                    <h4 className="text-base font-medium text-left">{data.title}</h4>
                                                    <p className="text-sm text-left">{data.desc}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </section>
                            )}

                        </div>
                    </div>
                </div>
            </section>
            <section className="relative">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="flex-col justify-start items-center gap-2.5 flex mt-12">
                        <p className="text-center font-bold md:text-2xl text-lg text-[#156634] uppercase pb-8">{locale === "vi" ? "CHĂM SÓC SỨC KHỎE TOÀN DIỆN" : "COMPREHENSIVE HEALTHCARE"}</p>
                    </div>
                    {isMobile ? (
                        <Swiper
                            slidesPerView={1.3}
                            spaceBetween={20}
                            pagination={{
                                clickable: true,
                            }}
                            modules={[Pagination]}
                            className="mySwiper slide"
                        >
                            {echoMediCard.map((data) => (
                                <SwiperSlide key={data.id} className='rounded-3xl'>
                                    <div className="group cursor-pointer w-full p-3 bg-white rounded-lg h-80">
                                        <div className="flex items-center mb-4">
                                            <img src={data.image} alt="Harsh image" className="rounded-lg w-full" />
                                        </div>
                                        <div className="block">
                                            <h4 className="text-sm md:text-base mt-4 font-bold">{data.title}</h4>
                                            <p className="text-sm">{data.desc}</p>
                                        </div>
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    ) : (
                        <div className="flex justify-center mb-14 gap-y-8 lg:gap-y-0 flex-wrap md:flex-wrap lg:flex-nowrap lg:flex-row lg:justify-between lg:gap-x-4">
                            {echoMediCard.map((data) => (
                                <div key={data.id} className="group cursor-pointer w-full rounded-2xl p-3 bg-white">
                                    <div className="flex items-center mb-4">
                                        <img src={data.image} alt="Harsh image" className="rounded-lg w-full" />
                                    </div>
                                    <div>
                                        <h4 className="text-sm md:text-base mt-4 font-bold text-center">{data.title}</h4>
                                        <p className="text-sm text-center">{data.desc}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                </div>
            </section>
        </>
    )
}





