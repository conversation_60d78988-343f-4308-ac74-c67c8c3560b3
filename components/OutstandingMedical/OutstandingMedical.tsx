import React, { useMemo, useState } from "react";
import { useRouter } from 'next/router';
import Image from 'next/image';
import { link } from "fs";
import { addToCart, shimmer, toBase64 } from '../../lib/ui';
import LinkComponent from "../Link";
import Modal from "../components/Modal";
import Button from "../components/Button";
import axios from "axios";
import toast from 'react-hot-toast';
import dayjs from "dayjs";
import moment from "moment";
import { DatePicker } from '../../lib/datePicker';
const tranlsate = (s: string, locale: string | undefined) => {
  switch (s) {
    case "buy_now":
      if (locale === "en")
        return "Add to cart";
      else
        return "Thêm vào giỏ";
    case "learn_more":
      if (locale === "en")
        return "Learn More";
      else
        return "Tìm hiểu thêm";
  }
  return "";
}

const OutstandingMedical = () => {
  const router = useRouter();
  const locale = router.query.locale as string || 'vi';
  const [showModal, setShowModal] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [bd, setBD] = useState<Date | null>(null);
  const [bookingDate, setBookingDate] = useState(moment().format('YYYY-MM-DD'));
  const [timeSlot, setTimeSlot] = useState("");
  const [gender, setGender] = useState("male");
  const [address, setAddress] = useState("");
  const [phone_number, setPhoneNumber] = useState("");
  const [phone_number_warning_msg, setPhoneNumberWarningMsg] = useState("");
  const [message, setMessage] = useState("");
  const [branch, setBranch] = useState("q7");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng đặt lịch khám ở phòng khám về gói" + title)
  };
  const isPastDate = (date: string) => {
    return dayjs(date).isBefore(dayjs().startOf("day"));
  };
  dayjs.locale(locale);

  const bookingSlots = useMemo(() => {
      let slots = [];
      const startTime = 9;
      let endTime = 19; // Kết thúc ở 19:00
      if (dayjs(bookingDate).day() === 0) {
        endTime = 14;
      }
      for (let i = startTime; i <= endTime; i++) {
        let slot = dayjs(bookingDate).set("hour", i).set("minute", 0);
        if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
          slots.push(slot);
        }
    
        if (i < endTime) {
          slot = dayjs(bookingDate).set("hour", i).set("minute", 30);
          if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
            slots.push(slot);
          }
        }
      }
    
      if (slots?.length) {
        setTimeSlot(slots[0].toISOString());
      }
    
      return slots;
    }, [bookingDate]);



  const handleBooking = () => {
    if (phone_number == "" || !validatePhone(phone_number)) {
      setPhoneNumberWarningMsg(locale == "vi" ? "Yêu cầu nhập số điện thoại hợp lệ." : "Please enter your phone number.")
      toast.error("Đặt lịch không thành công");
      return;
    }
    const payload = {
      data: {
        createNewPatient: true,
        full_name: name,
        contactFullName: name,
        gender,
        email,
        contactEmail: email,
        phone: phone_number,
        contactPhoneNumber: phone_number,
        message,
        birthday: bd ? dayjs(bd).toISOString() : null,
        address: {
          address
        },
        contactAddress: address,
        branch,
        bookingDate: timeSlot,
        note: message,
      }
    };

    axios
      .post("https://api.echomedi.com/api/bookings/createBookingFromWeb", payload)
      .then(function (response) {
        toast.success("Đặt lịch thành công");
        location.href = "/booking_detail/?code=" + response.data.booking.id;
      })
      .catch(function (error) {
        toast.error("Đặt lịch không thành công");
      });
  };

  function validatePhone(phone: string) {
    return /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
  }
  const blogs = [
    {
      id: 713,
      price: "2,350,000",
      href: "/services/goi-kham-suc-khoe-tong-quat-1/",
      imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Goi_Kham_Suc_Khoe_Tong_Quat_Tieu_Chuan_4f209299e4.png",
      title: "GÓI KHÁM SỨC KHỎE TỔNG QUÁT - TIÊU CHUẨN",
      title_en: "STANDARD GENERAL HEALTH CHECK UP PACKAGE",
      desc: "Khám sức khỏe định kỳ là phương pháp bảo vệ sức khỏe hiệu quả, giúp phát hiện và ngăn ngừa bệnh từ sớm. Đội ngũ ECHO MEDI sẽ thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra cơ bản nhằm phát hiện bệnh tiềm ẩn và các chỉ số bất thường của cơ thể. Từ đó, giúp ngăn ngừa bệnh, đưa ra chẩn đoán và điều trị sớm để duy trì và cải thiện tình trạng sức khỏe.",
      desc_en: "Regular health check-ups are an effective way to protect your health by detecting and preventing diseases early. ECHO MEDI team conducts a thorough examination and basic tests to identify potential diseases and abnormal body indicators. This helps prevent the onset of diseases and provides early diagnosis and treatment to maintain and improve your health status."
    },
    {
      id: 720,
      price: "6,400,000",
      href: "/services/quan-ly-dai-thao-duong/",
      imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/QUAN_LY_DAI_THAO_DUONG_ddd91328e0.png",
      title: "QUẢN LÝ ĐÁI THÁO ĐƯỜNG",
      title_en: "DIABETES MANAGEMENT",
      desc: "Đái tháo đường là bệnh lý mạn tính, âm thầm gây nhiều biến chứng đến mọi cơ quan trong cơ thể nhưng người bệnh không hay biết. Theo dõi, quản lý chặt chẽ trong suốt một năm sẽ giúp bệnh nhân nhận được sự quan tâm, hỗ trợ và điều trị hiệu quả theo mục tiêu cụ thể, giảm thiểu nguy cơ biến chứng và tăng cường chất lượng cuộc sống.",
      desc_en: "Diabetes is a chronic disease that can silently affect every organ of the body, without the patient realizing it. Regular monitoring and management throughout the year can help patients receive the necessary attention, support, and treatment as per their specific goals. This can help minimize the risk of complications and improve their quality of life."
    },
    {
      id: 867,
      price: "7,100,000",
      href: "/services/tham-van-tam-ly-cho-tre-vi-thanh-nien/",
      imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/THAM_VAN_TAM_LY_CHO_TRE_VI_THANH_NIEN_7aecccaef9.png",
      title: "THAM VẤN TÂM LÝ CHO TRẺ \n VỊ THÀNH VIÊN",
      title_en: "ADOLESCENT PSYCHOLOGICAL COUNSELLING",
      desc: "Đánh giá và can thiệp các vấn đề tâm lý ở trẻ vị thành niên, nhằm giúp trẻ vượt qua các khó khăn về tinh thần và phát triển một tâm lý vững vàng, lành mạnh. Liệu trình tham vấn hướng đến việc tạo ra một môi trường phát triển tích cực và hỗ trợ liên tục, giúp trẻ tự tin và thành công hơn trong học tập và cuộc sống.",
      desc_en: "Assess and intervene in psychological problems in adolescents to help them overcome mental difficulties and develop a strong and healthy mentality. The counseling process aims to create a positive developmental environment and provide continuous support, helping adolescents to be more confident and successful in their learning and in life."
    },
    {
      id: 865,
      price: "6,000,000",
      href: "/services/lieu-trinh-tam-ly-cho-tre-co-roi-loan-giac-ngu/",
      imgSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Roi_loan_giac_ngu_d08ab19388.jpg",
      title: "LIỆU TRÌNH TÂM LÝ CHO TRẺ CÓ RỐI LOẠN GIẤC NGỦ",
      title_en: "PEDIATRIC SLEEP DISORDER PSYCHOLOGICAL TREATMENT PACKAGE",
      desc: "Cải thiện chất lượng giấc ngủ cho trẻ, giúp các em có những giấc ngủ tốt. Từ đó phát triển khoẻ mạnh về thể chất lẫn tinh thần.",
      desc_en: "Improve children's sleep quality to foster their physical and mental health."
    }

  ];


  return (
    <>
      <article>
        <div className="md:px-16 px-4 py-[72px] hidden md:block">
          <h2 className="text-center font-bold md:text-[28px] text-2xl text-[#156634] uppercase mb-4"> {locale == "en" ? "Remarkable Services" : "CÁC GÓI KHÁM NỔI BẬT"}</h2>
          <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
            {blogs.map((blog, index) => (
              <>
                <div key={index} className='flex flex-col relative pb-[55px] hover:border hover:border-[#14813d] bg-white rounded-xl group'>
                  <LinkComponent href={blog.href} skipLocaleHandling={false} locale={locale}>
                    <div className="h-[150px] w-full  relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                      <Image loading='lazy' layout="fill" className="w-full object-cover rounded-t-[12px] transition-opacity group-hover:opacity-30" alt="Image Tọa Đàm Sức Khỏe" src={blog.imgSrc} />
                      <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                        <button className="px-4 py-2 text-base flex items-center gap-1 text-white transition-all duration-5000 ease-in-out transform translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-100">
                          {locale === "en" ? "Learn More" : "Xem chi tiết"}
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.01343 3.62499C9.18832 3.62568 9.35592 3.69078 9.48009 3.80624L12.5401 6.67499C12.9146 7.02656 13.125 7.50312 13.125 7.99999C13.125 8.49687 12.9146 8.97343 12.5401 9.32499L9.48009 12.1937C9.35518 12.3101 9.18622 12.3755 9.01009 12.3755C8.83397 12.3755 8.665 12.3101 8.54009 12.1937C8.47761 12.1356 8.42801 12.0665 8.39417 11.9904C8.36032 11.9142 8.34289 11.8325 8.34289 11.75C8.34289 11.6675 8.36032 11.5858 8.39417 11.5096C8.42801 11.4335 8.47761 11.3643 8.54009 11.3062L11.6001 8.44374C11.6626 8.38564 11.7122 8.31651 11.746 8.24035C11.7799 8.16419 11.7973 8.0825 11.7973 7.99999C11.7973 7.91748 11.7799 7.83579 11.746 7.75963C11.7122 7.68347 11.6626 7.61434 11.6001 7.55624L8.54009 4.69374C8.47761 4.63564 8.42801 4.56651 8.39417 4.49035C8.36032 4.41419 8.3429 4.3325 8.3429 4.24999C8.3429 4.16748 8.36032 4.08579 8.39417 4.00963C8.42801 3.93347 8.47761 3.86434 8.54009 3.80624C8.60239 3.74832 8.67626 3.70249 8.75749 3.67138C8.83871 3.64028 8.92569 3.62452 9.01343 3.62499Z" fill="white" />
                            <path d="M4.34689 3.62499C4.52178 3.62568 4.68938 3.69078 4.81355 3.80624L8.81354 7.55624C8.87603 7.61434 8.92563 7.68347 8.95947 7.75963C8.99332 7.83579 9.01074 7.91748 9.01074 7.99999C9.01074 8.0825 8.99332 8.16419 8.95947 8.24035C8.92563 8.31651 8.87603 8.38564 8.81354 8.44374L4.81355 12.1937C4.68864 12.3101 4.51967 12.3755 4.34355 12.3755C4.16743 12.3755 3.99846 12.3101 3.87355 12.1937C3.81107 12.1356 3.76147 12.0665 3.72762 11.9904C3.69378 11.9142 3.67635 11.8325 3.67635 11.75C3.67635 11.6675 3.69378 11.5858 3.72762 11.5096C3.76147 11.4335 3.81107 11.3643 3.87355 11.3062L7.40021 7.99999L3.87355 4.69374C3.81107 4.63564 3.76147 4.56651 3.72762 4.49035C3.69378 4.41419 3.67635 4.3325 3.67635 4.24999C3.67635 4.16748 3.69378 4.08579 3.72762 4.00963C3.76147 3.93347 3.81107 3.86434 3.87355 3.80624C3.93585 3.74832 4.00972 3.70249 4.09095 3.67138C4.17217 3.64028 4.25915 3.62452 4.34689 3.62499Z" fill="white" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className='px-[17px]'>
                      <p className='text-base font-semibold whitespace-pre-line mb-2 mt-3'>{locale === "en" ? blog.title_en : blog.title}</p>
                      <p className='text-sm text-justify'>{locale === "en" ? blog.desc_en : blog.desc}</p>
                    </div>
                  </LinkComponent>
                  <div className='absolute bottom-4 w-full'>
                    <div className='mt-auto flex items-center justify-end px-4'>
                      <button onClick={() => handleShowModal(blog.title, blog.title_en)} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-medium text-white">
                        {locale === "en" ? "Booking" : "Đặt lịch"}
                      </button>
                    </div>
                  </div>
                </div>
                <Modal
                  showCloseButton
                  visibleModal={showModal}
                  wrapperClassName="!w-[370px] md:!w-[600px]"
                  contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}
                >
                  <p className="text-2xl text-center font-bold text-[#156634]">{locale === "en" ? "Book an Appointment" : "Đặt lịch khám"}</p>
                  <p className='text-base text-center font-semibold my-2'>{locale === "en" ? currentBlog.title_en : currentBlog.title}</p>
                  <div className="flex flex-col justify-center mt-2">
                    <Button btnType="primary" onClick={() => { setShowModal(false) }}
                      style={{ width: "250px", height: "40px", borderRadius: "50px" }}
                      className="m-auto font-normal bg-white border border-[#156634]" type={undefined} icon={undefined}>
                       <img src="https://d3e4m6b6rxmux9.cloudfront.net/Phone_call_6a65c55145.svg" alt="Icon Phone" />
                      <p className="text-[#156634] mx-2">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                    </Button>
                    <div className="w-[270px] mx-auto flex mb-2 mt-4 text-xs items-center text-gray-500">
                      <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                      {locale == "en" ? "Or" : "Hoặc"}
                      <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                    </div>
                    <section>
                      <div className="w-full max-w-7xl p-4 md:p-5 lg:p-5 mx-auto bg-[#FAFAFA]">
                        <div className="w-full flex-col justify-start items-start gap-4 inline-flex">
                          <div className="w-full flex-col justify-start items-start gap-4 flex">
                            <div className="w-full flex-col justify-start items-start gap-4 flex">
                              <div className="w-full justify-start items-start gap-4 flex sm:flex-row flex-col">
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-base font-medium leading-relaxed">{locale == "en" ? "Full name" : "Họ và tên"}:
                                  </label>
                                  <input type="text" id="name" name="name" onChange={(e) => {
                                    setName(e.target.value);
                                  }}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                    required />
                                </div>
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-base font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                                    <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                                      <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                                    </svg>
                                  </label>
                                  <input type="tel" id="phone" name="phone" onChange={(e) => {
                                    setPhoneNumber(e.target.value);
                                  }}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-[#156634] text-xs lg:text-sm h-10"
                                    required />
                                  <span className="text-sm text-red-500">{phone_number_warning_msg}</span>
                                </div>
                              </div>
                              <div className="w-full justify-start items-start gap-8 flex sm:flex-row flex-col">
                                <div className="w-full flex-col justify-start items-star flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-base font-medium leading-relaxed">{locale == "en" ? "Location" : "Chi nhánh"}:
                                  </label>
                                  <select id="branch" name="branch" value={branch}
                                    onChange={(e) => setBranch(e.target.value)}
                                    className="h-10 border border-gray-300 text-gray-600 text-base rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                                    required>
                                    <option value="q7">{locale == "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7"}</option>
                                    <option value="q2">{locale == "en" ? "46 Nguyen Thi Dinh, An Phu Ward, District 2" : "46 Nguyễn Thị Định, P. An Phú, Quận 2"}</option>
                                    <option value="binhduong">{locale == "en" ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong" : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương"}</option>
                                  </select>
                                </div>
                              </div>
                              <div className="w-full justify-start items-start gap-8 flex flex-row">
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-base font-medium leading-relaxed">{locale == "en" ? "Booking date" : "Ngày đặt lịch"}:
                                  </label>
                                  <input
                                    type="date"
                                    id="booking-date"
                                    name="booking-date"
                                    onChange={(e) => {
                                      setBookingDate(e.target.value);
                                    }}
                                    value={bookingDate}
                                    min={dayjs().format("YYYY-MM-DD")}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                    required
                                  />
                                </div>
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-base font-medium leading-relaxed">{locale == "en" ? "Booking time" : "Giờ đặt lịch"}:
                                  </label>
                                  <select
                                    value={timeSlot}
                                    name="timeSlot"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10" required
                                    onChange={(e) => setTimeSlot(e.target.value)}
                                  >
                                    {bookingSlots?.map((slot) => (
                                      <option value={dayjs(slot).toISOString()}>
                                        {dayjs(slot).format("HH:mm")}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className='w-full'>
                            <div className='mt-auto flex items-end justify-end gap-12'>
                              <button onClick={() => setShowModal(false)} className="bg-white text-sm hover:underline hover:font-bold text-[#156634] font-medium">
                                {locale == "en" ? "Cancel" : "Hủy"}
                              </button>
                              <button onClick={handleBooking} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-semibold text-white">
                                {locale == "en" ? "Booking" : "Đặt lịch"}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </section>
                  </div>
                </Modal>
              </>
            ))}
          </div>
        </div>
      </article>

    </>
  );
}

export default OutstandingMedical;
