import React from 'react'
import LinkComponent from '../Link'
import Image from 'next/image'
import { shimmer, toBase64 } from '../../lib/ui'
interface HeaderMembershipProps {
    locale: string;
}
export default function HeaderMemBership({locale} : HeaderMembershipProps) {
    return (
        <>
            <section className="relative my-2">
                <div className="w-full mx-auto md:px-16 px-4">
                    <nav className="flex" aria-label="Breadcrumb">
                        <ol className="inline-flex items-center -space-x-1 md:space-x-1">
                            <li className="inline-flex items-center">
                                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                                    <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                                        {locale === "en" ? "Home" : "Trang chủ"}
                                    </span>
                                </LinkComponent>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                                    </svg>
                                    <LinkComponent locale={locale} skipLocaleHandling={false} href={"/membership"}>
                                        <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                                            {locale === "en" ? "Membership" : "Thành Viên"}
                                        </span>
                                    </LinkComponent>
                                </div>
                            </li>

                        </ol>
                    </nav>
                </div>
            </section>
            <section>
                <div className="mx-auto">
                    <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                        <div className="w-full relative">
                        <Image
                            src="/banner/banner_new.webp"
                            alt="Banner"
                            width={1920}
                            height={300}
                            placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                            className="object-center"
                            layout="responsive"
                        />
                        </div>
                        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 2xl:w-1/2 absolute md:px-16 px-4">
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block">
                                {locale === "vi" ? "Thành Viên Thường Niên" : "Annual Membership"}
                            </h2>
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                                {locale === "vi" ? "Thành Viên\n Thường Niên" : "Annual\n Membership"}
                            </h2>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
