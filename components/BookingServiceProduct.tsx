import React, { useState, useMemo } from 'react';
import dayjs from 'dayjs';
import moment from 'moment';
import toast from 'react-hot-toast';
import axios from 'axios';
import Modal from './components/Modal';
import Button from './components/Button';

interface ModalBookingProps {
    visible: boolean;
    onClose: () => void;
    currentBlog: {
        title: string;
        title_en: string;
    };
    locale: string;
}

const ModalBookingProduct: React.FC<ModalBookingProps> = ({ visible, onClose, currentBlog, locale }) => {
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [bd, setBD] = useState<Date | null>(null);
    const [bookingDate, setBookingDate] = useState(moment().format('YYYY-MM-DD'));
    const [timeSlot, setTimeSlot] = useState("");
    const [gender, setGender] = useState("male");
    const [address, setAddress] = useState("");
    const [phone_number, setPhoneNumber] = useState("");
    const [phone_number_warning_msg, setPhoneNumberWarningMsg] = useState("");
    const [message, setMessage] = useState("Khách hàng đặt lịch tư vấn về gói "+ currentBlog.title)
    const [branch, setBranch] = useState("q7");
    const isPastDate = (date: string) => dayjs(date).isBefore(dayjs().startOf("day"));

    const bookingSlots = useMemo(() => {
        let slots = [];
        const startTime = 9;
        let endTime = 19; // Kết thúc ở 19:00
        if (dayjs(bookingDate).day() === 0) {
          endTime = 14;
        }
        for (let i = startTime; i <= endTime; i++) {
          let slot = dayjs(bookingDate).set("hour", i).set("minute", 0);
          if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
            slots.push(slot);
          }
      
          if (i < endTime) {
            slot = dayjs(bookingDate).set("hour", i).set("minute", 30);
            if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
              slots.push(slot);
            }
          }
        }
      
        if (slots?.length) {
          setTimeSlot(slots[0].toISOString());
        }
      
        return slots;
      }, [bookingDate]);
      
    const handleBooking = () => {
        if (phone_number == "" || !validatePhone(phone_number)) {
            setPhoneNumberWarningMsg(locale == "vi" ? "Yêu cầu nhập số điện thoại hợp lệ." : "Please enter your phone number.")
            toast.error("Đặt lịch không thành công");
            return;
        }
        const payload = {
            data: {
                createNewPatient: true,
                full_name: name,
                contactFullName: name,
                gender,
                email,
                contactEmail: email,
                phone: phone_number,
                contactPhoneNumber: phone_number,
                message,
                birthday: bd ? dayjs(bd).toISOString() : null,
                address: {
                    address
                },
                contactAddress: address,
                branch,
                bookingDate: timeSlot,
                note: message,
            }
        };
        axios
          .post("https://api.echomedi.com/api/bookings/createBookingFromWeb", payload)
          .then(function (response) {
            toast.success("Đặt lịch thành công");
            location.href = "/booking_detail/?code=" + response.data.booking.id;
          })
          .catch(function (error) {
            toast.error("Đặt lịch không thành công");
          });
    };

    function validatePhone(phone: string) {
        return /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
    }

    return (
        <Modal
            showCloseButton
            visibleModal={visible}
            wrapperClassName="!w-[370px] md:!w-[600px]"
            contentClassName="!min-h-[0]"
            onClose={onClose}
        >
            <p className="text-2xl text-center font-bold text-[#156634]">{locale === "en" ? "Booking a consultation" : "Đặt lịch tư vấn"}</p>
            <p className='text-base text-center font-semibold my-2'>{locale === "en" ? currentBlog.title_en : currentBlog.title}</p>
            <div className="flex flex-col justify-center mt-2">
                <Button btnType="primary" onClick={onClose}
                    style={{ width: "250px", height: "40px", borderRadius: "50px" }}
                    className="m-auto font-normal bg-white border border-[#156634]" type={undefined} icon={undefined}>
                    <img src="https://d3e4m6b6rxmux9.cloudfront.net/Phone_call_6a65c55145.svg" alt="Icon Phone" />
                    <p className="text-[#156634] mx-2">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                </Button>
                <div className="w-[270px] mx-auto flex mb-2 mt-4 text-xs items-center text-gray-500">
                    <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                    {locale == "en" ? "Or" : "Hoặc"}
                    <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                </div>
                <section>
                    <div className="w-full max-w-7xl p-4 md:p-5 lg:p-5 mx-auto bg-[#FAFAFA] rounded-2xl">
                        <div className="w-full flex-col justify-start items-start gap-4 inline-flex">
                            <div className="w-full flex-col justify-start items-start gap-4 flex">
                                <div className="w-full flex-col justify-start items-start gap-4 flex">
                                    <div className="w-full justify-start items-start gap-4 flex sm:flex-row flex-col">
                                        <div className="w-full flex-col justify-start items-start flex">
                                            <label htmlFor="" className="flex gap-1 items-center  text-base font-medium leading-relaxed">{locale == "en" ? "Full name" : "Họ và tên"}:
                                            </label>
                                            <input type="text" id="name" name="name" onChange={(e) => {
                                                setName(e.target.value);
                                            }}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                                required />
                                        </div>
                                        <div className="w-full flex-col justify-start items-start flex">
                                            <label htmlFor="" className="flex gap-1 items-center  text-base font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                                                <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                                                    <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                                                </svg>
                                            </label>
                                            <input type="tel" id="phone" name="phone" onChange={(e) => {
                                                setPhoneNumber(e.target.value);
                                            }}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-[#156634] text-xs lg:text-sm h-10"
                                                required />
                                            <span className="text-sm text-red-500">{phone_number_warning_msg}</span>
                                        </div>
                                    </div>
                                    <div className="w-full justify-start items-start gap-8 flex sm:flex-row flex-col">
                                        <div className="w-full flex-col justify-start items-star flex">
                                            <label htmlFor="" className="flex gap-1 items-center  text-base font-medium leading-relaxed">{locale == "en" ? "Location" : "Chi nhánh"}:
                                            </label>
                                            <select id="branch" name="branch" value={branch}
                                                onChange={(e) => setBranch(e.target.value)}
                                                className="h-10 border border-gray-300 text-base rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                                                required>
                                                <option value="q7">{locale == "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7"}</option>
                                                <option value="q2">{locale == "en" ? "46 Nguyen Thi Dinh, An Phu Ward, District 2" : "46 Nguyễn Thị Định, P. An Phú, Quận 2"}</option>
                                                <option value="binhduong">{locale == "en" ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong" : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương"}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="w-full justify-start items-start gap-8 flex flex-row">
                                        <div className="w-full flex-col justify-start items-start flex">
                                            <label htmlFor="" className="flex gap-1 items-center  text-base font-medium leading-relaxed">{locale == "en" ? "Booking date" : "Ngày đặt lịch"}:
                                            </label>
                                            <input
                                                type="date"
                                                id="booking-date"
                                                name="booking-date"
                                                onChange={(e) => {
                                                    setBookingDate(e.target.value);
                                                }}
                                                value={bookingDate}
                                                min={dayjs().format("YYYY-MM-DD")}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                                required
                                            />
                                        </div>
                                        <div className="w-full flex-col justify-start items-start flex">
                                            <label htmlFor="" className="flex gap-1 items-center  text-base font-medium leading-relaxed">{locale == "en" ? "Booking time" : "Giờ đặt lịch"}:
                                            </label>
                                            <select
                                                value={timeSlot}
                                                name="timeSlot"
                                                className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10" required
                                                onChange={(e) => setTimeSlot(e.target.value)}
                                            >
                                                {bookingSlots?.map((slot) => (
                                                    <option value={dayjs(slot).toISOString()}>
                                                        {dayjs(slot).format("HH:mm")}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className='w-full'>
                                <div className='mt-auto flex items-end justify-end gap-12'>
                                    <button onClick={onClose} className="text-sm hover:underline hover:font-bold text-[#156634] font-medium">
                                        {locale == "en" ? "Cancel" : "Hủy"}
                                    </button>
                                    <button onClick={handleBooking} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-semibold text-white">
                                    {locale === "en" ? "Booking a consultation" : "Đặt lịch tư vấn"}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </Modal>
    );
};

export default ModalBookingProduct;
