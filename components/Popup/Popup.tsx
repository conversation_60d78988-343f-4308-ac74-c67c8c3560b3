import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image'
import { useRouter } from 'next/router';

const Popup = () => {
  const [isOpen, setIsOpen] = useState(false);
  const popupRef1 = useRef<HTMLDivElement>(null);
  const popupRef2 = useRef<HTMLDivElement>(null);
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';

  useEffect(() => {
    setIsOpen(true);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      popupRef1.current &&
      !popupRef1.current.contains(event.target as Node) &&
      popupRef2.current &&
      !popupRef2.current.contains(event.target as Node)
    ) {
      handleClose();
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <div>
      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="fixed inset-0 bg-black opacity-50"></div>
          <div ref={popupRef1} className="p-4 sm:flex flex-col relative hidden">
            <button
              className="self-end absolute top-6 right-6 hover:text-black z-10"
              onClick={handleClose}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 30 30"
                width="20px"
                height="20px"
                fill='rgb(22, 101, 52)'
              >
                <path d="M 7 4 C 6.744125 4 6.4879687 4.0974687 6.2929688 4.292998688 L 4.2929688 6.2929688 C 3.9019687 6.6839688 3.9019687 7.3170313 4.2929688 7.7070312 L 11.585938 15 L 4.2929688 22.292969 C 3.9019687 22.683969 3.9019687 23.317031 4.2929688 23.707031 L 6.2929688 25.707031 C 6.6839688 26.098031 7.3170313 26.098031 7.7070312 25.707031 L 15 18.414062 L 22.292969 25.707031 C 22.682969 26.098031 23.317031 26.098031 23.707031 25.707031 L 25.707031 23.707031 C 26.098031 23.316031 26.098031 22.682969 25.707031 22.292969 L 18.414062 15 L 25.707031 7.7070312 C 26.098031 7.3170312 26.098031 6.6829688 25.707031 6.2929688 L 23.707031 4.2929688 C 23.316031 3.9019687 22.682969 3.9019687 22.292969 4.2929688 L 15 11.585938 L 7.7070312 4.2929688 C 7.5115312 4.0974687 7.255875 4 7 4 z" />
              </svg>
            </button>
            <a href="/membership">
              <Image alt="ECHO MEDI" width={600} height={600}
                src={locale == "vi" ? "https://d3e4m6b6rxmux9.cloudfront.net/tv_f7089bf221.jpg?updated_at=2023-08-21T04:42:43.253Z"
                  : "https://d3e4m6b6rxmux9.cloudfront.net/ta_f3e9b843bf.jpg?updated_at=2023-08-21T04:42:43.203Z"}
                className="w-[600px] rounded-lg"
              />
              <button
                style={{
                  margin: 'auto',
                  width: '150px',
                  left: 0, right: 0, position: 'absolute', bottom: '5px',
                }}
              >
                <span
                  style={{
                    backgroundColor: "rgb(22, 101, 52)",
                    color: "white",
                    marginLeft: 6,
                    display: 'block'
                  }}
                  className="full-w text-center font-bold mt-1 flex px-2 py-2 text-black text-xs leading-tight uppercase rounded-full shadow-md hover:bg-green-700 hover:shadow-lg focus:bg-green-200 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-200 active:shadow-lg transition duration-150 ease-in-out bg-green-800"
                >
                  {locale === "en" ? "Learn More" : "TÌM HIỂU THÊM"}
                </span>
              </button>
            </a>
          </div>
          <div
            ref={popupRef2}
            className="p-2 flex flex-col relative sm:hidden -mt-5"
          >
            <button
              className="self-end absolute top-6 right-6 hover:text-black z-10"
              onClick={handleClose}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 30 30"
                width="30px"
                height="30px"
                fill='rgb(22, 101, 52)'
              >
                <path d="M 7 4 C 6.744125 4 6.4879687 4.0974687 6.2929688 4.2929688 L 4.2929688 6.2929688 C 3.9019687 6.6839688 3.9019687 7.3170313 4.2929688 7.7070312 L 11.585938 15 L 4.2929688 22.292969 C 3.9019687 22.683969 3.9019687 23.317031 4.2929688 23.707031 L 6.2929688 25.707031 C 6.6839688 26.098031 7.3170313 26.098031 7.7070312 25.707031 L 15 18.414062 L 22.292969 25.707031 C 22.682969 26.098031 23.317031 26.098031 23.707031 25.707031 L 25.707031 23.707031 C 26.098031 23.316031 26.098031 22.682969 25.707031 22.292969 L 18.414062 15 L 25.707031 7.7070312 C 26.098031 7.3170312 26.098031 6.6829688 25.707031 6.2929688 L 23.707031 4.2929688 C 23.316031 3.9019687 22.682969 3.9019687 22.292969 4.2929688 L 15 11.585938 L 7.7070312 4.2929688 C 7.5115312 4.0974687 7.255875 4 7 4 z" />
              </svg>
            </button>
            <a href="/membership">
              <Image width={400} height={490} alt='ECHO MEDI'
                src={locale == "vi" ? "https://d3e4m6b6rxmux9.cloudfront.net/mb_tv_c89b7cb925.jpg?updated_at=2023-08-21T04:42:42.695Z" :
                  "https://d3e4m6b6rxmux9.cloudfront.net/mb_ta_3a05734a17.jpg?updated_at=2023-08-21T04:42:42.633Z"}
                className="w-[600px] rounded-lg"
              />
              <button
                style={{
                  margin: 'auto',
                  width: '150px',
                  left: 0, right: 0, position: 'absolute', bottom: '-5px',
                }}
              >
                <span
                  style={{
                    backgroundColor: "rgb(22, 101, 52)",
                    color: "white",
                    marginLeft: 6,
                    display: 'block'
                  }}
                  className="full-w text-center font-bold mt-1 flex px-2 py-2 text-black text-xs leading-tight uppercase rounded-full shadow-md hover:bg-green-700 hover:shadow-lg focus:bg-green-200 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-200 active:shadow-lg transition duration-150 ease-in-out bg-green-800"
                >
                  {locale === "en" ? "Learn More" : "TÌM HIỂU THÊM"}
                </span>
              </button>
            </a>

          </div>

        </div>
      )}
    </div>
  );
};

export default Popup;
