import React from "react"

interface StepCircleProgressProps {
    totalSteps: number
    currentStep: number
    activeColor?: string
    lineColor?: string
}

const StepCircleProgress: React.FC<StepCircleProgressProps> = ({
    totalSteps,
    currentStep,
    activeColor = "#14813D",
    lineColor = "#B3B3B3",
}) => {
    return (
        <div className="flex items-center justify-center gap-1 my-4">
            {Array.from({ length: totalSteps }).map((_, index) => {
                const stepNumber = index + 1
                const isCompleted = currentStep > stepNumber
                const isActive = currentStep === stepNumber

                return (
                    <React.Fragment key={index}>
                        {/* Circle Step */}
                        <div className="flex items-center">
                            {isCompleted ? (
                                <div
                                    className="w-12 h-12 rounded-full flex items-center justify-center border-2 text-white"
                                    style={{ backgroundColor: activeColor, borderColor: activeColor }}
                                >
                                    ✓
                                </div>
                            ) : isActive ? (
                                <div className="border-2 rounded-full p-1" style={{ borderColor: activeColor }}>
                                    <div
                                        className="w-12 h-12 rounded-full flex items-center justify-center text-white"
                                        style={{ backgroundColor: activeColor }}
                                    >
                                        {stepNumber}
                                    </div>
                                </div>
                            ) : (
                                <div className="w-12 h-12 rounded-full flex items-center justify-center border-2 text-gray-500 bg-gray-100">
                                    {stepNumber}
                                </div>
                            )}
                        </div>

                        {stepNumber < totalSteps && (
                            <div
                                className="h-[2px] w-8 mx-1"
                                style={{ backgroundColor: lineColor }}
                            ></div>
                        )}
                    </React.Fragment>
                )
            })}
        </div>
    )
}

export default StepCircleProgress
