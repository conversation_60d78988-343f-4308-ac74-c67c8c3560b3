import { useState } from "react";


interface Props {
    socialImg: any;
  }
const ImageWithFullScreen = ({ socialImg }: Props) => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const fullScreenStyle = {
    container: {
      position: "fixed" as any,
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(0, 0, 0, 0.6)",
      zIndex: 9999,
    },
    image: {
      maxWidth: "100%",
      maxHeight: "100%",
      margin: "auto",
      position: "absolute" as any,
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    },
  };

  return (
    <div
      style={isFullScreen ? fullScreenStyle.container : {}}
      className={"hover:cursor-pointer"}
      onClick={(_) => setIsFullScreen(!isFullScreen)}
    >
      <img
        src={socialImg}
        alt={"image"}
        style={isFullScreen ? fullScreenStyle.image : {}}
        className={isFullScreen ? "" : "object-cover"}
      />
    </div>
  );
};

export default ImageWithFullScreen;
