import { useEffect, useState } from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import * as yup from "yup"
import toast from 'react-hot-toast';
import { createNewUser } from "../services/api/users"
import { updatePatient } from "../services/api/patient";
import { getErrorMessage } from "../utils/error";
import { useRouter } from 'next/router'
import Select from "./components/Select";
import Input from "./components/Input";
import { REGION_DATA } from "constants/Regions"
import dayjs from "dayjs";
import { DatePicker } from "../lib/datePicker";

const CustomersForm = ({ data, fromCheckIn, onUpdateGuestUserCheckin, onCloseModal }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [districtList, setDistrictList] = useState([])
  const [wardList, setWardList] = useState([])
  const locale = router.query.locale || 'vi';
  const [open, setOpen] = useState(false);
  const [birthday, setBirthday] = useState("");
  const [gender, setGender] = useState("");

  const validationSchema = yup.object({
  })

  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      code: data?.code || "",
      email: data?.patient?.email || "",
      full_name: data?.patient?.full_name || "",
      lastName: data?.lastName || "",
      gender: data?.gender || "",
      phone: data?.patient?.phone || "",
      birthday: !!data?.patient?.birthday ? new Date(data?.patient?.birthday) : null,
      address: {
        province: data?.patient?.address?.province || null,
        district: data?.patient?.address?.district || null,
        ward: data?.patient?.address?.ward || null,
        address: data?.patient?.address?.address || "",
      },
      customerTag:
        data?.customerTag === "referral"
          ? CUSTOMER_TAG.REFERRAL
          : data?.customerTag === "new"
            ? CUSTOMER_TAG.NEW_CUSTOMER
            : null,
      referral: data?.referral
        ? {
          value: data?.referral?.id,
          label: `${data?.referral?.firstName} ${data?.referral?.lastName} (${data?.referral?.code})`,
        }
        : null,
    },
  })

  const provincesList = REGION_DATA;

  // init district list
  useEffect(() => {
    if (data?.address?.province) {
      let chosenProvince = provincesList?.find((item) => item.id === data?.address?.province?.id)
      setDistrictList(chosenProvince?.level2s)
    }
  }, [data?.address?.province, provincesList])

  // init ward list
  useEffect(() => {
    if (data?.address?.district) {
      let chosenDistrict = districtList?.find(
        (districtItem) => districtItem.id === data?.address?.district.id
      )
      setWardList(
        chosenDistrict?.level3s?.map((ward) => {
          return { value: ward.id, label: ward.name }
        })
      )
    }
  }, [data?.address?.district, districtList])

  const onSubmit = async (formData) => {
    try {
      setLoading(true)
      const payload = {
        data: {
          ...formData,
          birthday: birthday,
          referral: formData?.referral?.value,
        }
      }
      if (data?.patient?.id) {
        await updatePatient(data?.patient?.id, payload)
        toast.success('Thành công');
      } else {
        const password = randomPassword()
        const res = await createNewUser({ ...payload, password, tmpPassword: password })
        if (res.data) {
          if (fromCheckIn) {
            onUpdateGuestUserCheckin(res.data.user)
          } else {
            // navigate(-1)
            toast.success("Customer updated successfully")
          }
        }
      }
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    setBirthday(dayjs(data?.patient?.birthday))
    setGender(data?.patient?.gender)
  }, []);


  const provinceFormatted = () => {
    return provincesList.map((province) => ({
      value: province.id,
      label: province.name,
    }))
  }

  let readOnly = false;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="bg-gray-100">
        <div className="lg:max-w-3xl mx-auto max-w-full lg:p-10">
          <div className="bg-white p-4 shadow-md lg:rounded-md">
            <h2 className="lg:text-2xl text-xl font-semibold mb-6 text-center">{locale == "en" ? "Personal Information" : "Thông tin cá nhân"}</h2>
            <div className="grid grid-cols-1 gap-y-5 gap-4">
              <div className="w-full col-span-2 sm:col-span-1">
                <Controller
                  name="full_name"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <Input
                      readOnly={readOnly}
                      name="full_name"
                      label="Họ và tên"
                      placeholder={"Nhập họ và tên"}
                      value={value}
                      onChange={onChange}
                      errors={errors?.address?.address?.message}
                    />
                  )}
                />
              </div>
              <div className="w-full col-span-2 sm:col-span-1">
                <label htmlFor="dob" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Date of birth" : "Ngày sinh"}:</label>
                <button type="button" id="dob" name="dob" onChange={(e) => {
                }}
                  onClick={e => setOpen(true)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 text-xs lg:text-sm h-10">
                  {dayjs(birthday).format("YYYY-MM-DD")}
                </button>

                <DatePicker
                  locale={locale}
                  isOpen={open}
                  onClose={() => setOpen(false)}
                  onChange={d => {
                    setBirthday(d);
                  }}
                  title={locale == "vi" ? "Chọn ngày" : "Pick date"}
                  dayNames={["a", "b"]}
                  monthNames={["a", "b"]}
                  defaultValue={new Date(2000, 1, 1)}
                  minDate={new Date(1910, 10, 10)}
                  maxDate={new Date()}
                  headerFormat='DD, MM dd'
                />
              </div>
              <div className="w-full">
                <label htmlFor="gender" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Gender" : "Giới tính"}:</label>
                <select id="gender" name="gender" value={gender}
                  onChange={(e) => setGender(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 text-xs lg:text-sm rounded-md focus:outline-none focus:border-blue-500 h-10" required>
                  <option value="male">{locale == "en" ? "Male" : "Nam"}</option>
                  <option value="female">{locale == "en" ? "Female" : "Nữ"}</option>
                </select>
              </div>
              <div>
                <label htmlFor="phone" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Phone" : "Số điện thoại"}:<span style={{ color: "red" }}> *</span></label>
                <input
                  disabled={true}
                  defaultValue={data?.patient?.phone}
                  type="tel" id="phone" name="phone" onChange={(e) => {
                    setPhoneNumber(e.target.value);
                  }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 text-xs lg:text-sm h-10"
                  required />
                <span className="text-sm text-red-500">{''}</span>
              </div>
              <div className="col-span-2">
                <label htmlFor="email" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">Email:</label>
                <input
                  disabled={true}
                  defaultValue={data?.patient?.email}
                  type="email" id="email" name="email" onChange={(e) => {
                    setEmail(e.target.value);
                  }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 text-xs lg:text-sm h-10"
                  required />
              </div>

            </div>
            <div className=" pt-4 grid grid-cols-1 sm:grid-cols-3 gap-x-6">
              <div className="w-full">
                <Controller
                  name="address.province"
                  control={control}
                  render={({ field: { value, ref } }) => (
                    <Select
                      isDisabled={readOnly}
                      placeholder="Chọn thành phố"
                      label="Thành phố"
                      name="address.province"
                      onChange={(e) => {
                        setValue(
                          "address.province",
                          { id: e.value, name: e.label },
                          { shouldDirty: true, shouldValidate: true }
                        )
                        let chosenProvince = provincesList?.find((item) => item.id === e.value)

                        setDistrictList(
                          chosenProvince?.level2s?.map((district) => {
                            return {
                              value: district.id,
                              label: district.name,
                              ...district,
                            }
                          })
                        )

                        setValue("address.district", null, { shouldDirty: true })
                        setValue("address.ward", null, { shouldDirty: true })
                      }}
                      value={value && { value: value?.id, label: value?.name }}
                      options={provinceFormatted()}
                      errors={errors?.address?.province?.message}
                    />
                  )}
                />
              </div>
              <div className="w-full">
                <Controller
                  name="address.district"
                  control={control}
                  render={({ field: { value, ref } }) => (
                    <Select
                      isDisabled={readOnly || !getValues("address.province")}
                      placeholder="Chọn quận"
                      label="Quận"
                      name="address.district"
                      onChange={(e) => {
                        setValue(
                          "address.district",
                          { id: e.value, name: e.label },
                          { shouldDirty: true, shouldValidate: true }
                        )
                        let chosenDistrict = districtList.filter(
                          (districtItem) => districtItem.id === e.value
                        )

                        setWardList(
                          chosenDistrict[0]?.level3s?.map((ward) => {
                            return { value: ward.name, label: ward.name }
                          })
                        )

                        setValue("address.ward", null)
                      }}
                      value={value && { value: value?.id, label: value?.name }}
                      options={districtList}
                      errors={errors?.address?.district?.message}
                    />
                  )}
                />
              </div>
              <div className="w-full">
                <Controller
                  name="address.ward"
                  control={control}
                  render={({ field: { value, ref } }) => (
                    <Select
                      isDisabled={readOnly || !getValues("address.district")}
                      placeholder="Chọn phường"
                      label="Phường"
                      name="address.ward"
                      onChange={(e) => {
                        setValue(
                          "address.ward",
                          { id: e.value, name: e.label },
                          { shouldDirty: true, shouldValidate: true }
                        )
                      }}
                      value={value && { value: value?.id, label: value?.name }}
                      options={wardList}
                      errors={errors?.address?.ward?.message}
                    />
                  )}
                />
              </div>
            </div>

            <Controller
              name="address.address"
              control={control}
              render={({ field: { onChange, value } }) => (
                <Input
                  readOnly={readOnly}
                  name="address.address"
                  label="Địa chỉ"
                  placeholder={"Nhập địa chỉ"}
                  value={value}
                  onChange={onChange}
                  errors={errors?.address?.address?.message}
                />
              )}
            />
            <div className="mt-8 flex justify-center">
              <button type="submit" className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-md hover:bg-green-700 font-bold">{locale == "en" ? "Save" : "Lưu"}</button>
            </div>
          </div>
        </div>
      </div>
    </form>
  )
}

export default CustomersForm
