// ./frontend/src/app/[lang]/components/PostList.tsx

import Image from "next/image";
import Link from "next/link";
import { getStrapiMedia, formatDate } from "../../utils/api-helpers";
import { useRouter } from 'next/router'
import { shimmer, toBase64 } from "../../lib/ui";
import { convertString } from "../../utils/convertString";

interface Article {
  id: 4;
  attributes: {
    title: string;
    description: string;
    slug: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    cover: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    category: {
      data: {
        attributes: {
          name: string;
          slug: string;
        };
      };
    };
    authorsBio: {
      data: {
        attributes: {
          name: string;
          avatar: {
            data: {
              attributes: {
                url: string;
              };
            };
          };
        };
      };
    };
  };
}

export default function PostList({
  data: articles,
  children,
}: {
  data: Article[];
  children?: React.ReactNode;
}) {
  const router = useRouter()
  const locale = router.query.locale || 'vi';
  return (
    <section>
      <div className="grid justify-center grid-cols-1 gap-6 md:grid-cols-3">
        {articles.map((article) => {
          const imageUrl = getStrapiMedia(
            article.attributes.cover?.data?.attributes.url
          );
          const category = article.attributes.category;
          const authorsBio = article.attributes.authorsBio?.data?.attributes;

          const avatarUrl = getStrapiMedia(
            authorsBio?.avatar.data.attributes.url
          );

          return (
            <Link
              href={`/vi/hoat_dong/${article.attributes.slug}`}
              key={article.id}
              className="group hover:no-underline focus:no-underline rounded-2xl overflow-hidden shadow-lg"
            >
              {imageUrl && (
                <Image
                  alt="presentation"
                  width="80"
                  height="80"
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(200, 200))}`}
                  className="object-cover w-full"
                  src={imageUrl}
                />
              )}
              <div className="p-3 space-y-2 relative">
                {avatarUrl && (
                  <Image
                    alt="avatar"
                    width="80"
                    height="80"
                    src={avatarUrl}
                    className="rounded-full h-16 w-16 object-cover absolute -top-8 right-4"
                  />
                )}
                <div className="flex justify-between items-center">
                  <span className="text-xs dark:text-gray-400">
                    {formatDate(article.attributes.publishedAt)}
                  </span>
                  {authorsBio && (
                    <span className="text-xs dark:text-gray-400">
                      {authorsBio.name}
                    </span>
                  )}
                </div>
                <h3 className="text-md group-hover:underline group-focus:underline font-semibold">
                  {convertString(article.attributes.title)}
                </h3>
              </div>
            </Link>
          );
        })}
      </div>
      {children && children}
    </section>
  );
}

