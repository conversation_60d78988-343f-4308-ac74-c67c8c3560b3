import React, { useEffect, useMemo, useState } from "react";
import { AiOutlineClose, AiOutlineMenu, AiOutlineArrowLeft } from "react-icons/ai";
import { useRouter } from "next/router";
import toast from "react-hot-toast";
import LinkComponent from "../Link";
import Modal from "../components/Modal";
import Button from "../components/Button";
import axios from 'axios'
import Image from 'next/image'
import { shimmer, toBase64 } from "../../lib/ui";
import Link from "next/link";
import dayjs from "dayjs";
import moment from "moment";
import { usePathname } from "next/navigation";
import useToken from "../../hooks/useToken";
import useUserData from "../../hooks/useUserData";
import ModalLogin from "../components/ModalLogin";
import OTPInput from "react-otp-input";
const getLogoUrl = (locale: String) => {
  if (locale == "vi") return "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Echo_Medi_Vn_d24b035bef.svg";
  return "https://d3e4m6b6rxmux9.cloudfront.net/Logo_Echo_Medi_En_f8cece4129.svg";
}

const NavBar = () => {
  const [nav, setNav] = useState(false);
  const [nav1, setNav1] = useState(false);
  const [nav2, setNav2] = useState(false);
  const [nav3, setNav3] = useState(false);
  const [nav4, setNav4] = useState(false);
  const [nav5, setNav5] = useState(false);
  const [nav7, setNav7] = useState(false);
  const [textColor, setTextColor] = useState("white");
  const [logged, setLogged] = useState(false);
  const [numOfItem, setNumOfItem] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [showModal2, setShowModal2] = useState(false);
  const [showModalLogin, setShowModalLogin] = useState(false);
  const [showModalSignup, setShowModalSignup] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [user, setUser] = useState({
    user: {
      phone: "",
      email: "",
    }
  });
  const { token } = useToken();
  const { userData } = useUserData(token);
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";

  const pathUrl = usePathname();
  const logout = () => {
    localStorage.removeItem('token');
    location.href = "/";
  }
  const register = () => {
    if (email == "") {
      toast.error("Thông tin không phù hợp")
    } else
      if (password != confirmPassword) {
        toast.error("Mật khẩu và xác nhận mật khẩu không trùng nhau")
      } else {
        axios.post('https://api.echomedi.com/api/auth/local/register', {
          "username": email,
          "email": email,
          "password": password,
        })
          .then(function (response) {
            toast.success('Đăng ký thành công');
          })
          .catch(function (error) {
            toast.error("Đăng ký thất bại")
          })
      }
  }

  const login = () => {
    if (email == "" || password == "") {
      toast.error("Thông tin không phù hợp")
    } else {
      const toastId = toast.loading('Loading...');
      axios
        .post('https://api.echomedi.com/api/user/auth', {
          identifier: email,
          password: password,
        })
        .then(response => {
          toast.success(locale == "vi" ? 'Đăng nhập thành công' : "Login successful");
          setLogged(true)
          localStorage.setItem('token', response.data.jwt);
          setUser(response.data)
        })
        .catch(error => {
          toast.error(locale == "vi" ? "Đăng nhập không thành công. Vui lòng kiểm tra lại tên đăng nhập, mật khẩu" : "Login fail. Invalid credentials")
        })
        .finally(() => {
          toast.dismiss(toastId);
          // location.href = "/";
        });;
    }
  }

  useEffect(() => {
    if (localStorage.getItem("token")) {
      const token = localStorage.getItem("token");
      setLogged(true);
      fetch("https://api.echomedi.com/api/product/getCart", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then(function (response) {
          setNumOfItem(response.user?.cart_lines?.length);
        })
        .catch(function (error) {
          // toast.error("Đăng ký thất bại");
        });
    }
  }, []);

  const handleNav = () => {
    setNav(!nav);
  };

  const handleNav1 = () => {
    setNav1(!nav1);
  };

  const handleNav2 = () => {
    setNav2(!nav2);
  };

  const handleNav3 = () => {
    setNav3(!nav3);
  };
  const handleNav4 = () => {
    setNav4(!nav4);
  };
  const handleNav5 = () => {
    setNav5(!nav5);
  };
  const handleNav7 = () => {
    setNav7(!nav7);
  };

  const tranlsate = (term: string, locale: string) => {
    if (locale === "en") {
      switch (term) {
        case "in_clinic_service":
          return "IN-CLINIC SERVICE";
        case "preventive_care":
          return "Preventive Care";
        case "primary_care":
          return "Primary Care";
        case "on_going_care":
          return "Chronic Diseases";
        case "wellness":
          return "Wellness";
        case "home_service":
          return "HOME SERVICE";
        case "home_visits":
          return "Home Visits";
        case "telemedicine":
          return "Telemedicine";
        case "services":
          return "Services";
        case "health_plans":
          return "Services";
        case "preventive_care_packages":
          return "Preventive Care";
        case "primary_care_packages":
          return "Primary Care";
        case "on_going_care_packages":
          return "Chronic Diseases";
        case "wellness_packages":
          return "Wellness";
        case "gene_examination_packages":
          return "Gene Examination";
        case "pharmacy":
          return "Pharmacy";
        case "monthly_packages":
          return "MONTHLY PACKAGES";
        case "elderly":
          return "Elderly (60+)";
        case "middle_aged_man":
          return "Middle-aged Man (45+)";
        case "middle_aged_woman":
          return "Middle-aged Woman (45+)";
        case "adult":
          return "Adult (18-45)";
        case "teenager":
          return "Teenager (13-19)";
        case "child":
          return "Child (6 - 12)";
        case "health_concern":
          return "HEALTH CONCERN";
        case "sleep":
          return "Sleep";
        case "smoking_cessation":
          return "Smoking Sessation";
        case "weight_loss":
          return "Weight Loss";
        case "skin_care_anti_aging":
          return "Skin Care";
        case "hair_nails_treatment":
          return "Hair";
        case "pregnancy_care":
          return "Pregnancy Care";
        case "men_sexual_health":
          return "Men Health";
        case "women_sexual_health":
          return "Women Health";
        case "heart_blood_circulation":
          return "Heart & Blood Circulation";
        case "digestive_system":
          return "Digestive System";
        case "bone_joint_health":
          return "Bone & Joint Health";
        case "immune_system":
          return "Immune System";
        case "brain_health":
          return "Brain Health";
        case "account":
          return "Account";
        case "profile":
          return "Personal Information";
        case "history":
          return "Past Medical Record";
        case "history2":
          return "Order History";
        case "detoxification_and_liver_function_enhancement":
          return "Detoxification And Liver Function Enhancement";
        case "gynecological_care":
          return "Gynecological Care";
        case "gastric_pain_or_stomachache":
          return "Gastric Pain Or Stomachache";
        case "lung_function_support":
          return "Lung Function Support";
        case "diabetes":
          return "Diabetes";
        case "gout":
          return "Gout Support";
        case "fatty_liver":
          return "Fatty Liver Support";
        case "stress":
          return "Stress Relief Support";
        case "benign_prostatic_hyperplasia":
          return "Benign Prostatic Hyperplasia (BPH)";
        case "eye_health":
          return "Eye health support";
        case "irritable_bowel_syndrome":
          return "Irritable Bowel Syndrome (IBS)";
        case "test_results":
          return "Test Results";
        case "booking_history":
          return "Booking History";
        case "gene_decoding_package":
          return "Gene Packages";
        case "echomedi":
          return "ECHO MEDI In Partnership With Genetica";
        case "gene_decoding":
          return "Gene Decoding";
        case "Pediatrics":
          return "Pediatrics";
        case "Pediatric_health_care_packages":
          return "Pediatric Healthcare"
        case "Pediatric_psychology_packages":
          return "Pediatric psychology"
        case "Mental_Health_Care_Packages":
          return "Mental Counselling";
        case "Adult_mental_health_care_packages":
          return "Adult Mental Healthcare"
        case "Pediatric_mental_health_care_packages":
          return "Children And Adolescent Mental Health"
        case "wellness_adult":
          return "Wellness Care (Adult)"
        case "wellness_children":
          return "Adolescent Psychological Counselling (Children)"
      }
    } else {
      switch (term) {
        case "wellness_adult":
          return "Chăm sóc sức khỏe toàn diện (Người lớn)"
        case "wellness_children":
          return "Tham vấn tâm lý cho trẻ vị thành niên (Trẻ em)"
        case "Pediatric_mental_health_care_packages":
          return "Tâm lý trẻ em và vị thành niên"
        case "Adult_mental_health_care_packages":
          return "Tâm lý người lớn"
        case "Mental_Health_Care_Packages":
          return "Tham vấn tâm lý";
        case "Pediatric_psychology_packages":
          return "Chăm sóc tâm lý Nhi"
        case "Pediatric_health_care_packages":
          return "Chăm sóc sức khỏe Nhi"
        case "Pediatrics":
          return "Nhi khoa";
        case "in_clinic_service":
          return "DỊCH VỤ TẠI PHÒNG KHÁM";
        case "preventive_care":
          return "Chăm Sóc Phòng Ngừa";
        case "primary_care":
          return "Điều Trị Ban Đầu";
        case "on_going_care":
          return "Quản Lý Bệnh Mạn Tính";
        case "wellness":
          return "Sức Khoẻ Toàn Diện";
        case "home_service":
          return "DỊCH VỤ TẠI NHÀ";
        case "home_visits":
          return "Chăm sóc tại nhà";
        case "telemedicine":
          return "Chăm sóc từ xa - Việt kiều nước ngoài";
        case "services":
          return "Các dịch vụ";
        case "health_plans":
          return "Dịch vụ";
        case "preventive_care_packages":
          return "Chăm Sóc Phòng Ngừa";
        case "primary_care_packages":
          return "Điều Trị Ban Đầu";
        case "on_going_care_packages":
          return "Quản Lý Bệnh Mạn Tính";
        case "wellness_packages":
          return "Sức Khoẻ Toàn Diện";
        case "gene_examination_packages":
          return "Gói xét nghiệm di truyền";
        case "pharmacy":
          return "Nhà thuốc";
        case "monthly_packages":
          return "GÓI CHĂM SÓC THÁNG";
        case "elderly":
          return "Người lớn tuổi (Trên 60 tuổi)";
        case "middle_aged_man":
          return "Nam trung niên (Trên 45 tuổi)";
        case "middle_aged_woman":
          return "Nữ trung niên (Trên 45 tuổi)";
        case "adult":
          return "Người trưởng thành (18 - 45 tuổi)";
        case "teenager":
          return "Thanh thiếu niên (13 - 19 tuổi)";
        case "child":
          return "Trẻ em (6 - 12 tuổi)";
        case "health_concern":
          return "VẤN ĐỀ SỨC KHOẺ";
        case "sleep":
          return "Ngủ ngon";
        case "smoking_cessation":
          return "Cai thuốc lá";
        case "weight_loss":
          return "Giảm cân";
        case "skin_care_anti_aging":
          return "Chăm sóc da";
        case "hair_nails_treatment":
          return "Chăm sóc tóc";
        case "pregnancy_care":
          return "Mang thai";
        case "men_sexual_health":
          return "Sinh lý nam";
        case "women_sexual_health":
          return "Sinh lý nữ";
        case "heart_blood_circulation":
          return "Tim mạch";
        case "digestive_system":
          return "Tiêu hóa";
        case "bone_joint_health":
          return "Xương khớp";
        case "immune_system":
          return "Đề kháng và miễn dịch";
        case "brain_health":
          return "Tinh thần & trí não";
        case "account":
          return "Tài khoản";
        case "profile":
          return "Thông tin cá nhân";
        case "history":
          return "Lịch sử bệnh án";
        case "history2":
          return "Lịch sử mua hàng";
        case "test_results":
          return "Kết quả xét nghiệm";
        case "booking_history":
          return "Lịch sử đặt hẹn";
        case "detoxification_and_liver_function_enhancement":
          return "Giải độc và tăng cường chức năng gan";
        case "gynecological_care":
          return "Chăm sóc phụ khoa";
        case "gastric_pain_or_stomachache":
          return "Đau dạ dày";
        case "lung_function_support":
          return "Hỗ trợ chức năng phổi";
        case "diabetes":
          return "Bệnh tiểu đường";
        case "gout":
          return "Hỗ trợ bệnh Gout";
        case "fatty_liver":
          return "Hỗ trợ gan nhiễm mỡ";
        case "stress":
          return "Hỗ trợ giảm căng thẳng, stress";
        case "benign_prostatic_hyperplasia":
          return "Hỗ trợ bệnh phì đại tuyến tiền liệt";
        case "eye_health":
          return "Bổ mắt";
        case "irritable_bowel_syndrome":
          return "Hỗ trợ bệnh đại tràng";
        case "gene_decoding_package":
          return "Gói giải mã gen"
        case "echomedi":
          return "ECHO MEDI đồng hành cùng Genetica";
        case "gene_decoding":
          return "Giải mã gen"
      }
    }
    return term;
  };

  useEffect(() => {
    setTextColor("#000000");
  }, []);
  const [isHovered, setIsHovered] = useState(false);
  const [name, setName] = useState("");
  const [bd, setBD] = useState<Date | null>(null);
  const [bookingDate, setBookingDate] = useState(moment().format('YYYY-MM-DD'));
  const [timeSlot, setTimeSlot] = useState("");
  const [gender, setGender] = useState("male");
  const [address, setAddress] = useState("");
  const [phone_number, setPhoneNumber] = useState("");
  const [phone_number_warning_msg, setPhoneNumberWarningMsg] = useState("");
  const [branch, setBranch] = useState("q7");
  const [selectedOption, setSelectedOption] = useState('clinic');
  const [message, setMessage] = useState("");

  const handleOptionChange = (event: any) => {
    const value = event.target.value;
    setSelectedOption(value);
    setMessage(value === 'clinic' ? "Khách hàng đặt lịch khám ở phòng khám" : "Khách hàng đặt lịch khám tại nhà");
  };

  const isPastDate = (date: string) => {
    return dayjs(date).isBefore(dayjs().startOf("day"));
  };

  dayjs.locale(locale);

  const bookingSlots = useMemo(() => {
    let slots = [];
    const startTime = 9;
    let endTime = 19; // Kết thúc ở 19:00
    if (dayjs(bookingDate).day() === 0) {
      endTime = 14;
    }
    for (let i = startTime; i <= endTime; i++) {
      let slot = dayjs(bookingDate).set("hour", i).set("minute", 0);
      if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
        slots.push(slot);
      }

      if (i < endTime) {
        slot = dayjs(bookingDate).set("hour", i).set("minute", 30);
        if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
          slots.push(slot);
        }
      }
    }

    if (slots?.length) {
      setTimeSlot(slots[0].toISOString());
    }

    return slots;
  }, [bookingDate]);

  const handleBooking = () => {
    if (phone_number == "" || !validatePhone(phone_number)) {
      setPhoneNumberWarningMsg(locale == "vi" ? "Yêu cầu nhập số điện thoại hợp lệ." : "Please enter your phone number.")
      toast.error("Đặt lịch không thành công");
      return;
    }
    const payload = {
      data: {
        createNewPatient: true,
        full_name: name,
        contactFullName: name,
        gender,
        email,
        contactEmail: email,
        phone: phone_number,
        contactPhoneNumber: phone_number,
        message,
        birthday: bd ? dayjs(bd).toISOString() : null,
        address: {
          address
        },
        contactAddress: address,
        branch,
        bookingDate: timeSlot,
        note: message,
      }
    };

    axios
      .post("https://api.echomedi.com/api/bookings/createBookingFromWeb", payload)
      .then(function (response) {
        toast.success("Đặt lịch thành công");
        location.href = "/booking_detail/?code=" + response.data.booking.id;
      })
      .catch(function (error) {
        toast.error("Đặt lịch không thành công");
      });
  };
  function validatePhone(phone: string) {
    return /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
  }


  const [showModalForgotPassword, setShowModalFotgotPassword] = useState(false);
  const [showModalPhone, setShowModalPhone] = useState(false);
  const [showModalPass, setShowModalPass] = useState(false);
  const [showModalSuccess, setShowModalSuccess] = useState(false);
  const [newpassword, setNewPassword] = useState("");
  const [otp, setOtp] = useState('');
  const [countdown, setCountdown] = useState(90);
  const [resendDisabled, setResendDisabled] = useState(false);
  useEffect(() => {
    if (countdown === 0) {
      setResendDisabled(false);
      return;
    }

    const timer = setTimeout(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, 1000);

    setResendDisabled(true);

    return () => clearTimeout(timer);
  }, [countdown]);

  const handleResendOTP = async () => {
    setCountdown(90);
    await axios.post('https://api.echomedi.com/api/resend-otp', {
      "phone": phone_number,
      "otp": otp,
    })
      .then(function (response) {
        if (response.data.userExist === true) {
          toast.error(locale == "vi" ? "Số điện thoại đã được đăng ký." : "Phone number registered")
        } else {
          toast.success(locale == "vi" ? 'Bạn đã xác thực thành công, vui lòng đăng nhập!' : "You have successfully authenticated, please log in!");
          location.href = "/login"
        }
      })
      .catch(function (error) {
        toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
      });
  };
  const forgotpassword = () => {
    if (password == "" || newpassword == "") {
      toast.error("Thông tin không phù hợp")
    } else {
      const toastId = toast.loading('Loading...');
      axios
        .post('https://api.echomedi.com/api/user/resetPassword', {
          code: otp,
          password: password,
          passwordConfirmation: newpassword
        })
        .then(response => {
          toast.success('Thay đổi mật khẩu thành công');
          setShowModalFotgotPassword(false);
          setShowModalSuccess(true);
        })
        .catch(error => {
          toast.error("Không thể thay đổi mật khẩu. Vui lòng kiểm tra lại quá trình đổi mật khẩu")
        })
        .finally(() => {
          toast.dismiss(toastId);
        });;
    }

  }
  const verifyOTP = async () => {
    await axios.post('https://api.echomedi.com/api/user/verifyPhoneOTP', {
      "phone": phone_number,
      "otp": otp,
    })
      .then(async function (response) {
        const ok = response.data.ok;
        if (!ok) {
          toast.error(locale == "vi" ? "Mã OTP không chính xác." : "OTP is not valid.")
        } else {
          toast.success(locale == "vi" ? 'Bạn đã xác thực thành công, vui lòng đăng nhập!' : "You have successfully authenticated, please log in!");
        }
      })
      .catch(function (error) {
        toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
      });
    setShowModalPass(false);
    setShowModalFotgotPassword(true)
  }
  const handleSendPhone = async () => {
    axios.post('https://api.echomedi.com/api/auth/forgotPassword', {
      email: phone_number
    });
    setShowModalPhone(false);
    setShowModalPass(true);
  }
  const handleBack = () => {
    setShowModalPhone(false);
    setShowModalLogin(true);
  }
  const handleBackToPhone = () => {
    setShowModalFotgotPassword(false);
    setShowModalPass(true);
  }
  const handleCloseModaLogin = () => {
    setShowModalLogin(false);
    setShowModalSuccess(false);
    setShowModalPhone(true);
  }
  const handleOpenModaLogin = () => {
    setShowModalLogin(true);
    setShowModalSuccess(false);
  }
  return (
    <>
      <nav
        style={{
          position: 'sticky',
          top: 0,
          width: '100%',
          background: '#EDF1F6',
          zIndex: isHovered ? 999 : 990,
          transition: 'z-index',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className="hidden md:block">
        <div className="flex items-center justify-between mx-16">
          <ul className="flex items-center gap-4">
            <li className="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="#8E8E8E" className="h-4 w-4" viewBox="0 0 24 24"><title>phone</title><path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z" /></svg>
              <a href="tel:1900638408" className="text-[#8E8E8E] text-xs">1900 638 408</a>
            </li>
            <li className="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="#8E8E8E" className="h-4 w-4" viewBox="0 0 24 24"><title>gmail</title><path d="M20,18H18V9.25L12,13L6,9.25V18H4V6H5.2L12,10.25L18.8,6H20M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z" /></svg>
              <span className="text-[#8E8E8E] text-xs"><EMAIL></span>
            </li>
            <li className="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="#8E8E8E" className="h-4 w-4" viewBox="0 0 24 24"><title>office-building</title><path d="M5,3V21H11V17.5H13V21H19V3H5M7,5H9V7H7V5M11,5H13V7H11V5M15,5H17V7H15V5M7,9H9V11H7V9M11,9H13V11H11V9M15,9H17V11H15V9M7,13H9V15H7V13M11,13H13V15H11V13M15,13H17V15H15V13M7,17H9V19H7V17M15,17H17V19H15V17Z" /></svg>
              <span className="text-[#8E8E8E] text-xs"> {locale === "en" ? "Ho Chi Minh City - Thu Duc - Binh Duong" : "TP. Hồ Chí Minh - Thủ Đức - Bình Dương"}</span>
            </li>
          </ul>
          <ul className="flex items-center gap-4">
            <li>
              <button onClick={e => setShowModal2(true)} className="flex items-center justify-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="#156634" className="h-4 w-4" viewBox="0 0 24 24"><title>tray-arrow-down</title><path d="M2 12H4V17H20V12H22V17C22 18.11 21.11 19 20 19H4C2.9 19 2 18.11 2 17V12M12 15L17.55 9.54L16.13 8.13L13 11.25V2H11V11.25L7.88 8.13L6.46 9.55L12 15Z" /></svg>
                <span onClick={handleNav} className="text-[#8E8E8E] text-xs">
                  {locale === "en" ? "Download App" : "Tải ứng dụng"}
                </span>
              </button>
              <Modal
                showCloseButton
                visibleModal={showModal2}
                wrapperClassName="!w-[450px]"
                contentClassName="!min-h-[0]" onClose={() => setShowModal2(false)}
              >
                <p className="text-2xl text-center mb-4 font-bold mt-2 text-green-900">{locale === "en" ? "Download app" : "Tải ứng dụng"}</p>
                <p style={{ width: "360px" }}
                  className="text-sm text-center mx-auto">{locale === "en" ? "Book an appointment at home, get a doctor's consultation, access medical records at home - All in the ECHOMEDI app" : "Đặt lịch khám tại nhà, nhận tư vấn của bác sĩ, truy cập hồ sơ y tế ngay tại nhà - Tất cả đã có trong ứng dụng ECHOMEDI."}</p>
                <div className="flex grid-cols-2 mt-5 mb-2">
                  <div className="col-span-1 mr-2 ml-4">
                    <a href="https://play.google.com/store/apps/details?id=com.echomedi.echomedi&hl=vi" target="blank"><img src="https://d3e4m6b6rxmux9.cloudfront.net/Group_7_1_64b787caad.png?updated_at=2023-04-27T05:38:32.266Z" className="rounded-3xl" /></a>
                  </div>
                  <div className="col-span-1 ml-2 mr-4">
                    <a href="https://apps.apple.com/vn/app/echo-medi/id6448538294" target="blank"><img src="https://d3e4m6b6rxmux9.cloudfront.net/Group_8_a88a0842a8.png?updated_at=2023-04-27T05:42:00.091Z" className="rounded-3xl" /></a>
                  </div>
                </div>
              </Modal>
            </li>
            <li>
              <div className="z-10 bg-transparent group-hover:block flex ml-1">
                <div className="text-black bg-transparent my-auto">
                  <div className="flex">
                    <div className="my-auto">
                      <div className="group inline-block relative">
                        <button
                          className="border-2 mt-1 px-1  border-grey-900 rounded-lg bg-white"
                        >
                          {locale == "vi" ?
                            <div className="flex">
                              <svg width="20px" height="20px" viewBox="0 -7 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clipPath="url(#clip0_503_2795)">
                                  <rect width="28" height="20" rx="2" fill="white" />
                                  <mask id="mask0_503_2795" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                    <rect width="28" height="20" rx="2" fill="white" />
                                  </mask>
                                  <g mask="url(#mask0_503_2795)">
                                    <rect width="28" height="20" fill="#EA403F" />
                                    <path fillRule="evenodd" clipRule="evenodd" d="M14 12.34L10.4733 14.8541L11.7745 10.7231L8.29366 8.1459L12.6246 8.1069L14 4L15.3754 8.1069L19.7063 8.1459L16.2255 10.7231L17.5267 14.8541L14 12.34Z" fill="#FFFE4E" />
                                  </g>
                                </g>
                                <defs>
                                  <clipPath id="clip0_503_2795">
                                    <rect width="28" height="20" rx="2" fill="white" />
                                  </clipPath>
                                </defs>
                              </svg>&ensp;
                              <p className="text-sm my-auto">VI</p>
                              <p className="mt-[2.5px]"><svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <rect x="0" fill="none" width="24" height="24" />
                                <g>
                                  <path d="M7 10l5 5 5-5" />
                                </g>
                              </svg>
                              </p>
                            </div> :
                            <div className="flex">
                              <svg width="20px" height="20px" viewBox="0 -7 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clipPath="url(#clip0_503_2952)">
                                  <rect width="28" height="20" rx="2" fill="white" />
                                  <mask id="mask0_503_2952" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                    <rect width="28" height="20" rx="2" fill="white" />
                                  </mask>
                                  <g mask="url(#mask0_503_2952)">
                                    <rect width="28" height="20" fill="#0A17A7" />
                                    <path fillRule="evenodd" clipRule="evenodd" d="M-1.28244 -1.91644L10.6667 6.14335V-1.33333H17.3334V6.14335L29.2825 -1.91644L30.7737 0.294324L21.3263 6.66667H28V13.3333H21.3263L30.7737 19.7057L29.2825 21.9165L17.3334 13.8567V21.3333H10.6667V13.8567L-1.28244 21.9165L-2.77362 19.7057L6.67377 13.3333H2.95639e-05V6.66667H6.67377L-2.77362 0.294324L-1.28244 -1.91644Z" fill="white" />
                                    <path d="M18.668 6.33219L31.3333 -2" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                    <path d="M20.0128 13.6975L31.3666 21.3503" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                    <path d="M8.00555 6.31046L-3.83746 -1.67099" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                    <path d="M9.29006 13.6049L-3.83746 22.3105" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                    <path fillRule="evenodd" clipRule="evenodd" d="M0 12H12V20H16V12H28V8H16V0H12V8H0V12Z" fill="#E6273E" />
                                  </g>
                                </g>
                                <defs>
                                  <clipPath id="clip0_503_2952">
                                    <rect width="28" height="20" rx="2" fill="white" />
                                  </clipPath>
                                </defs>
                              </svg>&ensp;
                              <p className="text-sm my-auto">EN</p>
                              <p className="mt-[2.5px]"><svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <rect x="0" fill="none" width="24" height="24" />
                                <g>
                                  <path d="M7 10l5 5 5-5" />
                                </g>
                              </svg></p>
                            </div>
                          }
                        </button>
                        <ul className="absolute hidden text-gray-700 group-hover:block -mt-1">
                          <li className="">
                            <button className="border-2 mt-1 px-1 rounded-sm border-grey-900 !rounded-lg bg-white pt-1"

                              onClick={() => {
                                let href = router.asPath;
                                let pName = router.pathname;
                                Object.keys(router.query).forEach((k) => {
                                  if (k === "locale") {
                                    pName = pName.replace(
                                      `[${k}]`,
                                      locale == "en" ? "vi" : "en"
                                    );
                                    return;
                                  }
                                  pName = pName.replace(`[${k}]`, router.query[k] as string);
                                });

                                if (pName != "/en" && pName != "/vi" && !pName.startsWith('/vi/') && !pName.startsWith('/en/')) {
                                  if (pName.startsWith('/')) {
                                    pName = '/en' + pName;
                                  } else {
                                    pName = '/en/' + pName;
                                  }
                                }

                                location.href = pName;
                                // const newLocale = locale === "en" ? "vi" : "en";
                                // let newPathname = router.pathname;
                                // newPathname = newPathname.replace('[locale]', newLocale);
                                // Object.keys(router.query).forEach((key) => {
                                //   if (key !== 'locale' && key !== 'categoryURL') {
                                //     newPathname = newPathname.replace(`[${key}]`, router.query[key] as string);
                                //   }
                                // });
                                // if (!newPathname.startsWith(`/${newLocale}`)) {
                                //   newPathname = `/${newLocale}${newPathname.startsWith('/') ? '' : '/'}${newPathname}`;
                                // }
                                // const newQuery = new URLSearchParams();
                                // if (router.query.categoryURL) {
                                //   newQuery.set('categoryURL', router.query.categoryURL as string);
                                // }
                                // const newUrl = `${newPathname}${newQuery.toString() ? `?${newQuery.toString()}` : ''}`;
                                // window.location.href = newUrl;
                              }}>
                              {locale == "vi" ?
                                <div className="flex w-[62.5px] px-">
                                  <svg width="20px" height="20px" viewBox="0 -4 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clipPath="url(#clip0_503_2952)">
                                      <rect width="28" height="20" rx="2" fill="white" />
                                      <mask id="mask0_503_2952" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                        <rect width="28" height="20" rx="2" fill="white" />
                                      </mask>
                                      <g mask="url(#mask0_503_2952)">
                                        <rect width="28" height="20" fill="#0A17A7" />
                                        <path fillRule="evenodd" clipRule="evenodd" d="M-1.28244 -1.91644L10.6667 6.14335V-1.33333H17.3334V6.14335L29.2825 -1.91644L30.7737 0.294324L21.3263 6.66667H28V13.3333H21.3263L30.7737 19.7057L29.2825 21.9165L17.3334 13.8567V21.3333H10.6667V13.8567L-1.28244 21.9165L-2.77362 19.7057L6.67377 13.3333H2.95639e-05V6.66667H6.67377L-2.77362 0.294324L-1.28244 -1.91644Z" fill="white" />
                                        <path d="M18.668 6.33219L31.3333 -2" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                        <path d="M20.0128 13.6975L31.3666 21.3503" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                        <path d="M8.00555 6.31046L-3.83746 -1.67099" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                        <path d="M9.29006 13.6049L-3.83746 22.3105" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                        <path fillRule="evenodd" clipRule="evenodd" d="M0 12H12V20H16V12H28V8H16V0H12V8H0V12Z" fill="#E6273E" />
                                      </g>
                                    </g>
                                    <defs>
                                      <clipPath id="clip0_503_2952">
                                        <rect width="28" height="20" rx="2" fill="white" />
                                      </clipPath>
                                    </defs>
                                  </svg>&ensp;
                                  <p className="text-xs my-auto">EN</p>
                                </div> :
                                <div className="flex w-[68.5px]">
                                  <svg width="20px" height="20px" viewBox="0 -4 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clipPath="url(#clip0_503_2795)">
                                      <rect width="28" height="20" rx="2" fill="white" />
                                      <mask id="mask0_503_2795" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                        <rect width="28" height="20" rx="2" fill="white" />
                                      </mask>
                                      <g mask="url(#mask0_503_2795)">
                                        <rect width="28" height="20" fill="#EA403F" />
                                        <path fillRule="evenodd" clipRule="evenodd" d="M14 12.34L10.4733 14.8541L11.7745 10.7231L8.29366 8.1459L12.6246 8.1069L14 4L15.3754 8.1069L19.7063 8.1459L16.2255 10.7231L17.5267 14.8541L14 12.34Z" fill="#FFFE4E" />
                                      </g>
                                    </g>
                                    <defs>
                                      <clipPath id="clip0_503_2795">
                                        <rect width="28" height="20" rx="2" fill="white" />
                                      </clipPath>
                                    </defs>
                                  </svg>&ensp;
                                  <p className="text-xs my-auto">VI</p>
                                </div>
                              }
                            </button>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nav>

      <nav
        style={{
          position: "sticky",
          top: 0,
          width: "100%",
          background: "white",
          zIndex: 990,
        }}
        className="flex items-center justify-between md:px-14">
        <div className="flex sm:hidden">
          <LinkComponent href={"/"} locale={locale} skipLocaleHandling={false}>
            <Image
              placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(144, 44))}`}
              className="w-36 max-h-12"
              width={144}
              height={44}
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />
          </LinkComponent>
        </div>
        <ul className="hidden sm:flex justify-start">
          <div className="flex">
            <LinkComponent href={"/"} locale={locale} skipLocaleHandling={false}>
              <Image
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(144, 44))}`}
                className="w-36 max-h-12"
                width={144}
                height={44}
                alt="ECHO MEDI"
                src={getLogoUrl(locale)}
              />
            </LinkComponent>
          </div>
        </ul>
        <ul
          className="hidden sm:flex"
        >
          <div>
            <ul className="flex">

              <li>
                <p className="menu-item">
                  <LinkComponent locale={locale} skipLocaleHandling={false} href={"/about/"}>
                    <span
                      className="hover:font-bold h-full flex items-center m-auto text-sm hover:underline hover:text-[#156634] px-2 py-3 hover:cursor-pointer rounded ">
                      {locale === "en" ? "About Us" : "Về chúng tôi"}
                    </span>
                  </LinkComponent>
                </p>
              </li>
              <li className="group relative">
                <p className="menu-item group-hover:border-white ">
                  <span
                    className="hover:font-bold h-full flex items-center m-auto text-sm hover:underline hover:text-[#156634] px-2 py-3 hover:cursor-pointer rounded ">
                    {tranlsate("health_plans", locale)}
                  </span>
                </p>
                <ul className="absolute bg-white left-0 w-max mt-14 opacity-0 invisible group-hover:opacity-100 group-hover:visible group-hover:mt-0 transition-all duration-500">
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/goi_cham_soc/goi-cham-soc-phong-ngua"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {tranlsate("preventive_care_packages", locale)}
                      </LinkComponent>
                    </p>
                  </li>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/initial_treatment/goi-dieu-tri-ban-dau"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {tranlsate("primary_care_packages", locale)}
                      </LinkComponent>
                    </p>
                  </li>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/chronicdiseases/goi-quan-ly-benh-man-tinh"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {tranlsate("on_going_care_packages", locale)}
                      </LinkComponent>
                    </p>
                  </li>
                  {/* <li className="sub-dropdown relative">
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/comprehensive_health/goi-suc-khoe-toan-dien/"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {tranlsate("wellness_packages", locale)}
                      </LinkComponent>
                    </p>
                  </li> */}
                  {/**/}
                  {/* <div className="border-b-[0.75px] my-1"></div> */}
                  {/* <li className="sub-dropdown relative">
                    <p className="menu-sub-item">
                      <span className="flex items-center justify-between">
                        {tranlsate("Mental_Health_Care_Packages", locale)}
                        <svg
                          className="ml-4 h-3 w-3 cursor-pointer -rotate-90"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 512 512"
                          fill="black"
                        >
                          <path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z" />
                        </svg>
                      </span>
                    </p>
                    <ul className="sub-dropdown-content absolute left-full top-full bg-white w-max opacity-0 invisible transition-all duration-500">
                      <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/goi_tam_ly/goi-tam-ly-nguoi-lon/"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("Adult_mental_health_care_packages", locale)}
                          </LinkComponent>
                        </p>
                      </li>
                      <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/goi_tam_ly/tam-ly-tre-em/"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("Pediatric_mental_health_care_packages", locale)}
                          </LinkComponent>
                        </p>
                      </li>
                    </ul>
                  </li> */}
                  <div className="border-b-[0.75px] my-1"></div>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/goi_suc_khoe_nhi/goi-cham-soc-suc-khoe-nhi"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {tranlsate("Pediatric_health_care_packages", locale)}
                      </LinkComponent>
                    </p>
                  </li>
                  {/* <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/echomedi_gen/echomedi-gen"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {tranlsate("gene_decoding", locale)}
                      </LinkComponent>
                    </p>
                  </li> */}
                </ul>
              </li>
              <li className="group">
                <p className="menu-item group-hover:border-white hover:underline">
                  <LinkComponent
                    href={"/store"}
                    locale={locale}
                    skipLocaleHandling={false}
                  >
                    <span
                      className="hover:font-bold hover:text-[#156634] h-full flex items-center m-auto text-sm hover:underline px-2 py-3 hover:cursor-pointer rounded ">
                      {tranlsate("pharmacy", locale)}
                    </span>
                  </LinkComponent>
                </p>
                <div className="grid grid-cols-4 w-full p-5 absolute top-full left-0 bg-white text-black mt-14 opacity-0 invisible group-hover:opacity-100 group-hover:visible group-hover:mt-0 transition-all duration-500">
                  <ul className="p-2">
                    <li>
                      <p className="mega-sub-item-title text-sm underline underline-offset-8 mb-4 font-bold text-black">
                        {tranlsate("monthly_packages", locale ? locale : "")}
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-nguoi-lon-tuoi-tuoi-60"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("elderly", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-nam-gioi-do-tuoi-trung-nien-tuoi-45"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("middle_aged_man", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-nu-gioi-do-tuoi-trung-nien-tuoi-45"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("middle_aged_woman", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-nguoi-truong-thanh-tuoi-18-45"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("adult", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-thanh-thieu-nien-tuoi-13-19"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("teenager", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-tre-em-tuoi-6-12"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("child", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                  </ul>
                  <ul className="p-2">
                    <li>
                      <p className="mega-sub-item-title text-sm underline underline-offset-8 mb-4 font-bold text-black">
                        {tranlsate("health_concern", locale)}
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-giac-ngu"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("sleep", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-cai-thuoc-la"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("smoking_cessation", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-giam-can"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("weight_loss", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-da-va-ngan-ngua-lao-hoa"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("skin_care_anti_aging", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-cham-soc-va-phuc-hoi-toc-mong"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("hair_nails_treatment", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={
                            "/products/goi-cham-soc-suc-khoe-cho-phu-nu-mang-thai"
                          }
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("pregnancy_care", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-suc-khoe-sinh-ly-nam"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("men_sexual_health", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-suc-khoe-sinh-ly-nu"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("women_sexual_health", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                  </ul>
                  <ul className="p-2">
                    <li>
                      <p className="mega-sub-item-title">
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-suc-khoe-tim-mach"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("heart_blood_circulation", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-tieu-hoa"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("digestive_system", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-phong-ngua-benh-xuong-khop"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("bone_joint_health", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-tang-suc-de-khang-va-mien-dich"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("immune_system", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-cai-thien-tri-nao"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("brain_health", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-giai-doc-tang-cuong-chuc-nang-gan"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("detoxification_and_liver_function_enhancement", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-cham-soc-phu-khoa"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("gynecological_care", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-benh-dau-da-day"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("gastric_pain_or_stomachache", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                  </ul>
                  <ul className="p-2">
                    <li>
                      <p className="mega-sub-item-title">
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-tang-cuong-chuc-nang-phoi"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("lung_function_support", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-benh-tieu-duong"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("diabetes", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-benh-gout"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("gout", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-benh-gan-nhiem-mo"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("fatty_liver", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-giam-cang-thang-stress"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("stress", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-benh-phi-dai-tuyen-tien-liet"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("benign_prostatic_hyperplasia", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-cham-soc-suc-khoe-doi-mat"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("eye_health", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                    <li>
                      <p className="mega-sub-item">
                        <LinkComponent
                          href={"/products/goi-ho-tro-benh-dai-trang"}
                          locale={locale}
                          skipLocaleHandling={false}
                        >
                          {tranlsate("irritable_bowel_syndrome", locale)}
                        </LinkComponent>
                      </p>
                    </li>
                  </ul>
                </div>
              </li>
              {/* <li>
                <p className="menu-item">
                  <LinkComponent locale={locale} skipLocaleHandling={false} href="/membership">
                    <span
                      className="hover:font-bold hover:text-[#156634] h-full flex items-center m-auto text-sm hover:underline py-3 hover:cursor-pointer rounded ">
                      {locale === "en" ? "Membership" : "Thành viên"}
                    </span>
                  </LinkComponent>
                </p>
              </li>
              <li className="group relative">
                <p className="menu-item group-hover:border-white ">
                  <span
                    className="hover:font-bold h-full flex items-center m-auto text-sm hover:underline hover:text-[#156634] px-2 py-3 hover:cursor-pointer rounded ">
                    {locale === "en" ? "Corporate Wellness" : "Doanh nghiệp"}
                  </span>
                </p>
                <ul className="absolute bg-white left-0 w-max mt-14 opacity-0 invisible group-hover:opacity-100 group-hover:visible group-hover:mt-0 transition-all duration-500">
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/corporate"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {locale === "en" ? "Corporate Healthcare" : "Chăm sóc doanh nghiệp"}
                      </LinkComponent>
                    </p>
                  </li>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/healthtalks"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {locale === "en" ? "Health Talks and Workshops" : "Tọa đàm sức khỏe"}
                      </LinkComponent>
                    </p>
                  </li>
                </ul>
              </li> */}

              {/* <li className="group relative">
                <p className="menu-item">
                  <LinkComponent
                    href={"/articles_preventive_care"}
                    locale={locale}
                    skipLocaleHandling={false}
                  >
                    <span
                      className="hover:font-bold hover:text-[#156634] h-full flex items-center m-auto text-sm hover:underline px-2 py-3 hover:cursor-pointer rounded ">
                      {locale === "en" ? "FAQs" : "Thông tin & Hỏi đáp"}
                    </span>
                  </LinkComponent>
                </p>
                <ul className="absolute left-0 bg-white text-black w-max mt-14 opacity-0 invisible group-hover:opacity-100 group-hover:visible group-hover:mt-0 transition-all duration-500">
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/articles_preventive_care"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {locale == "vi" ? "Bài viết sức khoẻ" : "Healthcare Articles"}
                      </LinkComponent>
                    </p>
                  </li>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/articles_news"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {locale == "vi" ? "Tin tức, Hoạt động và Ưu đãi" : "News, Events & Special Offers"}
                      </LinkComponent>
                    </p>
                  </li>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/video_short"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        Videos
                      </LinkComponent>
                    </p>
                  </li>
                  <li>
                    <p className="menu-sub-item">
                      <LinkComponent
                        href={"/question"}
                        locale={locale}
                        skipLocaleHandling={false}
                      >
                        {locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}
                      </LinkComponent>
                    </p>
                  </li>
                </ul>
              </li> */}

              {/* <li>
                <li>
                  <p className="menu-item">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/cart">
                      {numOfItem > -1 ? (<div className={`w-[18px] h-[18px] bg-red-600 text-center items-center justify-center ml-6 rounded-3xl absolute ${(numOfItem > 0) ? 'block' : 'invisible'}`}>
                        <p className={`num-of-item text-white text-xs leading-[20px]`}>{numOfItem}</p></div>)
                        : (<div className="w-[18px] h-[18px] text-center items-center justify-center ml-6 rounded-3xl">
                        </div>)
                      }
                      <svg className="mx-3 my-2 rotate-360" fill="#000000" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                        width="25px" height="25px" viewBox="0 0 902.86 902.86"
                        xmlSpace="preserve">
                        <g>
                          <g>
                            <path d="M671.504,577.829l110.485-432.609H902.86v-68H729.174L703.128,179.2L0,178.697l74.753,399.129h596.751V577.829z
			 M685.766,247.188l-67.077,262.64H131.199L81.928,246.756L685.766,247.188z"/>
                            <path d="M578.418,825.641c59.961,0,108.743-48.783,108.743-108.744s-48.782-108.742-108.743-108.742H168.717
			c-59.961,0-108.744,48.781-108.744,108.742s48.782,108.744,108.744,108.744c59.962,0,108.743-48.783,108.743-108.744
			c0-14.4-2.821-28.152-7.927-40.742h208.069c-5.107,12.59-7.928,26.342-7.928,40.742
			C469.675,776.858,518.457,825.641,578.418,825.641z M209.46,716.897c0,22.467-18.277,40.744-40.743,40.744
			c-22.466,0-40.744-18.277-40.744-40.744c0-22.465,18.277-40.742,40.744-40.742C191.183,676.155,209.46,694.432,209.46,716.897z
			 M619.162,716.897c0,22.467-18.277,40.744-40.743,40.744s-40.743-18.277-40.743-40.744c0-22.465,18.277-40.742,40.743-40.742
			S619.162,694.432,619.162,716.897z"/>
                          </g>
                        </g>
                      </svg>
                    </LinkComponent>
                  </p>
                </li>
              </li> */}

              <span className="border-r-[1px] h-6 my-auto mr-2"></span>
              {!logged && (
                <li>
                  <p className="menu-item">
                    <button onClick={e => setShowModalLogin(true)}
                      className="hover:font-bold hover:text-[#156634] h-full flex items-center m-auto text-sm hover:underline py-3 hover:cursor-pointer rounded ">
                      {locale === "en" ? "Sign in" : "Đăng nhập"}
                    </button>
                  </p>
                  <Modal
                    showCloseButton
                    visibleModal={showModalLogin}
                    wrapperClassName="!w-[500px]"
                    contentClassName="!min-h-[0]" onClose={() => setShowModalLogin(false)}
                  >
                    <div className="w-full rounded-lg md:mt-0 sm:max-w-md xl:p-0">
                      <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
                        <h1 className="text-sm font-bold leading-tight tracking-tight text-[#14532D] md:text-2xl">
                          {locale === "en" ? "Sign in" : "Đăng nhập"}
                        </h1>
                        <div>
                          <label htmlFor="email" className="block  mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Email or phone number" : "Email hoặc số điện thoại"}</label>
                          <input
                            id="exampleFormControlInput1"
                            onChange={(e) => { setEmail(e.target.value) }}
                            type="text" name="email" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                        </div>
                        <div className="relative">
                          <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Password" : "Mật khẩu"}</label>
                          <input
                            id="exampleFormControlInput1"
                            onChange={(e) => { setPassword(e.target.value) }}
                            type={showPassword ? 'text' : 'password'}
                            name="password" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                          <div
                            className="absolute right-0 top-1/2 mt-2 mr-3 flex items-center cursor-pointer"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <>
                                <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                                </svg>
                              </>
                            ) : (
                              <>
                                <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                                </svg>
                              </>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-start">
                            <div className="flex items-center h-4">
                              <input id="remember" aria-describedby="remember" type="checkbox" className="w-3 h-3 border border-gray-300 rounded" />
                            </div>
                            <div className="ml-3 text-xs">
                              <label htmlFor="remember" className="text-gray-500 font-bold">{locale === "en" ? "Remember me" : "Ghi nhớ đăng nhập"}</label>
                            </div>
                          </div>
                        </div>
                        <button type="button" onClick={login} className="w-full text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">{locale === "en" ? "Sign in" : "Đăng nhập"}</button>
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-light text-gray-500">
                            {locale === "en" ? "Don’t have an account yet? " : "Bạn chưa có tài khoản? "}<a href={"/signup"}><button className="font-medium text-green-800 hover:underline">{locale === "en" ? "Sign up" : "Đăng ký"}</button></a>
                          </p>
                          {/* <LinkComponent href={"/forgotpassword"} locale={locale} skipLocaleHandling={false}> */}
                          <p onClick={() => handleCloseModaLogin()}
                            className="font-medium text-green-800 hover:underline">{locale === "en" ? "Forgot password" : "Quên mật khẩu"}
                          </p>
                          {/* </LinkComponent> */}
                        </div>
                      </div>
                    </div>
                  </Modal>
                  <Modal
                    showCloseButton
                    visibleModal={showModalSignup}
                    wrapperClassName="!w-[500px]"
                    contentClassName="!min-h-[0]" onClose={() => setShowModalSignup(false)}
                  >
                    <div className="w-full rounded-lg md:mt-0 sm:max-w-md xl:p-0">
                      <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
                        <h1 className="text-sm font-bold leading-tight tracking-tight text-[#14532D] md:text-2xl">
                          {locale === "en" ? "Sign up" : "Đăng ký"}
                        </h1>
                        <form className="space-y-4 md:space-y-6" action="#">
                          <div>
                            <label htmlFor="email" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Email or phone number" : "Email hoặc số điện thoại"}</label>
                            <input
                              id="exampleFormControlInput1"
                              onChange={(e) => { setEmail(e.target.value) }}
                              type="email" name="email" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                          </div>
                          <div className="relative">
                            <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Password" : "Mật khẩu"}</label>
                            <input
                              id="exampleFormControlInput1"
                              onChange={(e) => { setPassword(e.target.value) }}
                              type={showPassword ? 'text' : 'password'} name="password" className="bg-gray-50 border h-10 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                            <div
                              className="absolute right-0 top-1/2 mt-2 mr-3 flex items-center cursor-pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <>
                                  <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                                  </svg>
                                </>
                              ) : (
                                <>
                                  <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                                  </svg>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="relative">
                            <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Confirm password" : "Nhập lại mật khẩu"}</label>
                            <input
                              type={showPassword ? 'text' : 'password'}
                              id="exampleFormControlInput1"
                              onChange={(e) => { setConfirmPassword(e.target.value) }}
                              name="password" className="bg-gray-50 border border-gray-300 h-10 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />

                            <div
                              className="absolute right-0 top-1/2 mt-2 mr-3 flex items-center cursor-pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <>
                                  <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                                  </svg>
                                </>
                              ) : (
                                <>
                                  <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                                  </svg>
                                </>
                              )}
                            </div>
                          </div>
                          <button type="button" onClick={register} className="w-full text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">{locale === "en" ? "Sign up" : "Đăng ký"}</button>
                          <p className="text-sm font-light text-gray-500">
                            {locale === "en" ? "Have an account yet? " : "Bạn đã có tài khoản? "}<button onClick={e => setShowModalLogin(true)}><button onClick={e => setShowModalSignup(false)} className="font-medium text-green-800 hover:underline">{locale === "en" ? "Sign in" : "Đăng nhập ngay"}</button></button>
                          </p>
                        </form>
                      </div>
                    </div>
                  </Modal>

                  <Modal
                    showCloseButton
                    visibleModal={showModalSignup}
                    wrapperClassName="!w-[500px]"
                    contentClassName="!min-h-[0]" onClose={() => setShowModalSignup(false)}
                  >
                    <div className="w-full rounded-lg md:mt-0 sm:max-w-md xl:p-0">
                      <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
                        <h1 className="text-sm font-bold leading-tight tracking-tight text-[#14532D] md:text-2xl">
                          {locale === "en" ? "Sign up" : "Đăng ký"}
                        </h1>
                        <form className="space-y-4 md:space-y-6" action="#">
                          <div>
                            <label htmlFor="email" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Email or phone number" : "Email hoặc số điện thoại"}</label>
                            <input
                              id="exampleFormControlInput1"
                              onChange={(e) => { setEmail(e.target.value) }}
                              type="email" name="email" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                          </div>
                          <div>
                            <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Password" : "Mật khẩu"}</label>
                            <input
                              id="exampleFormControlInput1"
                              onChange={(e) => { setPassword(e.target.value) }}
                              type="password" name="password" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                          </div>
                          <div>
                            <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Confirm password" : "Nhập lại mật khẩu"}</label>
                            <input
                              type="password"
                              id="exampleFormControlInput1"
                              onChange={(e) => { setConfirmPassword(e.target.value) }}
                              name="password" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                          </div>
                          <button type="button" onClick={register} className="w-full text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">{locale === "en" ? "Sign up" : "Đăng ký"}</button>
                          <p className="text-sm font-light text-gray-500">
                            {locale === "en" ? "Have an account yet? " : "Bạn đã có tài khoản? "}<button onClick={e => setShowModalLogin(true)}><button onClick={e => setShowModalSignup(false)} className="font-medium text-green-800 hover:underline">{locale === "en" ? "Sign in" : "Đăng nhập ngay"}</button></button>
                          </p>
                        </form>
                      </div>
                    </div>
                  </Modal>
                </li>)}
              {
                logged && (
                  <li className="group relative">
                    <p className="menu-item group-hover:border-white">
                      <span
                        className="hover:font-bold h-full gap-1 flex items-center m-auto text-sm hover:underline hover:text-[#156634] px-2 py-3 hover:cursor-pointer rounded">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10ZM13 7C13 8.65685 11.6569 10 10 10C8.34315 10 7 8.65685 7 7C7 5.34315 8.34315 4 10 4C11.6569 4 13 5.34315 13 7ZM9.99998 18.5C11.784 18.5 13.4397 17.9504 14.8069 17.0112C15.4108 16.5964 15.6688 15.8062 15.3178 15.1632C14.59 13.8303 13.0902 13 9.99994 13C6.90969 13 5.40997 13.8302 4.68214 15.1632C4.33105 15.8062 4.5891 16.5963 5.19296 17.0111C6.56018 17.9503 8.2159 18.5 9.99998 18.5Z" fill="#14813D" />
                        </svg>
                        {userData?.phone ? userData.phone.slice(0, 5) : user?.user?.phone.slice(0, 5)}
                      </span>
                    </p>
                    <ul className="absolute bg-white left-0 w-max mt-14 opacity-0 invisible group-hover:opacity-100 group-hover:visible group-hover:mt-0 transition-all duration-500">
                      <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/personal_information"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("profile", locale)}
                          </LinkComponent>
                        </p>
                      </li>
                      {/* <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/past_medical_record"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("history", locale)}
                          </LinkComponent>
                        </p>
                      </li> */}
                      {/* <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/order_history"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("history2", locale)}
                          </LinkComponent>
                        </p>
                      </li> */}
                      <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/test_results"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("test_results", locale)}
                          </LinkComponent>
                        </p>
                      </li>
                      <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/booking_history"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {tranlsate("booking_history", locale)}
                          </LinkComponent>
                        </p>
                      </li>
                      <li>
                        <p className="menu-sub-item">
                          <LinkComponent
                            href={"/change-password"}
                            locale={locale}
                            skipLocaleHandling={false}
                          >
                            {locale === "en" ? "Change Password" : "Thay đổi mật khẩu"}
                          </LinkComponent>
                        </p>
                      </li>
                      <li>
                        <p className="menu-sub-item">
                          <button type="button" onClick={logout}>
                            {locale === "en" ? "Logout" : "Đăng xuất"}
                          </button>
                        </p>
                      </li>
                    </ul>
                  </li>
                )}
            </ul>
          </div>
          <div>
            <ul className="flex">
              <li>
                <p className="menu-item">
                  <button onClick={e => setShowModal(true)}>
                    <span
                      onClick={handleNav}
                      style={{
                        color: "white",
                        marginLeft: 6,
                      }}
                      className="max-[1400px]:text-xs mt-1 font-medium text-sm flex px-4 py-2.5 text-black leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]"
                    >
                      {locale === "en" ? "Book now" : "Đặt lịch khám"}
                    </span>
                  </button>
                  <Modal
                    showCloseButton
                    visibleModal={showModal}
                    wrapperClassName="!w-[550px]"
                    contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}
                  >
                    <p className="text-2xl text-center font-bold text-[#156634]">{locale === "en" ? "Book an Appointment" : "Đặt lịch khám"}</p>
                    <div className="flex flex-col justify-center mt-2">
                      <Button btnType="primary" onClick={() => { setShowModal(false) }}
                        style={{ width: "250px", height: "40px", borderRadius: "50px" }}
                        className="m-auto font-normal bg-white border border-[#156634]" type={undefined} icon={undefined}>
                        <img src="https://d3e4m6b6rxmux9.cloudfront.net/Phone_call_6a65c55145.svg" alt="Icon Phone" />
                        <p className="text-[#156634] mx-2">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                      </Button>
                      <div className="w-[270px] mx-auto flex mb-2 mt-4 text-xs items-center text-gray-500">
                        <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                        {locale == "en" ? "Or" : "Hoặc"}
                        <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                      </div>
                      <h2 className="text-center text-sm my-2">
                        {locale === "en" ? "Please indicate whether you'd like to book home visit or clinic visit" : "Vui lòng lựa chọn phương thức khám tại nhà hoặc phòng khám"}
                      </h2>
                      <div className="w-full justify-center items-center flex gap-4 my-2">
                        <div className="flex items-center">
                          <input
                            id="radio-medium-1"
                            type="radio"
                            name="radio-size"
                            value="clinic"
                            className="hidden"
                            checked={selectedOption === 'clinic'}
                            onChange={handleOptionChange}
                          />
                          <label htmlFor="radio-medium-1" className={`flex items-center cursor-pointer text-gray-600 text-sm ${selectedOption === 'clinic' ? 'font-bold' : 'font-normal'}`}>
                            <span
                              className={`border border-gray-300 rounded-full mr-2 w-5 h-5 flex items-center justify-center`}
                            >
                              {selectedOption === 'clinic' && <span className="bg-[#156634] hover:bg-[#14813d] p-1 w-2.5 h-2.5 rounded-full"></span>}
                            </span>
                            {locale === "en" ? "Clinic Visit:" : "Tại phòng khám"}
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="radio-medium-2"
                            type="radio"
                            name="radio-size"
                            value="home"
                            className="hidden"
                            checked={selectedOption === 'home'}
                            onChange={handleOptionChange}
                          />
                          <label htmlFor="radio-medium-2" className={`flex items-center cursor-pointer text-gray-600 text-sm ${selectedOption === 'home' ? 'font-bold' : 'font-normal'}`}>
                            <span
                              className={`border border-gray-300 rounded-full mr-2 w-5 h-5 flex items-center justify-center`}
                            >
                              {selectedOption === 'home' && <span className={`bg-[#156634] hover:bg-[#14813d] p-1 w-2.5 h-2.5 rounded-full`}></span>}
                            </span>
                            {locale === "en" ? "Home Visit:" : "Khám tại nhà"}
                          </label>
                        </div>
                      </div>
                      <section>
                        <div className="w-full max-w-7xl p-4 md:p-5 lg:p-5 mx-auto bg-[#FAFAFA] rounded-2xl">
                          <div className="w-full flex-col justify-start items-start gap-4 inline-flex">
                            <div className="w-full flex-col justify-start items-start gap-4 flex">
                              <div className="w-full flex-col justify-start items-start gap-4 flex">
                                <div className="w-full justify-start items-start gap-8 flex sm:flex-row flex-col">
                                  <div className="w-full flex-col justify-start items-start flex">
                                    <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Full name" : "Họ và tên"}:
                                    </label>
                                    <input type="text" id="name" name="name" onChange={(e) => {
                                      setName(e.target.value);
                                    }}
                                      className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                      required placeholder={locale == "en" ? "Full name" : "Họ và tên"} />
                                  </div>
                                  <div className="w-full flex-col justify-start items-start flex">
                                    <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                                      <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                                        <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                                      </svg>
                                    </label>
                                    <input type="tel" id="phone" name="phone" onChange={(e) => {
                                      setPhoneNumber(e.target.value);
                                    }}
                                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-[#156634] text-xs lg:text-sm h-10"
                                      required placeholder={locale == "en" ? "Phone" : "Số điện thoại"} />
                                    <span className="text-sm text-red-500">{phone_number_warning_msg}</span>
                                  </div>
                                </div>
                                <div className={`w-full justify-start items-start gap-8 flex sm:flex-row flex-col  ${selectedOption === 'home' ? 'flex' : 'hidden'}`}>
                                  <div className="w-full flex-col justify-start items-start flex">
                                    <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Address" : "Địa chỉ"}:
                                    </label>
                                    <input type="text" id="address" name="address" onChange={(e) => {
                                      setAddress(e.target.value);
                                    }}
                                      className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                      required placeholder={locale == "en" ? "Address" : "Địa chỉ"} />
                                  </div>
                                </div>
                                <div className={`w-full justify-start items-start gap-8 flex sm:flex-row flex-col  ${selectedOption === 'home' ? 'hidden' : 'flex'}`}>
                                  <div className="w-full flex-col justify-start items-start flex">
                                    <label htmlFor="" className="flex gap-1 items-center text-sm font-medium leading-relaxed">{locale == "en" ? "Location" : "Chi nhánh"}:
                                    </label>
                                    <select id="branch" name="branch" value={branch}
                                      onChange={(e) => setBranch(e.target.value)}
                                      className="h-auto border border-gray-300  text-sm rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                                      required>
                                      <option value="q7">{locale == "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7"}</option>
                                      <option value="q2">{locale == "en" ? "46 Nguyen Thi Dinh, An Phu Ward, District 2" : "46 Nguyễn Thị Định, P. An Phú, Quận 2"}</option>
                                      <option value="binhduong">{locale == "en" ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong" : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương"}</option>
                                    </select>
                                  </div>
                                </div>
                                <div className="w-full justify-start items-start gap-8 flex sm:flex-row flex-col">
                                  <div className="w-full flex-col justify-start items-start flex">
                                    <label htmlFor="" className="flex gap-1 items-center  text-sm font-medium leading-relaxed">{locale == "en" ? "Booking date" : "Ngày đặt lịch"}:
                                    </label>
                                    <input
                                      type="date"
                                      id="booking-date"
                                      name="booking-date"
                                      onChange={(e) => {
                                        setBookingDate(e.target.value);
                                      }}
                                      value={bookingDate}
                                      min={dayjs().format("YYYY-MM-DD")}
                                      className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                      required
                                    />
                                  </div>
                                  <div className="w-full flex-col justify-start items-start flex">
                                    <label htmlFor="" className="flex gap-1 items-center text-sm font-medium leading-relaxed">{locale == "en" ? "Booking time" : "Giờ đặt lịch"}:
                                    </label>
                                    <select
                                      value={timeSlot}
                                      name="timeSlot"
                                      className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10" required
                                      onChange={(e) => setTimeSlot(e.target.value)}
                                    >
                                      {bookingSlots?.map((slot) => (
                                        <option value={dayjs(slot).toISOString()}>
                                          {dayjs(slot).format("HH:mm")}
                                        </option>
                                      ))}
                                    </select>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className='w-full'>
                              <div className='mt-auto flex items-center justify-center gap-12'>
                                <button onClick={() => setShowModal(false)} className="text-sm px-6 hover:underline hover:font-bold text-[#156634] font-medium">
                                  {locale == "en" ? "Cancel" : "Hủy"}
                                </button>
                                <button onClick={handleBooking} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-semibold text-white">
                                  {locale == "en" ? "Booking" : "Đặt lịch"}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </section>
                    </div>
                  </Modal>
                </p>
              </li>
            </ul>
          </div>
        </ul>
        < div onClick={handleNav} className="block sm:hidden mr-2 z-10 text-black" >
          {nav ? <AiOutlineClose size={30} /> : <AiOutlineMenu size={30} />}
        </div >
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            height: "100%",
            textAlign: "left",
          }}
          className={
            nav
              ? "sm:hidden absolute px-4 top-0 left-0 right-0 bottom-0 flex justify-start items-center w-full h-screen bg-white text-center text-black ease-in duration-300"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center text-black ease-in duration-300"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav}
          >
            <AiOutlineClose size={30} />{" "}
          </button>
          <ul className="h-full mt-20">
            <li className="my-4 absolute left-0 right-0  flex justify-center items-center">
              <Image
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(190, 48))}`}
                width={190}
                height={48}
                alt="ECHO MEDI"
                src={getLogoUrl(locale)}
              />
            </li>
            <li
              onClick={e => {
                handleNav();
              }}
              className="mt-24 inline-block">
              {/* <LinkComponent locale={locale} skipLocaleHandling={false} href="/cart">
                {logged && (<div className={`absolute w-5 h-5 bg-red-600 text-center items-center justify-center ml-6 rounded-3xl a ${(numOfItem > 0) ? 'block' : 'invisible'}`}>
                  <p className="num-of-item text-white text-xs leading-[20px]">{numOfItem}</p></div>)
                }
                <svg className="mx-3 my-3" fill="#000000" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                  width="25px" height="25px" viewBox="0 0 902.86 902.86"
                  xmlSpace="preserve">
                  <g>
                    <g>
                      <path d="M671.504,577.829l110.485-432.609H902.86v-68H729.174L703.128,179.2L0,178.697l74.753,399.129h596.751V577.829z
			 M685.766,247.188l-67.077,262.64H131.199L81.928,246.756L685.766,247.188z"/>
                      <path d="M578.418,825.641c59.961,0,108.743-48.783,108.743-108.744s-48.782-108.742-108.743-108.742H168.717
			c-59.961,0-108.744,48.781-108.744,108.742s48.782,108.744,108.744,108.744c59.962,0,108.743-48.783,108.743-108.744
			c0-14.4-2.821-28.152-7.927-40.742h208.069c-5.107,12.59-7.928,26.342-7.928,40.742
			C469.675,776.858,518.457,825.641,578.418,825.641z M209.46,716.897c0,22.467-18.277,40.744-40.743,40.744
			c-22.466,0-40.744-18.277-40.744-40.744c0-22.465,18.277-40.742,40.744-40.742C191.183,676.155,209.46,694.432,209.46,716.897z
			 M619.162,716.897c0,22.467-18.277,40.744-40.743,40.744s-40.743-18.277-40.743-40.744c0-22.465,18.277-40.742,40.743-40.742
			S619.162,694.432,619.162,716.897z"/>
                    </g>
                  </g>
                </svg>
              </LinkComponent> */}
            </li>
            <div className="text-black bg-transparent mt-24 m-auto inline-block absolute right-5">
              <div className="text-black bg-transparent my-auto">
                <div className="flex">
                  <div className="my-auto">
                    <div className="group inline-block relative">
                      <button
                        className="border-2 mt-1 px-1 rounded-sm border-grey-900 !rounded-lg bg-white"
                      >
                        {locale == "vi" ?
                          <div className="flex">
                            <svg width="20px" height="20px" viewBox="0 -7 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <g clipPath="url(#clip0_503_2795)">
                                <rect width="28" height="20" rx="2" fill="white" />
                                <mask id="mask0_503_2795" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                  <rect width="28" height="20" rx="2" fill="white" />
                                </mask>
                                <g mask="url(#mask0_503_2795)">
                                  <rect width="28" height="20" fill="#EA403F" />
                                  <path fillRule="evenodd" clipRule="evenodd" d="M14 12.34L10.4733 14.8541L11.7745 10.7231L8.29366 8.1459L12.6246 8.1069L14 4L15.3754 8.1069L19.7063 8.1459L16.2255 10.7231L17.5267 14.8541L14 12.34Z" fill="#FFFE4E" />
                                </g>
                              </g>
                              <defs>
                                <clipPath id="clip0_503_2795">
                                  <rect width="28" height="20" rx="2" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>&ensp;
                            <p className="text-sm my-auto">VI</p>
                            <p className="mt-[2.5px]"><svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">

                              <rect x="0" fill="none" width="24" height="24" />

                              <g>

                                <path d="M7 10l5 5 5-5" />

                              </g>

                            </svg></p>
                          </div> :
                          <div className="flex">
                            <svg width="20px" height="20px" viewBox="0 -7 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <g clipPath="url(#clip0_503_2952)">
                                <rect width="28" height="20" rx="2" fill="white" />
                                <mask id="mask0_503_2952" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                  <rect width="28" height="20" rx="2" fill="white" />
                                </mask>
                                <g mask="url(#mask0_503_2952)">
                                  <rect width="28" height="20" fill="#0A17A7" />
                                  <path fillRule="evenodd" clipRule="evenodd" d="M-1.28244 -1.91644L10.6667 6.14335V-1.33333H17.3334V6.14335L29.2825 -1.91644L30.7737 0.294324L21.3263 6.66667H28V13.3333H21.3263L30.7737 19.7057L29.2825 21.9165L17.3334 13.8567V21.3333H10.6667V13.8567L-1.28244 21.9165L-2.77362 19.7057L6.67377 13.3333H2.95639e-05V6.66667H6.67377L-2.77362 0.294324L-1.28244 -1.91644Z" fill="white" />
                                  <path d="M18.668 6.33219L31.3333 -2" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                  <path d="M20.0128 13.6975L31.3666 21.3503" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                  <path d="M8.00555 6.31046L-3.83746 -1.67099" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                  <path d="M9.29006 13.6049L-3.83746 22.3105" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                  <path fillRule="evenodd" clipRule="evenodd" d="M0 12H12V20H16V12H28V8H16V0H12V8H0V12Z" fill="#E6273E" />
                                </g>
                              </g>
                              <defs>
                                <clipPath id="clip0_503_2952">
                                  <rect width="28" height="20" rx="2" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>&ensp;
                            <p className="text-sm my-auto">EN</p>
                            <p className="mt-[2.5px]"><svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">

                              <rect x="0" fill="none" width="24" height="24" />

                              <g>

                                <path d="M7 10l5 5 5-5" />

                              </g>

                            </svg></p>
                          </div>
                        }
                      </button>
                      <ul className="absolute hidden text-gray-700 group-hover:block -mt-1">
                        <li className="">
                          <button className="border-2 mt-1 px-1 rounded-sm border-grey-900 !rounded-lg bg-white pt-1"
                            onClick={() => {
                              let href = router.asPath;
                              let pName = router.pathname;
                              Object.keys(router.query).forEach((k) => {
                                if (k === "locale") {
                                  pName = pName.replace(
                                    `[${k}]`,
                                    locale == "en" ? "vi" : "en"
                                  );
                                  return;
                                }
                                pName = pName.replace(`[${k}]`, router.query[k] as string);
                              });

                              if (pName != "/en" && pName != "/vi" && !pName.startsWith('/vi/') && !pName.startsWith('/en/')) {
                                if (pName.startsWith('/')) {
                                  pName = '/en' + pName;
                                } else {
                                  pName = '/en/' + pName;
                                }
                              }

                              location.href = pName;
                            }}>
                            {locale == "vi" ?
                              <div className="flex w-[62.5px]">
                                <svg width="20px" height="20px" viewBox="0 -4 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <g clipPath="url(#clip0_503_2952)">
                                    <rect width="28" height="20" rx="2" fill="white" />
                                    <mask id="mask0_503_2952" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                      <rect width="28" height="20" rx="2" fill="white" />
                                    </mask>
                                    <g mask="url(#mask0_503_2952)">
                                      <rect width="28" height="20" fill="#0A17A7" />
                                      <path fillRule="evenodd" clipRule="evenodd" d="M-1.28244 -1.91644L10.6667 6.14335V-1.33333H17.3334V6.14335L29.2825 -1.91644L30.7737 0.294324L21.3263 6.66667H28V13.3333H21.3263L30.7737 19.7057L29.2825 21.9165L17.3334 13.8567V21.3333H10.6667V13.8567L-1.28244 21.9165L-2.77362 19.7057L6.67377 13.3333H2.95639e-05V6.66667H6.67377L-2.77362 0.294324L-1.28244 -1.91644Z" fill="white" />
                                      <path d="M18.668 6.33219L31.3333 -2" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                      <path d="M20.0128 13.6975L31.3666 21.3503" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                      <path d="M8.00555 6.31046L-3.83746 -1.67099" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                      <path d="M9.29006 13.6049L-3.83746 22.3105" stroke="#DB1F35" strokeWidth="0.666667" strokeLinecap="round" />
                                      <path fillRule="evenodd" clipRule="evenodd" d="M0 12H12V20H16V12H28V8H16V0H12V8H0V12Z" fill="#E6273E" />
                                    </g>
                                  </g>
                                  <defs>
                                    <clipPath id="clip0_503_2952">
                                      <rect width="28" height="20" rx="2" fill="white" />
                                    </clipPath>
                                  </defs>
                                </svg>&ensp;
                                <p className="text-xs my-auto">EN</p>
                              </div> :
                              <div className="flex w-[68.5px]">
                                <svg width="20px" height="20px" viewBox="0 -4 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <g clipPath="url(#clip0_503_2795)">
                                    <rect width="28" height="20" rx="2" fill="white" />
                                    <mask id="mask0_503_2795" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
                                      <rect width="28" height="20" rx="2" fill="white" />
                                    </mask>
                                    <g mask="url(#mask0_503_2795)">
                                      <rect width="28" height="20" fill="#EA403F" />
                                      <path fillRule="evenodd" clipRule="evenodd" d="M14 12.34L10.4733 14.8541L11.7745 10.7231L8.29366 8.1459L12.6246 8.1069L14 4L15.3754 8.1069L19.7063 8.1459L16.2255 10.7231L17.5267 14.8541L14 12.34Z" fill="#FFFE4E" />
                                    </g>
                                  </g>
                                  <defs>
                                    <clipPath id="clip0_503_2795">
                                      <rect width="28" height="20" rx="2" fill="white" />
                                    </clipPath>
                                  </defs>
                                </svg>&ensp;
                                <p className="text-xs my-auto">VI</p>
                              </div>
                            }
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <li onClick={handleNav} className="p-2 text-sm hover:text-gray-500">
              <LinkComponent locale={locale} skipLocaleHandling={false} href={"/about/"}>
                {locale === "en" ? "About Us" : "Về chúng tôi"}
              </LinkComponent>
            </li>

            <li
              onClick={() => {
                handleNav();
                handleNav2();
              }}
              className="p-2 text-sm hover:text-gray-500 flex"
            >
              <button>{locale === "en" ? "Services" : "Dịch vụ"}</button>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 svg-icon ml-2"
                viewBox="0 0 1024 1024"
                version="1.1"
              >
                <path d="M64.704 455.808h682.368L426.496 142.656l78.592-77.568 452.928 446.656-453.824 446.976-77.184-76.864 319.872-317.76H64.704V455.808z" />
              </svg>
            </li>
            <li
              onClick={() => {
                handleNav();
                handleNav3();
              }}
              className="p-2 text-sm hover:text-gray-500 flex"
            >
              <button>{locale === "en" ? "Pharmacy" : "Nhà thuốc"}</button>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 svg-icon ml-2"
                viewBox="0 0 1024 1024"
                version="1.1"
              >
                <path d="M64.704 455.808h682.368L426.496 142.656l78.592-77.568 452.928 446.656-453.824 446.976-77.184-76.864 319.872-317.76H64.704V455.808z" />
              </svg>
            </li>
            {/* <li onClick={handleNav} className="p-2 text-sm hover:text-gray-500">
              <LinkComponent locale={locale} skipLocaleHandling={false} href="/membership">
                {locale === "en" ? "Membership" : "Thành viên"}
              </LinkComponent>
            </li> */}
            {/* <li onClick={handleNav} className="p-2 text-sm hover:text-gray-500">
              <LinkComponent locale={locale} skipLocaleHandling={false} href="/corporate">
                {locale === "en" ? "Corporate Healthcare" : "Chăm sóc doanh nghiệp"}
              </LinkComponent>
            </li> */}
            {/* <li onClick={handleNav} className="p-2 text-sm hover:text-gray-500">
              <LinkComponent locale={locale} skipLocaleHandling={false} href="/healthtalks">
                {locale === "en" ? "Health Talks and Workshops" : "Tọa đàm sức khỏe"}
              </LinkComponent>
            </li> */}
            {/* <li
              onClick={() => {
                handleNav();
                handleNav5();
              }}
              className="p-2 text-sm hover:text-gray-500 flex"
            >
              <button>{locale === "en" ? "FAQs" : "Thông tin & Hỏi đáp"}</button>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 svg-icon ml-2"
                viewBox="0 0 1024 1024"
                version="1.1"
              >
                <path d="M64.704 455.808h682.368L426.496 142.656l78.592-77.568 452.928 446.656-453.824 446.976-77.184-76.864 319.872-317.76H64.704V455.808z" />
              </svg>
            </li> */}
            {!logged && (
              <li onClick={handleNav} className="p-2 text-sm hover:text-gray-500">
                <LinkComponent
                  href="/login"
                  locale={locale}
                  skipLocaleHandling={false}
                >
                  {locale === "en" ? "Sign in" : "Đăng nhập"}
                </LinkComponent>
              </li>
            )}

            {logged && (
              <li
                onClick={() => {
                  handleNav();
                  handleNav4();
                }}
                className="p-2 text-sm hover:text-gray-500 flex"
              >
                <button>{locale === "en" ? "Account" : "Tài khoản"}</button>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 svg-icon ml-2"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                >
                  <path d="M64.704 455.808h682.368L426.496 142.656l78.592-77.568 452.928 446.656-453.824 446.976-77.184-76.864 319.872-317.76H64.704V455.808z" />
                </svg>
              </li>
            )}
            <li className="mt-12 inline-block">
              <li className="py-auto flex">
                <button onClick={e => setShowModal(true)}>
                  <span
                    onClick={handleNav}
                    style={{
                      color: "white",
                      marginTop: "-3px",
                      marginLeft: 6
                    }}
                    className="font-medium mt-1 flex px-6 py-2.5 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]"
                  >
                    {locale === "en" ? "Book now" : "Đặt lịch khám"}
                  </span>
                </button>
                <Modal
                  showCloseButton
                  visibleModal={showModal}
                  wrapperClassName="!w-[370px]"
                  contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}
                >
                  <p className="text-2xl text-center font-bold text-[#156634]">{locale === "en" ? "Book an Appointment" : "Đặt lịch khám"}</p>
                  <div className="flex flex-col justify-center mt-2">
                    <Button btnType="primary" onClick={() => { setShowModal(false) }}
                      style={{ width: "250px", height: "40px", borderRadius: "50px" }}
                      className="m-auto font-normal bg-white border border-[#156634]" type={undefined} icon={undefined}>
                      <img src="https://d3e4m6b6rxmux9.cloudfront.net/Phone_call_6a65c55145.svg" alt="Icon Phone" />
                      <p className="text-[#156634] mx-2">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                    </Button>
                    <div className="w-[270px] mx-auto flex mb-2 mt-4 text-xs items-center text-gray-500">
                      <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                      {locale == "en" ? "Or" : "Hoặc"}
                      <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                    </div>
                    <h2 className="text-center text-sm my-2">
                      {locale === "en" ? "Please indicate whether you'd like to book home visit or clinic visit" : "Vui lòng lựa chọn phương thức khám tại nhà hoặc phòng khám"}
                    </h2>
                    <div className="w-full justify-center items-center flex gap-6 mb-2">
                      <div className="flex items-center">
                        <input
                          id="radio-medium-1"
                          type="radio"
                          name="radio-size"
                          value="clinic"
                          className="hidden"
                          checked={selectedOption === 'clinic'}
                          onChange={handleOptionChange}
                        />
                        <label htmlFor="radio-medium-1" className={`flex items-center cursor-pointer text-gray-600 text-sm ${selectedOption === 'clinic' ? 'font-bold' : 'font-normal'}`}>
                          <span
                            className={`border border-gray-300 rounded-full mr-2 w-5 h-5 flex items-center justify-center`}
                          >
                            {selectedOption === 'clinic' && <span className="bg-green-900 p-1 w-2.5 h-2.5 rounded-full"></span>}
                          </span>
                          {locale === "en" ? "Clinic Visit:" : "Tại phòng khám"}
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="radio-medium-2"
                          type="radio"
                          name="radio-size"
                          value="home"
                          className="hidden"
                          checked={selectedOption === 'home'}
                          onChange={handleOptionChange}
                        />
                        <label htmlFor="radio-medium-2" className={`flex items-center cursor-pointer text-gray-600 text-sm ${selectedOption === 'home' ? 'font-bold' : 'font-normal'}`}>
                          <span
                            className={`border border-gray-300 rounded-full mr-2 w-5 h-5 flex items-center justify-center`}
                          >
                            {selectedOption === 'home' && <span className={`bg-green-900 p-1 w-2.5 h-2.5 rounded-full`}></span>}
                          </span>
                          {locale === "en" ? "Home Visit:" : "Khám tại nhà"}
                        </label>
                      </div>
                    </div>
                    <section>
                      <div className="w-full max-w-7xl p-4 md:p-5 lg:p-5 mx-auto bg-[#FAFAFA] rounded-2xl">
                        <div className="w-full flex-col justify-start items-start gap-4 inline-flex">
                          <div className="w-full flex-col justify-start items-start gap-4 flex">
                            <div className="w-full flex-col justify-start items-start gap-4 flex">
                              <div className="w-full justify-start items-start gap-4 flex sm:flex-row flex-col">
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Full name" : "Họ và tên"}:
                                  </label>
                                  <input type="text" id="name" name="name" onChange={(e) => {
                                    setName(e.target.value);
                                  }}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                    required placeholder={locale == "en" ? "Full name" : "Họ và tên"} />
                                </div>
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                                    <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                                      <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                                    </svg>
                                  </label>
                                  <input type="tel" id="phone" name="phone" onChange={(e) => {
                                    setPhoneNumber(e.target.value);
                                  }}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-[#156634] text-xs lg:text-sm h-10"
                                    required placeholder={locale == "en" ? "Phone" : "Số điện thoại"} />
                                  <span className="text-sm text-red-500">{phone_number_warning_msg}</span>
                                </div>
                              </div>
                              <div className={`w-full justify-start items-start gap-4 flex sm:flex-row flex-col  ${selectedOption === 'home' ? 'flex' : 'hidden'}`}>
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Address" : "Địa chỉ"}:
                                  </label>
                                  <input type="text" id="address" name="address" onChange={(e) => {
                                    setAddress(e.target.value);
                                  }}
                                    className="h-10 w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634]"
                                    required placeholder={locale == "en" ? "Address" : "Địa chỉ"} />
                                </div>
                              </div>
                              <div className={`w-full justify-start items-start gap-4 flex sm:flex-row flex-col  ${selectedOption === 'home' ? 'hidden' : 'flex'}`}>
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Location" : "Chi nhánh"}:
                                  </label>
                                  <select id="branch" name="branch" value={branch}
                                    onChange={(e) => setBranch(e.target.value)}
                                    className="h-10 border border-gray-300 text-gray-600 text-sm rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                                    required>
                                    <option value="q7">{locale == "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7"}</option>
                                    <option value="q2">{locale == "en" ? "46 Nguyen Thi Dinh, An Phu Ward, District 2" : "46 Nguyễn Thị Định, P. An Phú, Quận 2"}</option>
                                    <option value="binhduong">{locale == "en" ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong" : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương"}</option>
                                  </select>
                                </div>
                              </div>
                              <div className="w-full justify-start items-start gap-8 flex sm:flex-row flex-row">
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Booking date" : "Ngày đặt lịch"}:
                                  </label>
                                  <input
                                    type="date"
                                    id="booking-date"
                                    name="booking-date"
                                    onChange={(e) => {
                                      setBookingDate(e.target.value);
                                    }}
                                    value={bookingDate}
                                    min={dayjs().format("YYYY-MM-DD")}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10"
                                    required
                                  />
                                </div>
                                <div className="w-full flex-col justify-start items-start flex">
                                  <label htmlFor="" className="flex gap-1 items-center text-gray-600 text-sm font-medium leading-relaxed">{locale == "en" ? "Booking time" : "Giờ đặt lịch"}:
                                  </label>
                                  <select
                                    value={timeSlot}
                                    name="timeSlot"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-[#156634] h-10" required
                                    onChange={(e) => setTimeSlot(e.target.value)}
                                  >
                                    {bookingSlots?.map((slot) => (
                                      <option value={dayjs(slot).toISOString()}>
                                        {dayjs(slot).format("HH:mm")}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className='w-full'>
                            <div className='mt-auto flex items-center justify-center gap-12'>
                              <button onClick={() => setShowModal(false)} className="text-sm hover:underline hover:font-bold text-[#156634] font-medium">
                                {locale == "en" ? "Cancel" : "Hủy"}
                              </button>
                              <button onClick={handleBooking} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-semibold text-white">
                                {locale == "en" ? "Booking" : "Đặt lịch"}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </section>
                  </div>
                </Modal>
              </li>
            </li>
            <div className="text-black bg-transparent mt-12 m-auto inline-block absolute right-5">
              <li className="py-auto flex">
                <button onClick={e => setShowModal2(true)}>
                  <span
                    onClick={handleNav}
                    style={{
                      color: "white",
                      marginTop: "-3px",
                      marginLeft: 6
                    }}
                    className="font-bold mt-1 flex px-6 py-2.5 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]" >
                    {locale === "en" ? "Download app" : "Tải ứng dụng"}
                  </span>
                </button>
                <Modal
                  showCloseButton
                  visibleModal={showModal2}
                  wrapperClassName="!w-[350px]"
                  contentClassName="!min-h-[0]" onClose={() => setShowModal2(false)}
                >
                  <p className="text-sm text-center mb-4 font-bold mt-2 text-green-900">{locale === "en" ? "Download app" : "Tải ứng dụng"}</p>
                  <p style={{ width: "250px" }}
                    className="text-xs text-center mx-auto">{locale === "en" ? "Book an appointment at home, get a doctor's consultation, access medical records at home - All in the ECHOMEDI app" : "Đặt lịch khám tại nhà, nhận tư vấn của bác sĩ, truy cập hồ sơ y tế ngay tại nhà - Tất cả đã có trong ứng dụng ECHOMEDI."}</p>
                  <div className="flex grid-cols-2 mt-5 mb-2">
                    <div className="col-span-1 mr-2 ml-4">
                      <a href="https://play.google.com/store/apps/details?id=com.echomedi.echomedi&hl=vi" target="blank"><img src="https://d3e4m6b6rxmux9.cloudfront.net/Group_7_1_64b787caad.png?updated_at=2023-04-27T05:38:32.266Z" className="rounded-3xl" /></a>
                    </div>
                    <div className="col-span-1 ml-2 mr-4">
                      <a href="https://apps.apple.com/vn/app/echo-medi/id6448538294" target="blank"><img src="https://d3e4m6b6rxmux9.cloudfront.net/Group_8_a88a0842a8.png?updated_at=2023-04-27T05:42:00.091Z" className="rounded-3xl" /></a>
                    </div>
                  </div>
                </Modal>
              </li>
            </div>
          </ul>
        </div>
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            textAlign: "left",
            height: "100%",
          }}
          className={
            nav1
              ? "sm:hidden absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav1}
          >
            <AiOutlineClose size={30} />{" "}

          </button>
          <button
            style={{
              left: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={() => {
              handleNav1();
              handleNav();
            }}
          >
            <AiOutlineArrowLeft size={30} />{" "}
          </button>
          <ul className="h-full w-full pt-12 p-5 overflow-auto">
            <img
              className="mb-5"
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />

            <p className="text-sm underline mb-5 font-bold">
              {tranlsate("in_clinic_service", locale)}
            </p>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                href={"/packages/cham-soc-phong-ngua"}
                skipLocaleHandling={false}
              >
                {tranlsate("preventive_care", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                skipLocaleHandling={false}
                href={"/packages/goi-dieu-tri-ban-dau"}
              >
                {tranlsate("primary_care", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                skipLocaleHandling={false}
                href={"/goi_cham_soc/goi-quan-ly-benh-man-tinh"}
              >
                {tranlsate("on_going_care", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                skipLocaleHandling={false}
                href={"/packages/goi-suc-khoe-toan-dien"}
              >
                {tranlsate("wellness", locale)}
              </LinkComponent>
            </div>
            <p className="text-sm underline mt-10 mb-5 font-bold">
              {tranlsate("home_service", locale)}
            </p>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                skipLocaleHandling={false}
                href={"/packages/cham-soc-tai-nha"}
              >
                {tranlsate("home_visits", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                skipLocaleHandling={false}
                href={"/services/kham-benh-tu-xa-khach-hang-tai-viet-nam/"}
              >
                {locale == "en"
                  ? "Telemedicine - For Residents in Vietnam"
                  : "Khám bệnh từ xa - Khách Hàng Tại Việt Nam"}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav1}>
              <LinkComponent
                locale={locale}
                skipLocaleHandling={false}
                href={"/services/kham-benh-tu-xa-viet-kieu-o-nuoc-ngoai/"}
              >
                {locale == "en"
                  ? "Telemedicine - For Non-Residents (Overseas Vietnamese)"
                  : "Khám bệnh từ xa - Việt Kiều Ở Nước Ngoài"}
              </LinkComponent>
            </div>
            <div className="m-auto">
              <div></div>
              <ul className="flex">
                <li
                  className="inline mr-2 cursor-pointer relative"
                  onClick={() => {
                    const router = useRouter();
                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
                <li
                  className="inline cursor-pointer"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
              </ul>
            </div>
          </ul>
        </div>
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            height: "100%",
            textAlign: "left",
          }}
          className={
            nav2
              ? "sm:hidden absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav2}
          >
            <AiOutlineClose size={30} />{" "}

          </button>
          <button
            style={{
              left: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={() => {
              handleNav2();
              handleNav();
            }}
          >
            <AiOutlineArrowLeft size={30} />{" "}
          </button>
          <ul className="h-full w-full pt-12 p-5 overflow-auto">
            <img
              className="mb-5"
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />
            <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/goi_cham_soc/goi-cham-soc-phong-ngua"}
              >
                {tranlsate("preventive_care_packages", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/initial_treatment/goi-dieu-tri-ban-dau"}
              >
                {tranlsate("primary_care_packages", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/chronicdiseases/goi-quan-ly-benh-man-tinh"}
              >
                {tranlsate("on_going_care_packages", locale)}
              </LinkComponent>
            </div>
            {/* <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/comprehensive_health/goi-suc-khoe-toan-dien/"}
              >
                {tranlsate("wellness", locale)}
              </LinkComponent>
            </div>  */}
            {/* */}
            {/* <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                href={"/goi_tam_ly/goi-tam-ly-nguoi-lon/"}
                locale={locale}
                skipLocaleHandling={false}
              >
                {tranlsate("Adult_mental_health_care_packages", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                href={"/goi_tam_ly/tam-ly-tre-em/"}
                locale={locale}
                skipLocaleHandling={false}
              >
                {tranlsate("Pediatric_mental_health_care_packages", locale)}
              </LinkComponent>
            </div> */}
            <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/goi_suc_khoe_nhi/goi-cham-soc-suc-khoe-nhi/"}
              >
                {tranlsate("Pediatric_health_care_packages", locale)}
              </LinkComponent>
            </div>
            {/* <div className="mb-4 text-sm" onClick={handleNav2}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/echomedi_gen/echomedi-gen"}
              >
                {tranlsate("gene_decoding", locale)}
              </LinkComponent>
            </div> */}
            <div className="m-auto">
              <div></div>
              <ul className="flex">
                <li
                  className="inline mr-2 cursor-pointer relative"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
                <li
                  className="inline cursor-pointer"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
              </ul>
            </div>
          </ul>
        </div>
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            height: "100%",
            textAlign: "left",
          }}
          className={
            nav7
              ? "sm:hidden absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav7}
          >
            <AiOutlineClose size={30} />{" "}

          </button>
          <button
            style={{
              left: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={() => {
              handleNav7();
              handleNav();
            }}
          >
            <AiOutlineArrowLeft size={30} />{" "}
          </button>
          <ul className="h-full w-full p-5 mt-20">
            <img
              className="mb-5"
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />

            <div className="mb-4 text-sm" onClick={handleNav7}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/goi_suc_khoe_nhi/goi-cham-soc-suc-khoe-nhi"}
              >
                {tranlsate("Pediatric_health_care_packages", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav7}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/goi_suc_khoe_nhi/goi-cham-soc-tam-ly-nhi/"}
              >
                {tranlsate("Pediatric_psychology_packages", locale)}
              </LinkComponent>
            </div>
            <div className="m-auto">
              <div></div>
              <ul className="flex">
                <li
                  className="inline mr-2 cursor-pointer relative"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
                <li
                  className="inline cursor-pointer"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
              </ul>
            </div>
          </ul>
        </div>
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            textAlign: "left",
            height: "100%",
          }}
          className={
            nav3
              ? "sm:hidden absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav3}
          >
            <AiOutlineClose size={30} />{" "}

          </button>
          <button
            style={{
              left: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={() => {
              handleNav3();
              handleNav();
            }}
          >
            <AiOutlineArrowLeft size={30} />{" "}
          </button>
          <ul className="h-full w-full pt-12 p-5 overflow-auto">
            <img
              className="mb-5"
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />

            <p className="underline text-sm my-5 font-bold">
              {tranlsate("monthly_packages", locale ? locale : "")}
            </p>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={
                  "/products/goi-cham-soc-suc-khoe-cho-nguoi-lon-tuoi-tuoi-60/"
                }
              >
                {tranlsate("elderly", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={
                  "/products/goi-cham-soc-suc-khoe-cho-nam-gioi-do-tuoi-trung-nien-tuoi-45"
                }
              >
                {tranlsate("middle_aged_man", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={
                  "/products/goi-cham-soc-suc-khoe-cho-nu-gioi-do-tuoi-trung-nien-tuoi-45/"
                }
              >
                {tranlsate("middle_aged_woman", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={
                  "/products/goi-cham-soc-suc-khoe-cho-nguoi-truong-thanh-tuoi-18-45/"
                }
              >
                {tranlsate("adult", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={
                  "/products/goi-cham-soc-suc-khoe-cho-thanh-thieu-nien-tuoi-13-19/"
                }
              >
                {tranlsate("teenager", locale ? locale : "")}
              </LinkComponent>
            </div>
            <p className="underline text-sm my-5 font-bold">
              {tranlsate("health_concern", locale ? locale : "")}
            </p>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-giac-ngu/"}
              >
                {tranlsate("sleep", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-cai-thuoc-la/"}
              >
                {tranlsate("smoking_cessation", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-giam-can/"}
              >
                {tranlsate("weight_loss", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-cham-soc-da-va-ngan-ngua-lao-hoa/"}
              >
                {tranlsate("skin_care_anti_aging", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-cham-soc-va-phuc-hoi-toc-mong/"}
              >
                {tranlsate("hair_nails_treatment", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-cham-soc-suc-khoe-cho-phu-nu-mang-thai/"}
              >
                {tranlsate("pregnancy_care", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-suc-khoe-sinh-ly-nam/"}
              >
                {tranlsate("men_sexual_health", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-suc-khoe-sinh-ly-nu/"}
              >
                {tranlsate("women_sexual_health", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-suc-khoe-tim-mach/"}
              >
                {tranlsate("heart_blood_circulation", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-tieu-hoa/"}
              >
                {tranlsate("digestive_system", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-phong-ngua-benh-xuong-khop/"}
              >
                {tranlsate("bone_joint_health", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-tang-suc-de-khang-va-mien-dich/"}
              >
                {tranlsate("immune_system", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-cai-thien-tri-nao/"}
              >
                {tranlsate("brain_health", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-giai-doc-tang-cuong-chuc-nang-gan/"}
              >
                {tranlsate("detoxification_and_liver_function_enhancement", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-cham-soc-phu-khoa"}
              >
                {tranlsate("gynecological_care", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-benh-dau-da-day"}
              >
                {tranlsate("gastric_pain_or_stomachache", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-tang-cuong-chuc-nang-phoi"}
              >
                {tranlsate("lung_function_support", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-benh-tieu-duong"}
              >
                {tranlsate("diabetes", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-benh-gout"}
              >
                {tranlsate("gout", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-benh-gan-nhiem-mo"}
              >
                {tranlsate("fatty_liver", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-giam-cang-thang-stress"}
              >
                {tranlsate("stress", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-benh-phi-dai-tuyen-tien-liet"}
              >
                {tranlsate("benign_prostatic_hyperplasia", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-cham-soc-suc-khoe-doi-mat"}
              >
                {tranlsate("eye_health", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav3}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/products/goi-ho-tro-benh-dai-trang"}
              >
                {tranlsate("irritable_bowel_syndrome", locale ? locale : "")}
              </LinkComponent>
            </div>
            <div className="absolute z-10 hidden group-hover:block bg-transparent">
              <div
                className="text-black bg-transparent "
                style={{ width: "100px" }}
              >
                <div className="flex">
                  <button
                    onClick={() => {
                      let href = router.asPath;
                      let pName = router.pathname;
                      Object.keys(router.query).forEach((k) => {
                        if (k === "locale") {
                          pName = pName.replace(
                            `[${k}]`,
                            locale == "en" ? "vi" : "en"
                          );
                          return;
                        }
                        pName = pName.replace(
                          `[${k}]`,
                          router.query[k] as string
                        );
                      });

                      location.href = pName;
                    }}
                  >
                    {locale === "vi" ? (
                      <div className="flex">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 60 30"
                          width="40"
                          height="40"
                        >
                          <clipPath id="s">
                            <path d="M0,0 v30 h60 v-30 z" />
                          </clipPath>
                          <clipPath id="t">
                            <path d="M30,15 h30 v15 z v15 h-30 z h-30 v-15 z v-15 h30 z" />
                          </clipPath>
                          <g clipPath="url(#s)">
                            <path d="M0,0 v30 h60 v-30 z" fill="#012169" />
                            <path
                              d="M0,0 L60,30 M60,0 L0,30"
                              stroke="#fff"
                              strokeWidth="6"
                            />
                            <path
                              d="M0,0 L60,30 M60,0 L0,30"
                              clipPath="url(#t)"
                              stroke="#C8102E"
                              strokeWidth="4"
                            />
                            <path
                              d="M30,0 v30 M0,15 h60"
                              stroke="#fff"
                              strokeWidth="10"
                            />
                            <path
                              d="M30,0 v30 M0,15 h60"
                              stroke="#C8102E"
                              strokeWidth="6"
                            />
                          </g>
                        </svg>
                      </div>
                    ) : (
                      <div className="flex">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="40"
                          height="40"
                          viewBox="0 0 30 20"
                          version="1.1"
                        >
                          <rect width="30" height="20" fill="#da251d" />
                          <polygon
                            points="15,4 11.47,14.85 20.71,8.15 9.29,8.15 18.53,14.85"
                            fill="#ff0"
                          />
                        </svg>
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="m-auto">
              <div></div>
              <ul className="flex">
                <li
                  className="inline mr-2 cursor-pointer relative"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                  {/* <Image
                    src={IgImg3}
                    alt="/"
                    width={25}
                    height={25}
                  /> */}
                </li>
                <li
                  className="inline cursor-pointer"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                  {/* <Image
                    src={IgImg4}
                    alt="/"
                    width={25}
                    height={25}
                  /> */}
                </li>
              </ul>
            </div>
          </ul>
        </div>
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            height: "100%",
            textAlign: "left",
          }}
          className={
            nav4
              ? "sm:hidden absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav4}
          >
            <AiOutlineClose size={30} />{" "}

          </button>
          <button
            style={{
              left: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={() => {
              handleNav4();
              handleNav();
            }}
          >
            <AiOutlineArrowLeft size={30} />{" "}
          </button>
          <ul className="h-full w-full p-5 mt-20">
            <img
              className="mb-5"
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />

            <div className="mb-4 text-sm" onClick={handleNav4}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/personal_information"}
              >
                {tranlsate("profile", locale)}
              </LinkComponent>
            </div>
            {/* <div className="mb-4 text-sm" onClick={handleNav4}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/past_medical_record"}
              >
                {locale == "en" ? "History medical record" : "Lịch sử bệnh án"}
              </LinkComponent>
            </div> */}
            {/* <div className="mb-4 text-sm" onClick={handleNav4}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/order_history"}
              >
                {tranlsate("history2", locale)}
              </LinkComponent>
            </div> */}
            <div className="mb-4 text-sm" onClick={handleNav4}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/test_results"}
              >
                {tranlsate("test_results", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav4}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/booking_history"}
              >
                {tranlsate("booking_history", locale)}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav4}>
              <LinkComponent
                skipLocaleHandling={false}
                locale={locale}
                href={"/change-password"}
              >
                {locale === "en" ? "Change Password" : "Thay đổi mật khẩu"}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav4}>
              <button className="" type="button" onClick={logout}>{locale === "en" ? "Logout" : "Đăng xuất"}</button>
            </div>
            <div className="m-auto">
              <div></div>
              <ul className="flex">
                <li
                  className="inline mr-2 cursor-pointer relative"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
                <li
                  className="inline cursor-pointer"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
              </ul>
            </div>
          </ul>
        </div>
        <div
          style={{
            background: "white",
            zIndex: 999,
            position: "fixed",
            height: "100%",
            textAlign: "left",
          }}
          className={
            nav5
              ? "sm:hidden absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
              : "sm:hidden absolute top-0 left-[-100%] right-0 bottom-0 flex justify-center items-center w-full h-screen bg-white text-center ease-in duration-300 text-black"
          }
        >
          <button
            style={{
              right: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={handleNav5}
          >
            <AiOutlineClose size={30} />{" "}

          </button>
          <button
            style={{
              left: "20px",
              top: "20px",
              position: "absolute",
            }}
            onClick={() => {
              handleNav5();
              handleNav();
            }}
          >
            <AiOutlineArrowLeft size={30} />{" "}
          </button>
          <ul className="h-full w-full p-5 mt-20">
            <img
              className="mb-5"
              alt="ECHO MEDI"
              src={getLogoUrl(locale)}
            />
            <div className="mb-4 text-sm" onClick={handleNav5}>
              <LinkComponent
                href={"/articles_preventive_care"}
                locale={locale}
                skipLocaleHandling={false}
              >
                {locale == "vi" ? "Bài viết sức khoẻ" : "Healthcare Articles"}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav5}>
              <LinkComponent
                href={"/articles_news"}
                locale={locale}
                skipLocaleHandling={false}
              >
                {locale == "vi" ? "Tin tức, Hoạt động và Ưu đãi" : "News, Events & Special Offers"}
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav5}>
              <LinkComponent
                href={"/video_short"}
                locale={locale}
                skipLocaleHandling={false}
              >
                Videos
              </LinkComponent>
            </div>
            <div className="mb-4 text-sm" onClick={handleNav5}>
              <LinkComponent
                href={"/question"}
                locale={locale}
                skipLocaleHandling={false}
              >
                {locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}
              </LinkComponent>
            </div>
            <div className="m-auto">
              <div></div>
              <ul className="flex">
                <li
                  className="inline mr-2 cursor-pointer relative"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
                <li
                  className="inline cursor-pointer"
                  onClick={() => {
                    const router = useRouter();

                    let href = router.asPath;
                    let pName = router.pathname;
                  }}
                >
                </li>
              </ul>
            </div>
          </ul>
        </div>
        {/* {logged && numOfItem > 0 && (
          <div
            className="cart"
            style={{
              position: "fixed",
              right: "20px",
              bottom: "20px",
              background: "rgb(158 169 154)",
              borderRadius: "50%",
              zIndex: 123,
              margin: "auto",
              padding: "10px",
            }}
          >
            <LinkComponent href={"/cart"} skipLocaleHandling={false} locale={locale}>
              <div className="flex text-green-800 m-auto">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlnsXlink="http://www.w3.org/1999/xlink"
                  fill="white"
                  version="1.1"
                  id="Capa_1"
                  width="30px"
                  height="30px"
                  viewBox="0 0 902.86 902.86"
                  xmlSpace="preserve"
                >
                  <g>
                    <g>
                      <path d="M671.504,577.829l110.485-432.609H902.86v-68H729.174L703.128,179.2L0,178.697l74.753,399.129h596.751V577.829z     M685.766,247.188l-67.077,262.64H131.199L81.928,246.756L685.766,247.188z" />
                      <path d="M578.418,825.641c59.961,0,108.743-48.783,108.743-108.744s-48.782-108.742-108.743-108.742H168.717    c-59.961,0-108.744,48.781-108.744,108.742s48.782,108.744,108.744,108.744c59.962,0,108.743-48.783,108.743-108.744    c0-14.4-2.821-28.152-7.927-40.742h208.069c-5.107,12.59-7.928,26.342-7.928,40.742    C469.675,776.858,518.457,825.641,578.418,825.641z M209.46,716.897c0,22.467-18.277,40.744-40.743,40.744    c-22.466,0-40.744-18.277-40.744-40.744c0-22.465,18.277-40.742,40.744-40.742C191.183,676.155,209.46,694.432,209.46,716.897z     M619.162,716.897c0,22.467-18.277,40.744-40.743,40.744s-40.743-18.277-40.743-40.744c0-22.465,18.277-40.742,40.743-40.742    S619.162,694.432,619.162,716.897z" />
                    </g>
                  </g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                  <g></g>
                </svg>
              </div>
            </LinkComponent>
          </div>
        )} */}
      </nav >
      <section>
        {showModalPass && (
          <ModalLogin
            showCloseButton
            visibleModal={showModalPass}
            wrapperClassName="lg:!w-[435px] !w-[370px]"
            contentClassName="!min-h-[0]" onClose={() => setShowModalPass(false)}
          >
            <div className=" bg-white rounded-lg w-full max-w-md overflow-hidden">
              <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Nhập mã xác thực để tiếp tục" : "Enter the verification code to continue"}</h3>
              <div className="flex justify-center mt-4">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                    ✓
                  </div>
                  <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
                </div>
                <div className="flex items-center">
                  <div className="border border-[#14813D] rounded-full p-1">
                    <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center">
                      2
                    </div>
                  </div>
                  <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center border border-[#B3B3B3]">
                    3
                  </div>
                </div>
              </div>
              <div className="px-4 py-6">
                <p className="py-4 text-center text-base">{locale == "vi" ? "Vui lòng nhập mã xác thực vừa được gửi đến số điện thoại" : "The authentication code has just been sent to the number"} {phone_number}</p>
                <div className="flex flex-col gap-2">
                  <label htmlFor="OTP" className="font-semibold">{locale === 'en' ? "Verification code" : "Mã xác thực"}</label>
                  <OTPInput
                    value={otp}
                    onChange={setOtp}
                    numInputs={6}
                    renderSeparator={<span className="md:px-4 px-2.5"></span>}
                    renderInput={(props) => <input {...props} style={{ width: 35, height: 35, textAlign: 'center', border: '1px solid black', borderRadius: 5, fontWeight: 'bold' }} />}
                  />
                </div>
                <div className="text-center flex items-center justify-between py-2">
                  <button onClick={handleResendOTP}
                    disabled={resendDisabled} className="text-[#156634] text-xs">{resendDisabled ? "Resend OTP" : "Resend OTP"}</button>
                  <span className="text-xs">{locale == "vi" ? `Mã xác nhận sẽ hết hạn trong ${countdown}s` : `The verification code will expire in ${countdown}s`}</span>
                </div>
                <div className="flex items-center justify-center mt-2">
                  <button onClick={verifyOTP} className="bg-green-700 hover:bg-green-800 text-white font-bold py-2 w-full rounded-full focus:outline-none focus:shadow-outline" type="button">
                    {locale == "vi" ? "Xác thực" : "Verify"}
                  </button>
                </div>
              </div>
            </div>
          </ModalLogin>
        )}
      </section>
      <ModalLogin
        showCloseButton
        visibleModal={showModalPhone}
        wrapperClassName="lg:!w-[435px] !w-[370px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalPhone(false)}
      >
        <section>
          <div>
            <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Đặt lại mật khẩu" : "Reset Password"}</h3>
            <div className="flex justify-center mt-4">
              <div className="flex items-center">
                <div className="border border-[#14813D] rounded-full p-1">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center">
                    1
                  </div>
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full  flex items-center justify-center border border-[#B3B3B3]">
                  2
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full flex items-center justify-center border border-[#B3B3B3]">
                  3
                </div>
              </div>
            </div>

            <form className="px-4 py-6">
              <p className="text-center text-base">{locale == "vi" ? "Vui lòng nhập số điện thoại liên kết với tài khoản của bạn để nhận mã xác thực qua tin nhắn" : "Enter the phone number linked with your account to receive the verification code via SMS"}</p>
              <div className="mt-4">
                <label htmlFor="text" className="block mb-2 font-semibold">
                  {locale === "en" ? "Phone Number" : "Số điện thoại"}
                </label>
                <input
                  type="text"
                  id="exampleFormControlInput1"
                  onChange={(e) => { setPhoneNumber(e.target.value) }}
                  name="password" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
              </div>
              <div className="flex items-center justify-center mt-4 gap-2">
                <button onClick={handleBack} className="py-2 w-full text-[#156634]" type="button">
                  {locale == "vi" ? "Quay lại" : "Back"}
                </button>
                <button onClick={handleSendPhone} disabled={!phone_number} className="bg-[#156634] rounded-full hover:bg-[#166534] text-white py-2 w-full focus:outline-none focus:shadow-outline" type="button">
                  {locale == "vi" ? "Tiếp tục" : "Next"}
                </button>
              </div>
            </form>
          </div>
        </section>
      </ModalLogin>
      <ModalLogin
        showCloseButton
        visibleModal={showModalForgotPassword}
        wrapperClassName="lg:!w-[435px] !w-[370px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalFotgotPassword(false)}
      >
        <section>
          <div>
            <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Đặt lại mật khẩu" : "Reset Password"}</h3>
            <div className="flex justify-center mt-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                  ✓
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                  ✓
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="border border-[#14813D] rounded-full p-1">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center">
                    3
                  </div>
                </div>
              </div>
            </div>
            <p className="text-center text-sm my-2">{locale === "en" ? "Please reset your new password" : "Vui lòng đặt lại mật khẩu mới"}</p>
            <div className="p-6 space-y-4 sm:p-8">
              <div className="space-y-3">
                <div>
                  <label htmlFor="password" className="block mb-2 text-sm font-medium ">
                    {locale === "en" ? "New password" : "Mật khẩu mới"}
                  </label>
                  <input
                    type="password"
                    id="exampleFormControlInput1"
                    onChange={(e) => { setPassword(e.target.value) }}
                    name="password" className="bg-gray-50 border border-gray-300  sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
                <div>
                  <label htmlFor="password" className="block mb-2 text-sm font-medium ">
                    {locale === "en" ? "Re-enter your new password" : "Nhập lại mật khẩu mới"}
                  </label>
                  <input
                    type="password"
                    id="exampleFormControlInput1"
                    onChange={(e) => { setNewPassword(e.target.value) }}
                    name="password" className="bg-gray-50 border border-gray-300 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
              </div>
              <button
                onClick={forgotpassword} className="w-full bg-[#156634] rounded-full hover:bg-[#166534] text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium text-sm md:px-5 py-2.5 text-center">
                {locale === "en" ? "Change Password" : "Đổi Mật Khẩu"}
              </button>
            </div>
          </div>
        </section>
      </ModalLogin>
      <ModalLogin
        showCloseButton
        visibleModal={showModalSuccess}
        wrapperClassName="lg:!w-[435px] !w-[370px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalSuccess(false)}
      >
        <section>
          <div>
            <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Đặt lại mật khẩu" : "Reset Password"}</h3>
            <div className="flex justify-center my-5">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                    ✓
                  </div>
                  {i < 3 && <div className="h-1 w-8 bg-[#156634] mx-1"></div>}
                </div>
              ))}
            </div>

            <p className="text-base text-center">{locale === "en" ? "Password changed successfully!" : "Đổi mật khẩu thành công!"}</p>
            <p className="text-base text-center mb-6">{locale === "en" ? "Please log in again to continue" : "Vui lòng đăng nhập lại để tiếp tục"}</p>
            <p onClick={() => handleOpenModaLogin()} className="w-full bg-[#156634] text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm px-5 py-2.5 text-center">
              {locale === "en" ? "Login" : "Đăng nhập"}
            </p>
          </div>
        </section>
      </ModalLogin>
    </>
  );
};

export default NavBar;