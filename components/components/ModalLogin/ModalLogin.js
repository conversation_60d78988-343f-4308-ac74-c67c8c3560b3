import Icon from "../Icon"

const ModalLogin = ({
  visibleModal,
  wrapperClassName = "",
  contentClassName = "",
  bodyClassName = "",
  children,
  onClose,
  fill="black",
  showCloseButton = false,
}) => {
  return (
    <>
      <div
        className={`m-auto justify-center items-center flex overflow-hidden fixed inset-0 z-[1001] outline-none focus:outline-none transition-all duration-300 w-[100%] sm:w-[940px] ${wrapperClassName} ${(visibleModal ? "visible opacity-1" : "invisible opacity-0")
          }`}
      >
        <div
          style={{
          }}
          className={`relative p-4 md:p-4 rounded-3xl max-h-[95vh] min-h-[460px] w-full bg-white  bottom-0 sm:bottom-auto sm:block ${contentClassName}`}
        >
          <div className={bodyClassName}>{children}</div>
        </div>
      </div>
      <button
        onClick={onClose}
        className={`fixed inset-0 z-[100] bg-black transition-all duration-300 ${visibleModal ? "visible opacity-30" : "invisible opacity-0"
          }`}
      />
    </>
  )
}

export default ModalLogin
