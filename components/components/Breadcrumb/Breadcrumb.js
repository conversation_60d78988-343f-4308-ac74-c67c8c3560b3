
const Breadcrumb = ({ pageName, description }) => {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1">
        <li className="inline-flex items-center">
          <div className="flex items-center">
            <a className="flex items-center text-base font-medium text-gray-900">
              {pageName}
            </a>
            <svg className="w-5 h-5 mx-1" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.00378 5.99561L15.004 11.9959L9.00024 17.9996" stroke="black" stroke-width="null" stroke-linecap="round" stroke-linejoin="round" class="my-path"></path>
            </svg>
          </div>
        </li>
        <li className="inline-flex items-center">
          <div className="flex items-center">
            <a className="inline-flex items-center text-base font-medium text-gray-900">
              {description}
            </a>
            <svg className="w-5 h-5 mx-1" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.00378 5.99561L15.004 11.9959L9.00024 17.9996" stroke="black" stroke-width="null" stroke-linecap="round" stroke-linejoin="round" class="my-path"></path>
            </svg>
          </div>
        </li>
        <li className="inline-flex items-center">
          <div className="flex items-center">
            <a className="inline-flex items-center text-base font-medium text-gray-900 opacity-80">
              {description}
            </a>
          </div>
        </li>
      </ol>
    </nav>
  );
};

export default Breadcrumb;
