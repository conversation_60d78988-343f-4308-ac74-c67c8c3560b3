import React, { useState, useMemo } from "react";
import { useRouter } from 'next/router';
import axios from "axios";
import toast from 'react-hot-toast';
import dayjs from "dayjs";
import moment from "moment";
import { DatePicker } from '../../lib/datePicker';

require("dayjs/locale/vi");

const contact = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [birthday, setBirthday] = useState("");
  const [bd, setBD] = useState<Date | null>(null);
  const [bookingDate, setBookingDate] = useState(moment().format('YYYY-MM-DD'));
  const [timeSlot, setTimeSlot] = useState("");
  const [gender, setGender] = useState("male");
  const [address, setAddress] = useState("");
  const [phone_number, setPhoneNumber] = useState("");
  const [phone_number_warning_msg, setPhoneNumberWarningMsg] = useState("");
  const [message, setMessage] = useState("");
  const [branch, setBranch] = useState("q7");
  const [open, setOpen] = useState(false);

  const router = useRouter();
  const locale = (router.query.locale as string) || "en";
  const isPastDate = (date: string) => {
    return dayjs(date).isBefore(dayjs().startOf("day"));
  };

  dayjs.locale(locale);

  const bookingSlots = useMemo(() => {
    let slots = [];
    const startTime = 9;
    let endTime = 19; // Kết thúc ở 19:00
    if (dayjs(bookingDate).day() === 0) {
      endTime = 14;
    }
    for (let i = startTime; i <= endTime; i++) {
      let slot = dayjs(bookingDate).set("hour", i).set("minute", 0);
      if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
        slots.push(slot);
      }
  
      if (i < endTime) {
        slot = dayjs(bookingDate).set("hour", i).set("minute", 30);
        if (slot.isAfter(dayjs()) && !isPastDate(slot.toISOString())) {
          slots.push(slot);
        }
      }
    }
  
    if (slots?.length) {
      setTimeSlot(slots[0].toISOString());
    }
  
    return slots;
  }, [bookingDate]);
  
  const handleBooking = () => {
    if (phone_number == "" || !validatePhone(phone_number)) {
      setPhoneNumberWarningMsg(locale == "vi" ? "Yêu cầu nhập số điện thoại hợp lệ." : "Please enter your phone number.")
      toast.error("Đặt lịch không thành công");
      return;
    }
    const payload = {
      data: {
        createNewPatient: true,
        full_name: name,
        contactFullName: name,
        gender,
        email,
        contactEmail: email,
        phone: phone_number,
        contactPhoneNumber: phone_number,
        message,
        birthday: bd ? dayjs(bd).toISOString() : null,
        address: {
          address
        },
        contactAddress: address,
        branch,
        bookingDate: timeSlot,
        note: message,
      }
    };

    axios
      .post("https://api.echomedi.com/api/bookings/createBookingFromWeb", payload)
      .then(function (response) {
        toast.success("Đặt lịch thành công");
        location.href = "/booking_detail/?code=" + response.data.booking.id;
      })
      .catch(function (error) {
        toast.error("Đặt lịch không thành công");
      });
  };

  function validatePhone(phone: string) {
    return /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
  }

  return (
    <div className="bg-gray-100">
      <div className="lg:max-w-3xl mx-auto max-w-full lg:p-10">
        <div className="bg-white p-4 shadow-md lg:rounded-md">
          <h2 className="lg:text-2xl text-xl font-semibold mb-6 text-center">{locale == "en" ? "BOOKING FORM" : "ĐẶT LỊCH KHÁM"}</h2>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Full name" : "Họ và tên"}:</label>
              <input type="text" id="name" name="name" onChange={(e) => {
                setName(e.target.value);
              }}
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-blue-500 h-10"
                required />
            </div>
            <div>
              <label htmlFor="phone" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Phone" : "Số điện thoại"}:<span style={{ color: "red" }}> *</span></label>
              <input type="tel" id="phone" name="phone" onChange={(e) => {
                setPhoneNumber(e.target.value);
              }}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 text-xs lg:text-sm h-10"
                required />
              <span className="text-sm text-red-500">{phone_number_warning_msg}</span>
            </div>
            <div>
              <label htmlFor="booking-date" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Booking date" : "Ngày đặt lịch"}:</label>
              <input
                type="date"
                id="booking-date"
                name="booking-date"
                onChange={(e) => {
                  setBookingDate(e.target.value);
                }}
                value={bookingDate}
                min={dayjs().format("YYYY-MM-DD")}
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-xs lg:text-sm focus:outline-none focus:border-blue-500 h-10"
                required
              />

            </div>
            <div>
              <label htmlFor="booking-time" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Booking time" : "Giờ đặt lịch"}:</label>
              <select
                value={timeSlot}
                name="timeSlot"
                className="w-full px-4 py-2 border border-gray-300 text-xs lg:text-sm rounded-md focus:outline-none focus:border-blue-500 h-10" required
                onChange={(e) => setTimeSlot(e.target.value)}
              >
                {bookingSlots?.map((slot) => (
                  <option value={dayjs(slot).toISOString()}>
                    {dayjs(slot).format("HH:mm")}
                  </option>
                ))}
              </select>
            </div>
            <div className="col-span-2">
              <label htmlFor="address" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Address" : "Địa chỉ"}:</label>
              <textarea id="address" name="address" rows={3} onChange={(e) => {
                setAddress(e.target.value);
              }}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                required></textarea>
            </div>
            <div className="col-span-2">
              <label htmlFor="branch" className="block text-gray-700 lg:textx-sm text-xs font-bold mb-2">{locale == "en" ? "Location" : "Chi nhánh"}:</label>
              <select id="branch" name="branch" value={branch}
                onChange={(e) => setBranch(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 text-xs lg:text-sm h-10"
                required>
                <option value="q7">{locale == "en" ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7" : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7"}</option>
                <option value="q2">{locale == "en" ? "46 Nguyen Thi Dinh, An Phu Ward, District 2" : "46 Nguyễn Thị Định, P. An Phú, Quận 2"}</option>
                <option value="binhduong">{locale == "en" ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong" : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương"}</option>
              </select>
            </div>
          </div>
          <div className="mt-8 flex justify-center">
            <button onClick={handleBooking} type="submit" className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-full text-xs hover:bg-green-700 font-bold">{locale == "en" ? "Booking" : "Đặt lịch"}</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default contact;
