Echo Medi Website ([http://](https://echomedi.com/))

## Getting Started

```bash

git clone https://github.com/echomedidev/em_web.git
cd em_web
npm i
npm run dev
# or
yarn dev

```
Open http://localhost:3000 with your browser to see the result.

## File design using code layout

```bash

https://www.figma.com/file/YsscbkoLWFnVOg5HJR6seA/ECHO-MEDI?type=design&node-id=2959-142&mode=design&t=5dlEQnRGkRHuzP1D-0


```

## Coding Process

- Create Branch (Meaningful branch names)
- Commit code to the branch after completed (Meaningful commit names)
- Push code to branch
- Create Pull Request to branch main (Meaningful Pull Request names, Assign Pull Request to leader)  
- After merging the code and deploying, check the merged function

## Tech Stacks

- ReactJs
- NodeJs
- Strapi
- Tailwind CSS

## Learn More

Project functions:

- Page Home
- Page Tổng Quan
- Page Login - Register
- Page Thành Viên
- Page Về Chúng Tôi
- <PERSON> C<PERSON>c <PERSON>
- Page Dịch Vụ
- Page <PERSON>
- Page Appointment Booking
- Page Giỏ Hàng (Momo + VNPAY)
- Page Order Detail
- Page Your Information 



You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome !

