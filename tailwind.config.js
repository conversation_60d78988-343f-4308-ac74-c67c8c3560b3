/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'emgreen': '#416045'
      },
      backgroundImage: {
        'header-gold': 'linear-gradient(92.26deg, #F0BB40 -1.27%, #EAB03D 13.91%, #DEA138 43.67%, #DD9F37 82.85%, #F3C042 113.07%)',
        'body-gold': 'linear-gradient(158.43deg, #FFFDF9 0.87%, #FFFBEE 23.7%, #FFFEFB 54.24%, #FFFDF2 84.16%)',
        'header-family': 'linear-gradient(90.75deg, #24A555 0.65%, #41AB69 32.38%, #299B54 63.94%, #41AB69 99.29%)',
        'body-family': 'linear-gradient(101.92deg, #FCFFFD 4.14%, #F7FFFA 34.74%, #F3FFF8 69.95%, #FCFFFD 82.59%)',
        'header-platinum': 'linear-gradient(90.73deg, #DEE0E1 -0.42%, #EFEEEE 15.22%, #DCDEDD 45.86%, #C8C8CA 69%, #E9E9E9 93.47%, #B8BABB 117.33%)',
        'body-platinum': 'linear-gradient(154.29deg, #FFFFFF -3.24%, #F8F5F5 25.32%, #FFFFFF 43.6%, #F5F5F5 63.31%, #FAFAFA 82.45%)',
        'body-family-doctor': 'linear-gradient(178.42deg, #FAFBFD -13.15%, #FCFFFE 22.25%, #ECFCF2 98.66%)',
      },
    },
  },
  plugins: [],
};
