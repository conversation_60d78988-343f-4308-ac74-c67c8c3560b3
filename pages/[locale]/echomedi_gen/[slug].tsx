import type {
    InferGetStaticPropsType,
} from 'next';
import React from 'react';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import toast from 'react-hot-toast';
import { useState } from 'react';
import axios from 'axios';
import Modal from "../../../components/components/Modal";
import Button from "../../../components/components/Button";
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';
import { getStaticPathsPackagesEchomediGen, getStaticPropsGenDetail } from '../../../lib/getStatic';
import LinkComponent from '../../../components/Link';
export { getStaticPathsPackagesEchomediGen as getStaticPaths, getStaticPropsGenDetail as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsGenDetail>) => {
    const [showAll, setShowAll] = useState(false);
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const [showModal, setShowModal] = useState(false);
    const [email, setEmail] = useState("");

    const sendEmailSubscription = () => {
        axios.post('https://api.echomedi.com' + '/api/packages/subscribeInfo', {
            "email": email,
        })
            .then(function (response) {
                toast.success('Thành công');
            })
            .catch(function (error) {
            });
    }

    const [showMore, setShowMore] = useState(false);

    const toggleText = () => {
        setShowMore(!showMore);
    };

    const vn_Text = "Mỗi hệ gen đều mang thông tin mã hóa cho tất cả đặc điểm của mỗi người và thể hiện nguy cơ phát triển bệnh lý trong tương lai. Trên cơ sở đó, ECHO MEDI hợp tác cùng Genetica triển khai các gói chăm sóc sức khỏe kết hợp phương pháp giải mã gen. Đây là bước cải tiến đột phá trong mô hình bác sĩ gia đình, ứng dụng trí tuệ nhân tạo và công nghệ sinh học từ Hoa Kỳ kết hợp với chuyên môn y tế. Genetica là công ty Xét nghiệm gen ứng dụng Trí tuệ nhân tạo và Công nghệ sinh học cung cấp các dịch vụ giải mã gen, cung cấp các thông tin về thể chất, hành vi, trí tuệ, và nguy cơ sức khỏe dựa trên nền tảng khoa học. Hợp tác với Illumina và Thermo Fisher - 2 tổ chức giải mã gen hàng đầu thế giới, Genetica đã tạo ra chip giải mã gen dành riêng cho người Châu Á. Mục tiêu của ECHO MEDI và Genetica là tầm soát nhằm phát hiện sớm các nguy cơ bệnh lý, từ đó thiết lập kế hoạch chăm sóc và theo dõi sức khỏe dài hạn, đồng hành xuyên suốt cùng khách hàng nhằm cải thiện chất lượng sống. Sau khi khách hàng phân tích gen và biết được những nguy cơ phát triển bệnh lý, ECHO MEDI sẽ thực hiện các hoạt động thăm khám, chăm sóc nhằm phòng ngừa bệnh tật, hướng đến  việc xây dựng một sức khỏe toàn diện. Không “đứng yên” sau khi có kết quả phân tích gen, khách hàng sẽ được một đội ngũ nhân viên y tế tư vấn, quản lý sức khỏe để có thể thay đổi lối sống, thói quen, dinh dưỡng, vận động… Đến với các dịch vụ dưới đây, khách hàng sẽ có trong tay giải pháp tối ưu hóa về kế hoạch phát triển cá nhân cũng như bảo vệ sức khỏe cho chính bản thân bạn và gia đình.";

    const en_Text = "Each set of genes carries information that encodes all of a person's characteristics and represents the risk of future development of the disease. On that basis, ECHO MEDI cooperates with Genetica to deploy healthcare packages associated with gene decoding services. This is a breakthrough improvement in the family doctor model, the application of artificial intelligence and biotechnology from the United States combined with medical expertise. Genetica is an Artificial Intelligence and Biotechnology Gene Testing company that provides science-based genetic sequencing services that reveal physical, behavioral, intellectual, and health risk information. In partnership with Illumina and Thermo Fisher - two of the world's leading genomics organizations, Genetica has created genetic sequencing chips specifically for Asians. ECHO MEDI and Genetica aim to screen for early detection of pathological risks, thereby establishing a long-term health monitoring and care plan, accompanying customers throughout to improve their quality of life. After customers analyze their genes and understand the risks of developing diseases, ECHO MEDI will carry out examination and care activities to prevent illnesses and build comprehensive health. Not \"standing still\" after obtaining genetic analysis results, customers will be provided with a medical professional team on managing their health through changing lifestyles, habits, nutrition, exercise, etc. With the following services, customers will have a solution to optimize their personal development plan as well as protect the health of themselves and their families."

    const text = locale === 'en' ? en_Text : vn_Text;

    return (
        <>
            <Head>
                <title>{locale === "en" ? props.en_label : props.label}</title>
                <meta
                    name="description"
                    content="ECHO MEDI"
                />
                <meta name="keywords" content="ECHO MEDI"></meta>
                <link rel="icon" href="/favicon1.png" />
            </Head>
            <div className='h-[500px] absolute pt-[230px] text-green-900 text-lg leading-9 hidden lg:block'>
                <h1 className='w-[45%] px-[100px]'>
                    {locale === "en" ? "ECHO MEDI comprehensive cooperation with Genetica " : "ECHO MEDI hợp tác toàn diện cùng Genetica "}
                    <span className='font-bold'>{locale === "en" ? "offering Gene Decoding service for all ages." : "mang đến dịch vụ Giải mã gen dành cho mọi lứa tuổi."}</span>
                </h1>
                <button onClick={e => setShowModal(true)}
                    className='ml-[100px] mt-5 font-bold flex px-6 py-2.5 text-white leading-tight uppercase rounded-full shadow-md hover:bg-green-700 hover:shadow-lg focus:bg-green-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg transition duration-150 ease-in-out bg-green-800'>
                    {locale === "en" ? "Book now" : "Đặt lịch"}
                </button>
                <Modal
                    showCloseButton
                    visibleModal={showModal}
                    wrapperClassName="!w-[400px]  mt-28"
                    contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}>
                    <p className="text-xl text-center mb-4 font-bold text-green-900">{locale === "en" ? "Book an Appointment" : "Đặt lịch khám"}</p>
                    <p style={{ width: "310px" }}
                        className="text-lg text-center mx-auto">{locale === "en" ? "Please indicate whether you'd like to book home visit or clinic visit" : "Vui lòng lựa chọn phương thức khám tại nhà hoặc phòng khám:"}</p>
                    <div className="flex flex-col justify-center gap-x-40 mt-4">
                        <LinkComponent href={"/booking/"} skipLocaleHandling={false} locale={""}>
                            <Button btnType="primary" onClick={() => { setShowModal(false) }}
                                style={{ width: "270px", height: "50px", borderRadius: "50px" }}
                                className="mb-2 m-auto text-sm font-normal bg-green-800 shadow-lg shadow-gray-500/40" type={undefined} icon={undefined}>
                                <svg width="15px" height="15px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" className="iconify iconify--emojione" preserveAspectRatio="xMidYMid meet">
                                    <path d="M40 18H14c-2.2 0-4 1.8-4 4v38h34V22c0-2.2-1.8-4-4-4" fill="#e8e8e8">
                                    </path>
                                    <g fill="#62727a">
                                        <path d="M60 36H26c-1.1 0-2 .9-2 2v22h38V38c0-1.1-.9-2-2-2">
                                        </path>
                                        <path d="M7 36c-.6 0-1.2.4-1.6.8l-2.9 4.3c-.3.5-.5 1.4-.5 1.9v17h8V36H7">
                                        </path>
                                    </g>
                                    <path fill="#b4d7ee" d="M12 53h10v7H12z">
                                    </path>
                                    <path d="M63 60H1c-.6 0-1 .5-1 1v2c0 .6.4 1 1 1h62c.5 0 1-.4 1-1v-2c0-.5-.5-1-1-1" fill="#62727a">
                                    </path>
                                    <g fill="#b4d7ee">
                                        <path d="M14 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M26 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M30 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M14 42.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 42.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 42.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M14 50.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 50.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 50.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M32 44h30v16H32z">
                                        </path>
                                        <path d="M7 46.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M7 55.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                    </g>
                                    <g fill="#ffffff">
                                        <path d="M21 53h-9v7h1v-6h3.5v6h1v-6H21v6h1v-7z">
                                        </path>
                                        <path d="M17.5 56h-1c-.3 0-.5.2-.5.5v1c0 .3.2.5.5.5h1c.3 0 .5-.2.5-.5v-1c0-.3-.2-.5-.5-.5">
                                        </path>
                                    </g>
                                    <path fill="#b2c1c0" d="M13 60h8v2h-8z">
                                    </path>
                                    <path fill="#e8e8e8" d="M13 62h8v2h-8z">
                                    </path>
                                    <path d="M33 44h29v-2H31c-.5 0-1 .5-1 1v17h2v-3c0-.5.5-1 1-1h29v-2H33c-.5 0-1-.4-1-1v-2c0-.5.5-1 1-1h29v-2H33c-.5 0-1-.5-1-1v-2c0-.5.5-1 1-1" fill="#ffffff">
                                    </path>
                                    <g fill="#b4d7ee">
                                        <path d="M14 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M26 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M30 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M34 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M34 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M38 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M38 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M42 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M42 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                    </g>
                                    <path fill="#b2c1c0" d="M19 8h16v10H19z">
                                    </path>
                                    <circle cx="27" cy="8" r="8" fill="#e8e8e8">
                                    </circle>
                                    <path d="M32 6h-3V3c0-.5-.5-1-1-1h-2c-.5 0-1 .5-1 1v3h-3c-.5 0-1 .4-1 1v2c0 .6.5 1 1 1h3v3c0 .6.5 1 1 1h2c.5 0 1-.4 1-1v-3h3c.5 0 1-.4 1-1V7c0-.6-.5-1-1-1" fill="#f15744">
                                    </path>
                                </svg>
                                &ensp;
                                {locale == "en" ? "Book Clinic Visit" : "Đặt lịch tại phòng khám"}</Button>
                        </LinkComponent>
                        <div className="w-[250px] mx-auto flex my-3 text-xs items-center text-gray-500">
                            <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                            {locale == "en" ? "OR" : "HOẶC"}
                            <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                        </div>
                        <LinkComponent href={"/booking/"} skipLocaleHandling={false} locale={""}>
                            <Button btnType="primary" onClick={() => { setShowModal(false) }}
                                style={{ width: "270px", height: "50px", borderRadius: "50px" }}
                                className="mb-2 m-auto text-sm font-normal bg-green-800 shadow-lg shadow-gray-500/40" type={undefined} icon={undefined}>

                                <svg width="15px" height="15px" viewBox="0 0 1024 1024" className="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M896 832H128V490.666667L512 128l384 362.666667z" fill="#E8EAF6" /><path d="M832 448l-106.666667-106.666667V192h106.666667zM128 832h768v106.666667H128z" fill="#C5CAE9" /><path d="M512 91.733333L85.333333 488.533333l42.666667 46.933334L512 179.2l384 356.266667 42.666667-46.933334z" fill="#B71C1C" /><path d="M384 597.333333h256v341.333334H384z" fill="#D84315" /><path d="M448 362.666667h128v128h-128z" fill="#01579B" /><path d="M586.666667 757.333333c-6.4 0-10.666667 4.266667-10.666667 10.666667v42.666667c0 6.4 4.266667 10.666667 10.666667 10.666666s10.666667-4.266667 10.666666-10.666666v-42.666667c0-6.4-4.266667-10.666667-10.666666-10.666667z" fill="#FF8A65" />
                                </svg>&ensp;
                                {locale == "en" ? "Book Home Visit" : "Đặt lịch khám tại nhà"}</Button>
                        </LinkComponent>
                        <div className="w-[250px] mx-auto flex my-3 text-xs items-center text-green-500">
                            <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                            {locale == "en" ? "OR" : "HOẶC"}
                            <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                        </div>
                        <Button btnType="primary" onClick={() => { setShowModal(false) }}
                            style={{ width: "270px", height: "50px", borderRadius: "50px" }}
                            className="mb-3 m-auto text-sm font-normal bg-white shadow-lg shadow-gray-500/40" type={undefined} icon={undefined}>
                            <svg width="20px" height="20px" viewBox="0 0 120 120" id="Layer_1" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path style={{ fill: "#A0D995" }}
                                        className="st0" d="M74.9,77.7l-3.2,3.2c-4.9,4.9-9.8,8.1-13.8,4.1L35.1,62.1c-4.1-4.1-0.9-8.9,4.1-13.8l3.2-3.2L26.7,29.5   l-4.5,4.5c-8,8-8,20.9,0,28.8l35,35c8,8,20.9,8,28.8,0l4.5-4.5L74.9,77.7z" />
                                    <g>
                                        <path style={{ fill: "#6CC4A1" }}
                                            className="st1" d="M45,43.7L28.4,27.1c-1.5-1.5-4-1.5-5.5,0l-2.8,2.8c-1.5,1.5-1.5,4,0,5.5L36.7,52c1.5,1.5,4,1.5,5.5,0l2.8-2.8    C46.5,47.7,46.5,45.3,45,43.7z" />
                                        <path style={{ fill: "#6CC4A1" }}
                                            className="st1" d="M92.9,91.6L76.3,75c-1.5-1.5-4-1.5-5.5,0L68,77.8c-1.5,1.5-1.5,4,0,5.5l16.6,16.6c1.5,1.5,4,1.5,5.5,0    l2.8-2.8C94.4,95.6,94.4,93.2,92.9,91.6z" />
                                    </g>
                                    <g>
                                        <path style={{ fill: "#F6E3C5" }}
                                            className="st2" d="M57.1,46.6c-0.4-0.4-0.7-0.9-0.8-1.4c-0.4-1.6,0.6-3.1,2.2-3.5c5.6-1.3,11.3,0.4,15.3,4.4s5.7,9.8,4.4,15.3    c-0.4,1.6-1.9,2.5-3.5,2.2c-1.6-0.4-2.5-1.9-2.2-3.5c0.8-3.6-0.2-7.3-2.8-9.9c-2.6-2.6-6.3-3.7-9.9-2.8    C58.8,47.6,57.8,47.3,57.1,46.6z" />
                                        <path style={{ fill: "#F6E3C5" }}
                                            className="st2" d="M54.6,35.2c-0.4-0.4-0.7-0.9-0.8-1.4c-0.4-1.6,0.6-3.1,2.2-3.5c9.5-2.2,19.3,0.6,26.2,7.5    c6.9,6.9,9.7,16.6,7.5,26.2c-0.4,1.6-1.9,2.6-3.5,2.2c-1.6-0.4-2.6-1.9-2.2-3.5c1.7-7.5-0.5-15.3-5.9-20.7    c-5.4-5.4-13.2-7.7-20.7-5.9C56.3,36.2,55.3,35.9,54.6,35.2z" />
                                        <path style={{ fill: "#F6E3C5" }}
                                            className="st2" d="M52.1,23.8c-0.4-0.4-0.7-0.9-0.8-1.4c-0.4-1.6,0.6-3.1,2.2-3.5c13.4-3.1,27.3,0.9,37,10.6    c9.7,9.7,13.7,23.5,10.6,37c-0.4,1.6-1.9,2.6-3.5,2.2c-1.6-0.4-2.6-1.9-2.2-3.5c2.6-11.5-0.8-23.3-9-31.5    c-8.3-8.3-20.1-11.7-31.5-9C53.8,24.8,52.8,24.5,52.1,23.8z" />
                                    </g>
                                </g>
                            </svg>&ensp;
                            <p className="text-green-800">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                        </Button>
                    </div>
                </Modal>
            </div>
            <div className='absolute m-6 w-[40%] justify-between lg:hidden block'>
                <h1 className='text-[11px] pt-4 text-[#14532D]'>{locale === "en" ? "ECHO MEDI comprehensive cooperation with Genetica " : "ECHO MEDI hợp tác toàn diện cùng Genetica "}
                    <span className='font-bold'>{locale === "en" ? "offering Gene Decoding service for all ages." : "mang đến dịch vụ Giải mã gen dành cho mọi lứa tuổi."}</span></h1>
                <button onClick={e => setShowModal(true)}
                    className='font-bold text-xs mt-3 flex px-4 py-2.5 text-white leading-tight uppercase rounded-full shadow-md hover:bg-green-700 hover:shadow-lg focus:bg-green-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg transition duration-150 ease-in-out bg-green-800'>
                    {locale === "en" ? "Book now" : "Đặt lịch"}
                </button>
                <Modal
                    showCloseButton
                    visibleModal={showModal}
                    wrapperClassName="!w-[350px]"
                    contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}>
                    <p className="text-xl text-center mb-4 font-bold text-green-900">{locale === "en" ? "Book an Appointment" : "Đặt lịch khám"}</p>
                    <p style={{ width: "250px" }}
                        className="text-lg text-center mx-auto">{locale === "en" ? "Please indicate whether you'd like to book home visit or clinic visit" : "Vui lòng lựa chọn phương thức khám tại nhà hoặc phòng khám:"}</p>
                    <div className="flex flex-col justify-center gap-x-40 mt-4">
                        <LinkComponent href={"/booking/"} skipLocaleHandling={false} locale={""}>
                            <Button btnType="primary" onClick={() => { setShowModal(false) }}
                                style={{ width: "250px", height: "50px", borderRadius: "50px" }}
                                className="mb-2 m-auto text-sm font-normal bg-green-800 shadow-lg shadow-gray-500/40" type={undefined} icon={undefined}>
                                <svg width="15px" height="15px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" className="iconify iconify--emojione" preserveAspectRatio="xMidYMid meet">
                                    <path d="M40 18H14c-2.2 0-4 1.8-4 4v38h34V22c0-2.2-1.8-4-4-4" fill="#e8e8e8">
                                    </path>
                                    <g fill="#62727a">
                                        <path d="M60 36H26c-1.1 0-2 .9-2 2v22h38V38c0-1.1-.9-2-2-2">
                                        </path>
                                        <path d="M7 36c-.6 0-1.2.4-1.6.8l-2.9 4.3c-.3.5-.5 1.4-.5 1.9v17h8V36H7">
                                        </path>
                                    </g>
                                    <path fill="#b4d7ee" d="M12 53h10v7H12z">
                                    </path>
                                    <path d="M63 60H1c-.6 0-1 .5-1 1v2c0 .6.4 1 1 1h62c.5 0 1-.4 1-1v-2c0-.5-.5-1-1-1" fill="#62727a">
                                    </path>
                                    <g fill="#b4d7ee">
                                        <path d="M14 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M26 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M30 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M14 42.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 42.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 42.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M14 50.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 50.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 50.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M32 44h30v16H32z">
                                        </path>
                                        <path d="M7 46.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M7 55.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                    </g>
                                    <g fill="#ffffff">
                                        <path d="M21 53h-9v7h1v-6h3.5v6h1v-6H21v6h1v-7z">
                                        </path>
                                        <path d="M17.5 56h-1c-.3 0-.5.2-.5.5v1c0 .3.2.5.5.5h1c.3 0 .5-.2.5-.5v-1c0-.3-.2-.5-.5-.5">
                                        </path>
                                    </g>
                                    <path fill="#b2c1c0" d="M13 60h8v2h-8z">
                                    </path>
                                    <path fill="#e8e8e8" d="M13 62h8v2h-8z">
                                    </path>
                                    <path d="M33 44h29v-2H31c-.5 0-1 .5-1 1v17h2v-3c0-.5.5-1 1-1h29v-2H33c-.5 0-1-.4-1-1v-2c0-.5.5-1 1-1h29v-2H33c-.5 0-1-.5-1-1v-2c0-.5.5-1 1-1" fill="#ffffff">
                                    </path>
                                    <g fill="#b4d7ee">
                                        <path d="M14 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M18 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M22 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M26 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M30 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M34 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M34 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M38 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M38 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M42 34.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                        <path d="M42 26.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5v-5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5v5">
                                        </path>
                                    </g>
                                    <path fill="#b2c1c0" d="M19 8h16v10H19z">
                                    </path>
                                    <circle cx="27" cy="8" r="8" fill="#e8e8e8">
                                    </circle>
                                    <path d="M32 6h-3V3c0-.5-.5-1-1-1h-2c-.5 0-1 .5-1 1v3h-3c-.5 0-1 .4-1 1v2c0 .6.5 1 1 1h3v3c0 .6.5 1 1 1h2c.5 0 1-.4 1-1v-3h3c.5 0 1-.4 1-1V7c0-.6-.5-1-1-1" fill="#f15744">
                                    </path>
                                </svg>
                                &ensp;
                                {locale == "en" ? "Book Clinic Visit" : "Đặt lịch tại phòng khám"}</Button>
                        </LinkComponent>
                        <div className="w-[250px] mx-auto flex my-3 text-xs items-center text-gray-500">
                            <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                            {locale == "en" ? "OR" : "HOẶC"}
                            <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                        </div>
                        <LinkComponent href={"/booking/"} skipLocaleHandling={false} locale={""}>
                            <Button btnType="primary" onClick={() => { setShowModal(false) }}
                                style={{ width: "250px", height: "50px", borderRadius: "50px" }}
                                className="mb-2 m-auto text-sm font-normal bg-green-800 shadow-lg shadow-gray-500/40" type={undefined} icon={undefined}>

                                <svg width="15px" height="15px" viewBox="0 0 1024 1024" className="icon" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M896 832H128V490.666667L512 128l384 362.666667z" fill="#E8EAF6" /><path d="M832 448l-106.666667-106.666667V192h106.666667zM128 832h768v106.666667H128z" fill="#C5CAE9" /><path d="M512 91.733333L85.333333 488.533333l42.666667 46.933334L512 179.2l384 356.266667 42.666667-46.933334z" fill="#B71C1C" /><path d="M384 597.333333h256v341.333334H384z" fill="#D84315" /><path d="M448 362.666667h128v128h-128z" fill="#01579B" /><path d="M586.666667 757.333333c-6.4 0-10.666667 4.266667-10.666667 10.666667v42.666667c0 6.4 4.266667 10.666667 10.666667 10.666666s10.666667-4.266667 10.666666-10.666666v-42.666667c0-6.4-4.266667-10.666667-10.666666-10.666667z" fill="#FF8A65" />
                                </svg>&ensp;
                                {locale == "en" ? "Book Home Visit" : "Đặt lịch khám tại nhà"}</Button>
                        </LinkComponent>
                        <div className="w-[250px] mx-auto flex my-3 text-xs items-center text-green-500">
                            <div className="flex-grow border-t border-gray-200 h-px mr-3"></div>
                            {locale == "en" ? "OR" : "HOẶC"}
                            <div className="flex-grow border-t border-gray-200 h-px ml-3"></div>
                        </div>
                        <Button btnType="primary" onClick={() => { setShowModal(false) }}
                            style={{ width: "250px", height: "50px", borderRadius: "50px" }}
                            className="mb-3 m-auto text-sm font-normal bg-white shadow-lg shadow-gray-500/40" type={undefined} icon={undefined}>
                            <svg width="20px" height="20px" viewBox="0 0 120 120" id="Layer_1" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path style={{ fill: "#A0D995" }}
                                        className="st0" d="M74.9,77.7l-3.2,3.2c-4.9,4.9-9.8,8.1-13.8,4.1L35.1,62.1c-4.1-4.1-0.9-8.9,4.1-13.8l3.2-3.2L26.7,29.5   l-4.5,4.5c-8,8-8,20.9,0,28.8l35,35c8,8,20.9,8,28.8,0l4.5-4.5L74.9,77.7z" />
                                    <g>
                                        <path style={{ fill: "#6CC4A1" }}
                                            className="st1" d="M45,43.7L28.4,27.1c-1.5-1.5-4-1.5-5.5,0l-2.8,2.8c-1.5,1.5-1.5,4,0,5.5L36.7,52c1.5,1.5,4,1.5,5.5,0l2.8-2.8    C46.5,47.7,46.5,45.3,45,43.7z" />
                                        <path style={{ fill: "#6CC4A1" }}
                                            className="st1" d="M92.9,91.6L76.3,75c-1.5-1.5-4-1.5-5.5,0L68,77.8c-1.5,1.5-1.5,4,0,5.5l16.6,16.6c1.5,1.5,4,1.5,5.5,0    l2.8-2.8C94.4,95.6,94.4,93.2,92.9,91.6z" />
                                    </g>
                                    <g>
                                        <path style={{ fill: "#F6E3C5" }}
                                            className="st2" d="M57.1,46.6c-0.4-0.4-0.7-0.9-0.8-1.4c-0.4-1.6,0.6-3.1,2.2-3.5c5.6-1.3,11.3,0.4,15.3,4.4s5.7,9.8,4.4,15.3    c-0.4,1.6-1.9,2.5-3.5,2.2c-1.6-0.4-2.5-1.9-2.2-3.5c0.8-3.6-0.2-7.3-2.8-9.9c-2.6-2.6-6.3-3.7-9.9-2.8    C58.8,47.6,57.8,47.3,57.1,46.6z" />
                                        <path style={{ fill: "#F6E3C5" }}
                                            className="st2" d="M54.6,35.2c-0.4-0.4-0.7-0.9-0.8-1.4c-0.4-1.6,0.6-3.1,2.2-3.5c9.5-2.2,19.3,0.6,26.2,7.5    c6.9,6.9,9.7,16.6,7.5,26.2c-0.4,1.6-1.9,2.6-3.5,2.2c-1.6-0.4-2.6-1.9-2.2-3.5c1.7-7.5-0.5-15.3-5.9-20.7    c-5.4-5.4-13.2-7.7-20.7-5.9C56.3,36.2,55.3,35.9,54.6,35.2z" />
                                        <path style={{ fill: "#F6E3C5" }}
                                            className="st2" d="M52.1,23.8c-0.4-0.4-0.7-0.9-0.8-1.4c-0.4-1.6,0.6-3.1,2.2-3.5c13.4-3.1,27.3,0.9,37,10.6    c9.7,9.7,13.7,23.5,10.6,37c-0.4,1.6-1.9,2.6-3.5,2.2c-1.6-0.4-2.6-1.9-2.2-3.5c2.6-11.5-0.8-23.3-9-31.5    c-8.3-8.3-20.1-11.7-31.5-9C53.8,24.8,52.8,24.5,52.1,23.8z" />
                                    </g>
                                </g>
                            </svg>&ensp;
                            <p className="text-green-800">{locale == "en" ? "Contact" : "Liên hệ:"} 1900 638 408</p>
                        </Button>
                    </div>
                </Modal>
            </div>
            <img className='w-full' src="https://d3e4m6b6rxmux9.cloudfront.net/download_649ef0fdc3.png?updated_at=2023-07-12T18:04:18.984Z" />
            <div className='mt-[50px] hidden lg:block'>
                <div className='grid grid-cols-6 w-[70%] mx-auto'>
                    <div className='col-span-1 bg-[#F0F0F0] py-6 border-right rounded-s-xl border-r-2'>
                        <svg className='m-auto'
                            fill="#000000" width="80px" height="80px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M6.121,10.7A6.462,6.462,0,0,0,3,16c0,3.859,4.037,7,9,7s9-3.141,9-7a6.58,6.58,0,0,0-3.121-5.3h0A6.721,6.721,0,0,1,15,5.04V3h1a1,1,0,0,0,0-2H8A1,1,0,0,0,8,3H9V5.04A6.724,6.724,0,0,1,6.121,10.7ZM12,21c-3.171,0-6.9-1.8-6.992-4.892a9.692,9.692,0,0,0,6.363-.179,14.315,14.315,0,0,1,7.539-.673l.008.035C19.629,18.547,15.857,21,12,21ZM13,3V5.04a8.713,8.713,0,0,0,3.768,7.318,6.1,6.1,0,0,1,.91.744,15.626,15.626,0,0,0-7.049.969,7.644,7.644,0,0,1-5.122.1,5.388,5.388,0,0,1,1.725-1.808A8.716,8.716,0,0,0,11,5.04V3Zm2,15a1,1,0,1,1-1-1A1,1,0,0,1,15,18Zm-4-7a1,1,0,1,1,1,1A1,1,0,0,1,11,11Z" /></svg>
                    </div>
                    <div className='col-span-5 bg-[#F0F0F0] p-6 rounded-tr-xl rounded-br-xl'>
                        <p className='font-bold mb-2 text-lg'>
                            {locale === "en" ? "The future of healthcare isn't just about cutting-edge devices..." : "Tương lai của y tế không chỉ là các thiết bị tiên tiến..."}
                        </p>
                        <span className='text-[14px]'>
                            {locale === "en" ? "But medical expertise combined with the results of personal genetic decoding using artificial intelligence and biotechnology." : "Mà bằng chuyên môn y tế kết hợp với kết quả giải mã gen cá nhân ứng dụng trí tuệ thông minh nhân tạo và công nghệ sinh học."}
                        </span>
                    </div>
                </div>
            </div>
            <div className='my-10 hidden lg:block'>
                <div className='grid grid-cols-6 w-[70%] mx-auto'>
                    <div className='col-span-1 bg-[#F0F0F0] py-6 border-right rounded-s-xl border-r-2'>
                        <svg className='mx-auto'
                            fill="#000000" height="80px" width="80px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 488.896 488.896" xmlSpace="preserve">
                            <g>
                                <g>
                                    <path d="M470.182,252.398l-77.9-10l-40.5-96.4l49.6-63.6c7.3-9.4,5.2-22.9-4.2-30.2s-22.9-5.2-30.2,4.2l-48.4,61l-108.1-15.1
      l-30.1-72.9c-4.2-10.4-16.7-15.6-28.1-11.5c-11.5,4.2-16.7,17.7-11.5,27.1l29.4,71.3l-70.1,90.6l-76-9.7c-11.5-1-22.9,7.3-24,18.8
      c-1,11.5,7.3,22.9,18.8,24l77.4,9.9l41.4,98.6l-46.9,60.2c-7.3,9.4-5.2,22.9,4.2,30.2c9.4,7.3,22.9,5.2,30.2-4.2l45.8-58.7
      l113.5,15l28.2,68.5c4.2,10.4,16.7,15.6,28.1,11.5c10.4-4.2,15.6-16.7,11.5-28.1l-29.4-71.2l66.6-86.1l75.3,9.6
      c13.3,1.4,24.4-8.2,24-18.8C489.982,264.898,481.582,253.498,470.182,252.398z M286.682,347.298l-108.4-14.6l-42.7-100.1
      l66.7-86.5l109.4,14.6l41.7,100.1L286.682,347.298z"/>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div className='col-span-5 bg-[#F0F0F0] p-6 rounded-tr-xl rounded-br-xl'>
                        <p className='font-bold mb-2 text-lg'>
                            {locale === "en" ? "Improve the comprehensive health system with personalized healthcare services based on the results of gene decoding using artificial intelligence and biotechnology!" : "Cải tiến hệ thống y tế toàn diện bằng dịch vụ chăm sóc sức khỏe cá nhân dựa trên kết quả giải mã gen ứng dụng trí tuệ nhân tạo và công nghệ sinh học!"}
                        </p>
                        <p className='text-[14px] mb-2'>
                            {locale === "en" ? "Each set of genes carries information that encodes all of a person's characteristics and represents the risk of future development of the disease. On that basis, ECHO MEDI cooperates with Genetica to deploy healthcare packages associated with gene decoding services. This is a breakthrough improvement in the family doctor model, the application of artificial intelligence and biotechnology from the United States combined with medical expertise." : "Mỗi hệ gen đều mang thông tin mã hóa cho tất cả đặc điểm của mỗi người và thể hiện nguy cơ phát triển bệnh lý trong tương lai. Trên cơ sở đó, ECHO MEDI hợp tác cùng Genetica triển khai các gói chăm sóc sức khỏe kết hợp phương pháp giải mã gen. Đây là bước cải tiến đột phá trong mô hình bác sĩ gia đình, ứng dụng trí tuệ nhân tạo và công nghệ sinh học từ Hoa Kỳ kết hợp với chuyên môn y tế."}
                        </p>
                        <p className='text-[14px] mb-2'>
                            {locale === "en" ? "Genetica is an Artificial Intelligence and Biotechnology Gene Testing company that provides science-based genetic sequencing services that reveal physical, behavioral, intellectual, and health risk information. In partnership with Illumina and Thermo Fisher - two of the world's leading genomics organizations, Genetica has created genetic sequencing chips specifically for Asians. " : "Genetica là công ty Xét nghiệm gen ứng dụng Trí tuệ nhân tạo và Công nghệ sinh học cung cấp các dịch vụ giải mã gen, cung cấp các thông tin về thể chất, hành vi, trí tuệ, và nguy cơ sức khỏe dựa trên nền tảng khoa học. Hợp tác với Illumina và Thermo Fisher - 2 tổ chức giải mã gen hàng đầu thế giới, Genetica đã tạo ra chip giải mã gen dành riêng cho người Châu Á."}
                        </p>
                        <p className='text-[14px] mb-2'>
                            {locale === "en" ? "ECHO MEDI and Genetica aim to screen for early detection of pathological risks, thereby establishing a long-term health monitoring and care plan, accompanying customers throughout to improve their quality of life. After customers analyze their genes and understand the risks of developing diseases, ECHO MEDI will carry out examination and care activities to prevent illnesses and build comprehensive health. Not \"standing still\" after obtaining genetic analysis results, customers will be provided with a medical professional team on managing their health through changing lifestyles, habits, nutrition, exercise, etc." : "Mục tiêu của ECHO MEDI và Genetica là tầm soát nhằm phát hiện sớm các nguy cơ bệnh lý, từ đó thiết lập kế hoạch chăm sóc và theo dõi sức khỏe dài hạn, đồng hành xuyên suốt cùng khách hàng nhằm cải thiện chất lượng sống. Sau khi khách hàng phân tích gen và biết được những nguy cơ phát triển bệnh lý, ECHO MEDI sẽ thực hiện các hoạt động thăm khám, chăm sóc nhằm phòng ngừa bệnh tật, hướng đến  việc xây dựng một sức khỏe toàn diện. Không “đứng yên” sau khi có kết quả phân tích gen, khách hàng sẽ được một đội ngũ nhân viên y tế tư vấn, quản lý sức khỏe để có thể thay đổi lối sống, thói quen, dinh dưỡng, vận động…"}
                        </p>
                        <p className='text-[14px] mb-2'>
                            {locale === "en" ? "With the following services, customers will have a solution to optimize their personal development plan as well as protect the health of themselves and their families." : "Đến với các dịch vụ dưới đây, khách hàng sẽ có trong tay giải pháp tối ưu hóa về kế hoạch phát triển cá nhân cũng như bảo vệ sức khỏe cho chính bản thân bạn và gia đình."}
                        </p>
                    </div>
                </div>
            </div>
            <div className='block lg:hidden'>
                <div className='m-5 bg-[#f0f0f0] rounded-lg'>
                    <div className='p-5'>
                        <p className='font-bold mb-2 text-[15px]'>{locale === "en" ? "The future of healthcare isn't just about cutting-edge devices..." : "Tương lai của y tế không chỉ là các thiết bị tiên tiến..."}</p>
                        <p className='text-[13px] leading-5'>{locale === "en" ? "But medical expertise combined with the results of personal genetic decoding using artificial intelligence and biotechnology." : "Mà bằng chuyên môn y tế kết hợp với kết quả giải mã gen cá nhân ứng dụng trí tuệ thông minh nhân tạo và công nghệ sinh học."}</p>
                    </div>
                </div>
                <div className='m-5 bg-[#f0f0f0] rounded-lg'>
                    <div className='p-5'>
                        <p className='font-bold mb-2 text-[15px]'> {locale === "en" ? "Improve the comprehensive health system with personalized healthcare services based on the results of gene decoding using artificial intelligence and biotechnology!" : "Cải tiến hệ thống y tế toàn diện bằng dịch vụ chăm sóc sức khỏe cá nhân dựa trên kết quả giải mã gen ứng dụng trí tuệ nhân tạo và công nghệ sinh học!"}</p>
                        <p className='text-[13px] leading-5 justify-between'>
                            {showMore ? text : `${text.slice(0, 200)}...`}</p>
                        <button onClick={toggleText} className='text-[13px] underline font-bold'>
                            {showMore ? (locale === 'en' ? 'Less' : 'Thu gọn') : (locale === 'en' ? 'More' : 'Xem thêm')}
                        </button>
                    </div>
                </div>
            </div>
            <div className="bg-white py-5">
                <Tabs>
                    <TabList className="lg:flex justify-center shadow-lg max-w-[400px] m-auto mt-10 p-2 box-border border rounded-xl bg-gray-50 hidden">
                        {props.sub_packages?.map((sp: any, id: any) => (
                            <Tab key={id} className="px-[60px] py-2 text-base font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
                                {locale === "en" ? sp.en_label : sp.label}
                            </Tab>
                        ))}
                    </TabList>
                    <TabList className="flex justify-center shadow-lg max-w-[250px] m-auto mt-10 p-2 box-border border rounded-xl bg-gray-50 lg:hidden">
                        {props.sub_packages?.map((sp: any, id: any) => (
                            <Tab key={id} className="px-[20px] py-2 text-xs font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
                                {locale === "en" ? sp.en_label : sp.label}
                            </Tab>
                        ))}
                    </TabList>
                    {props.sub_packages?.map((sp: any, id: any) => (
                        <TabPanel key={id}>
                            <div className="container mx-auto lg:px-[100px] pt-6 pb-[50px]">
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4">
                                    {sp.services.map((sv: any, index: number) => {
                                        if (!showAll && index > 2) {
                                            return null;
                                        }
                                        return (

                                            <div className="group relative shadow-lg rounded-lg" id={sv.id} key={sv.id}>
                                                <div className="p-6 aspect-h-1 aspect-w-1 w-full overflow-hidden rounded-md bg-white        lg:aspect-none group-hover:opacity-75 lg:h-80">
                                                    <img src={`https://d3e4m6b6rxmux9.cloudfront.net/${sv.genetica_image.hash}${sv.genetica_image.ext}`} alt="Front of men&#039;s Basic Tee in black." className="h-full w-full object-cover object-center lg:h-full lg:w-full rounded-lg" />
                                                </div>
                                                <div className='grid grid-cols-5 px-6'>
                                                    <div className='col-span-2'>
                                                        <img src='https://d3e4m6b6rxmux9.cloudfront.net/q_U_Muiaf_10ab4fa1d8.png?updated_at=2023-06-20T06:20:46.826Z' />
                                                    </div>
                                                    <div className='col-span-1'></div>
                                                    <div className='col-span-1'></div>
                                                    <div className='col-span-1 pt-2'>
                                                        <img src='https://d3e4m6b6rxmux9.cloudfront.net/logo_2_6aa91abdd980eb0030e11f2c049238fc_a2153e7725.png?updated_at=2023-06-20T06:16:22.695Z' />
                                                    </div>
                                                </div>
                                                <div className="p-6">
                                                    <p className='text-xs text-green-800 font-semibold pb-3'>{locale === "en" ? sp.en_label : sp.label}</p>
                                                    <div className='grid grid-cols-5'>
                                                        <h5 className="col-span-3 mb-2 block font-sans text-xl font-semibold leading-snug tracking-normal antialiased">
                                                            {locale === "en" ? sv.en_label : sv.label}
                                                        </h5>
                                                    </div>
                                                    <hr />
                                                    {sv.en_specification && sv.specification &&
                                                        <p className="label-product-2 mt-4 block font-sans text-sm font-bold leading-relaxed h-[160px]">
                                                            {locale === "en" ? sv.en_specification[0] : sv.specification[0]}
                                                            <p className='flex font-normal pt-4'>
                                                                <svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <title />
                                                                    <g id="Complete">
                                                                        <g id="tick">
                                                                            <polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke="#166534" strokeLinecap="round" stroke-linejoin="round" strokeWidth="2" />
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                                <span className='pl-1'>
                                                                    {locale === "en" ? sv.en_specification[1] : sv.specification[1]}
                                                                </span>
                                                            </p>
                                                            <p className='flex font-normal'>
                                                                <svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <title />
                                                                    <g id="Complete">
                                                                        <g id="tick">
                                                                            <polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke="#166534" strokeLinecap="round" stroke-linejoin="round" strokeWidth="2" />
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                                <span className='pl-1'>
                                                                    {locale === "en" ? sv.en_specification[2] : sv.specification[2]}
                                                                </span>
                                                            </p>
                                                            <p className='flex font-normal'>
                                                                <svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <title />
                                                                    <g id="Complete">
                                                                        <g id="tick">
                                                                            <polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke="#166534" strokeLinecap="round" stroke-linejoin="round" strokeWidth="2" />
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                                <span className='pl-1'>
                                                                    {locale === "en" ? sv.en_specification[3] : sv.specification[3]}
                                                                </span>
                                                            </p>
                                                        </p>
                                                    }
                                                </div>
                                                <div className="px-6 pb-6 pt-0">
                                                    <LinkComponent href={"/gen_detail/" + sv.slug} skipLocaleHandling={false} locale={""}>
                                                        <button
                                                            className="w-full select-none rounded-lg bg-green-800 py-3 px-6 text-center align-middle font-sans text-sm font-bold text-white shadow-md  transition-all hover:shadow-lg focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none mb-3"
                                                            type="button"
                                                            data-ripple-light="true"
                                                        >
                                                            {locale === "en" ? "View detail" : "Xem chi tiết"}
                                                        </button>
                                                    </LinkComponent>
                                                </div>
                                            </div>

                                        );

                                    })}</div>
                                {sp.services.length > 3 && (
                                    <div className="flex justify-center mt-[70px]">
                                        {showAll ? (
                                            <button
                                                className="text-gray-800 bg-white border border-gray-300 focus:outline-none text-sm px-5 py-2.5 mr-2 mb-2"
                                                onClick={() => setShowAll(false)}
                                            >
                                                <a className='flex'>
                                                    <span>{locale === "en" ? "Show less" : "Ẩn bớt"}</span>
                                                    <svg className="mt-[5px] ml-[8px]" fill="#000000" height="10px" width="10px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                                                        viewBox="0 0 407.436 407.436" xmlSpace="preserve">
                                                        <polygon points="203.718,91.567 0,294.621 21.179,315.869 203.718,133.924 386.258,315.869 407.436,294.621 " />
                                                    </svg>
                                                </a>
                                            </button>
                                        ) : (
                                            <button
                                                className="text-gray-800 bg-white border border-gray-300 focus:outline-none text-sm px-5 py-2.5 mr-2 mb-2"
                                                onClick={() => setShowAll(true)}
                                            >
                                                <a className='flex'>
                                                    <span>{locale === "en" ? "Show all" : "Xem tất cả"}</span>
                                                    <svg className="mt-[5px] ml-[8px]" fill="#000000" height="10px" width="10px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                                                        viewBox="0 0 407.437 407.437" xmlSpace="preserve">
                                                        <polygon points="386.258,91.567 203.718,273.512 21.179,91.567 0,112.815 203.718,315.87 407.437,112.815 " />
                                                    </svg>
                                                </a>
                                            </button>
                                        )}
                                    </div>
                                )}
                            </div>
                        </TabPanel>
                    ))}
                </Tabs>
            </div>
            <div className="mx-auto rounded-3xl px-4 pb-10 bg-white">
                <p className="text-center text-xl font-light">{locale === "en" ? "Subscribe To Our Newsletter:" : "Đăng ký nhận thông tin của chúng tôi:"}</p>
                <div className="mx-auto mt-4 flex max-w-xl flex-col border-gray-600 bg-white sm:flex-row sm:rounded-full sm:border">
                    <input
                        value={email}
                        onChange={e => {
                            setEmail(e.target.value)
                        }}
                        className="m-2 h-12 rounded-full px-4 text-gray-500 sm:w-full focus:outline-none" placeholder={locale === "en" ? "Your Email Address ..." : "Địa chỉ email của bạn"} type="email" name="email" />
                    <button
                        onClick={() => sendEmailSubscription()}
                        className="shrink-0 m-2 rounded-full bg-green-800 px-8 py-3 text-white focus:bg-green-800 focus:outline-none hover:bg-green-900 font-bold">{locale === "en" ? "Subscribe Now" : "Đăng ký ngay"}</button>
                </div>
            </div>
            <Contact />
        </>
    );
};

function numberWithCommas(x: number) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Blog;