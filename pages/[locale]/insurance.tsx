import { NextPage } from 'next';
import Image from 'next/image'
import { useRouter } from 'next/router';
import React, { useState } from 'react'
import { shimmer, toBase64 } from '../../lib/ui';
import { Toaster } from "react-hot-toast";
import Contact from "../../components/Contact/Contact";
import useIsMobile from '../../utils/detectMob';
import { makeStaticProps } from '../../lib/getStatic';
import Head from 'next/head';

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }
const InsurancePages: NextPage = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const [showModal, setShowModal] = useState(false);

    const handleShowModal = () => {
        setShowModal(!showModal);
    };


    return (
        <>
            <Head>
                <title>Bảo Lãnh Viện Phí - Echo Medi</title>
                <meta
                    name="description"
                    content="Tìm hiểu cách Echo Medi hợp tác với doanh nghiệp, cung cấp giải pháp y tế hiện đại, dịch vụ chuyên nghiệp và nâng cao chất lượng chăm sóc sức khỏe cho khách hàng của bạn."
                />
                <meta
                    name="keywords"
                    content="Echo Medi, giải pháp doanh nghiệp, dịch vụ y tế, chăm sóc sức khỏe, đối tác doanh nghiệp"
                />
                <meta property="og:title" content="Bảo Lãnh Viện Phí - Echo Medi" />
                <meta
                    property="og:description"
                    content="Tìm hiểu cách Echo Medi hợp tác với doanh nghiệp, cung cấp giải pháp y tế hiện đại, dịch vụ chuyên nghiệp và nâng cao chất lượng chăm sóc sức khỏe cho khách hàng của bạn."
                />
                <meta
                    property="og:image"
                    content="https://d3e4m6b6rxmux9.cloudfront.net/QUY_TRINH_BAO_LANH_VIEN_PHI_NGOAI_TRU_8553851402.svg"
                />
                <meta property="og:type" content="website" />
                <meta property="twitter:card" content="summary_large_image" />
                <meta property="twitter:title" content="Bảo Lãnh Viện Phí - Echo Medi" />
                <meta
                    property="twitter:description"
                    content="Tìm hiểu cách Echo Medi hợp tác với doanh nghiệp, cung cấp giải pháp y tế hiện đại, dịch vụ chuyên nghiệp và nâng cao chất lượng chăm sóc sức khỏe cho khách hàng của bạn."
                />
                <meta
                    property="twitter:image"
                    content="https://d3e4m6b6rxmux9.cloudfront.net/QUY_TRINH_BAO_LANH_VIEN_PHI_NGOAI_TRU_8553851402.svg"
                />
            </Head>

            <section>
                <div className="mx-auto">
                    <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                        <div className="w-full relative">
                            <Image
                                alt="Image Bảo Hiểm"
                                width={1280}
                                height={400}
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1280, 400))}`}
                                className='object-cover w-full'
                                src={locale === "vi" ? "/banner/banner_insurance.webp" : "/banner/banner_insurance_en.webp"}
                            />
                        </div>
                        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 2xl:w-1/2 absolute md:px-16 px-4">
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block">
                                {locale === "vi" ? "BẢO HIỂM" : "Insurance"}
                            </h2>
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                                {locale === "vi" ? "BẢO HIỂM" : "Insurance"}
                            </h2>
                        </div>
                    </div>
                </div>
            </section>
            <div className="2xl:container 2xl:mx-auto lg:pb-12 lg:px-20 md:py-4 md:px-6 pb-9 px-4 pt-2">
                <h2 className="text-center mt-4 md:mt-0 font-bold md:text-2xl text-xl text-[#156634]">{locale === "vi" ? "Đồng Hành Cùng Đơn Vị Bảo Hiểm" : 'Partnerdship With Insurance'}</h2>
                <h2 className="text-center font-bold text-lg">{locale === "vi" ? "Nâng Tầm Chăm Sóc Sức Khỏe Toàn Diện" : 'Enhancing Comprehensive Healthcare'}</h2>
                <div className="grid gap-8 grid-cols-1 md:grid-cols-3 mt-4">
                    <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] md:mt-4 bg-white">
                        <div className="flex items-center md:justify-start justify-center">
                            <Image loading='lazy' width={60} height={60} alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/Bao_Hiem_Icon_1_fda810198c.png" />
                        </div>
                        <h1 className="font-bold text-center md:text-left text-xl text-[#156634] my-2">{locale === "en" ? "Collaborating to offer comprehensive healthcare services" : "Hợp tác cung cấp dịch vụ"}</h1>
                        <p className="md:text-base text-sm">{locale === "en" ? "Ensuring customers have access to high-quality and convenient healthcare services." : "Giúp khách hàng tiếp cận dịch vụ chăm sóc sức khỏe chất lượng và tiện lợi."}</p>
                    </div>
                    <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] md:mt-4 bg-white">
                        <div className="flex items-center md:justify-start justify-center">
                            <Image loading='lazy' width={60} height={60} alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/Bao_Hiem_Icon_2_c1a764e90b.png" />
                        </div>
                        <h1 className="font-bold text-xl text-[#156634] text-center md:text-left my-2">{locale === "en" ? "Optimizing insurance benefits for customers." : "Tối ưu quyền lợi cho khách hàng"}</h1>
                        <p className="md:text-base text-sm">{locale === "en" ? "Helping customers maximize benefits from insurance packages, reduce costs, and enhance health protection." : "Hỗ trợ khách hàng sử dụng hiệu quả các quyền lợi từ gói bảo hiểm, giảm chi phí và cung cấp giải pháp duy trì sức khỏe toàn diện."}</p>
                    </div>
                    <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] md:mt-4 bg-white">
                        <div className="flex items-center md:justify-start justify-center">
                            <Image loading='lazy' width={60} height={60} alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/Bao_Hiem_Icon_3_316ccf60ed.png" />
                        </div>
                        <h1 className="font-bold text-xl text-[#156634] text-center md:text-left my-2">{locale === "en" ? "Easy and transparent process" : "Trải nghiệm dịch vụ tiện lợi"}</h1>
                        <p className="md:text-base text-sm">{locale === "en" ? "Simplifies the registration, payment, and use of medical services, providing a quick and convenient experience." : "Đơn giản hóa quy trình đăng ký, thanh toán và sử dụng dịch vụ y tế, mang đến trải nghiệm nhanh chóng và thuận tiện."}</p>
                    </div>
                </div>
                <h2 className="text-center mt-12 font-bold md:text-2xl text-xl text-[#156634]">{locale === "vi" ? "Quy Trình Bảo Lãnh Viện Phí" : 'Outpatient Guarantee Of Payment Procedure'}</h2>
                <h2 className="text-center mt-4 md:mt-0 font-bold text-lg">{locale === "vi" ? "Nhanh Chóng Và Minh Bạch" : 'Fast And Transparent'}</h2>
                <div className="flex justify-center items-center">
                    <button onClick={handleShowModal} className="text-blue-600 hover:underline md:my-4">
                        {locale === 'en' ? "Quick view in images" : "Xem nhanh hình ảnh"}
                    </button>
                </div>
                <section className='mt-12'>
                    <div className="flex items-center justify-between flex-col md:flex-row gap-4">
                        <FlowchartStep
                            number={1}
                            icon="https://d3e4m6b6rxmux9.cloudfront.net/Quy_Trinh_1_c560a0e166.png"
                            text={locale === "en" ? "Customers require a direct outpatient guarantee of payment to the Care Concierge. Customers must present their insurance card and photo identification." : "Khách hàng báo với quầy tiếp nhận về nhu cầu BLVP trực tiếp, xuất trình thẻ bảo hiểm kèm giấy tờ tùy thân có ảnh."}
                        />
                        <div className="hidden md:block self-center">
                            <svg width="71" height="16" viewBox="0 0 71 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M70.7071 8.70711C71.0976 8.31658 71.0976 7.68342 70.7071 7.29289L64.3431 0.928932C63.9526 0.538408 63.3195 0.538408 62.9289 0.928932C62.5384 1.31946 62.5384 1.95262 62.9289 2.34315L68.5858 8L62.9289 13.6569C62.5384 14.0474 62.5384 14.6805 62.9289 15.0711C63.3195 15.4616 63.9526 15.4616 64.3431 15.0711L70.7071 8.70711ZM0 9H2.91667V7H0V9ZM8.75 9H14.5833V7H8.75V9ZM20.4167 9H26.25V7H20.4167V9ZM32.0833 9H37.9167V7H32.0833V9ZM43.75 9H49.5833V7H43.75V9ZM55.4167 9H61.25V7H55.4167V9ZM67.0833 9H70V7H67.0833V9ZM70.7071 8.70711C71.0976 8.31658 71.0976 7.68342 70.7071 7.29289L64.3431 0.928932C63.9526 0.538408 63.3195 0.538408 62.9289 0.928932C62.5384 1.31946 62.5384 1.95262 62.9289 2.34315L68.5858 8L62.9289 13.6569C62.5384 14.0474 62.5384 14.6805 62.9289 15.0711C63.3195 15.4616 63.9526 15.4616 64.3431 15.0711L70.7071 8.70711ZM0 9H2.91667V7H0V9ZM8.75 9H14.5833V7H8.75V9ZM20.4167 9H26.25V7H20.4167V9ZM32.0833 9H37.9167V7H32.0833V9ZM43.75 9H49.5833V7H43.75V9ZM55.4167 9H61.25V7H55.4167V9ZM67.0833 9H70V7H67.0833V9Z" fill="#156634" />
                            </svg>
                        </div>
                        <FlowchartStep
                            number={2}
                            icon="https://d3e4m6b6rxmux9.cloudfront.net/Quy_Trinh_2_ffd5adfbf0.png"
                            text={locale === "en" ? "ECHO MEDI ensures the validity of hospital fees and guides customers through the doctor visit process at the clinic." : "ECHO MEDI kiểm tra tính hợp lệ và giải thích về quy trình bảo lãnh viện phí tới khách hàng. Hướng dẫn khách hàng khám bệnh theo quy trình tại phòng khám."}
                        />
                        <div className="hidden md:block self-center">
                            <svg width="71" height="16" viewBox="0 0 71 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M70.7071 8.70711C71.0976 8.31658 71.0976 7.68342 70.7071 7.29289L64.3431 0.928932C63.9526 0.538408 63.3195 0.538408 62.9289 0.928932C62.5384 1.31946 62.5384 1.95262 62.9289 2.34315L68.5858 8L62.9289 13.6569C62.5384 14.0474 62.5384 14.6805 62.9289 15.0711C63.3195 15.4616 63.9526 15.4616 64.3431 15.0711L70.7071 8.70711ZM0 9H2.91667V7H0V9ZM8.75 9H14.5833V7H8.75V9ZM20.4167 9H26.25V7H20.4167V9ZM32.0833 9H37.9167V7H32.0833V9ZM43.75 9H49.5833V7H43.75V9ZM55.4167 9H61.25V7H55.4167V9ZM67.0833 9H70V7H67.0833V9ZM70.7071 8.70711C71.0976 8.31658 71.0976 7.68342 70.7071 7.29289L64.3431 0.928932C63.9526 0.538408 63.3195 0.538408 62.9289 0.928932C62.5384 1.31946 62.5384 1.95262 62.9289 2.34315L68.5858 8L62.9289 13.6569C62.5384 14.0474 62.5384 14.6805 62.9289 15.0711C63.3195 15.4616 63.9526 15.4616 64.3431 15.0711L70.7071 8.70711ZM0 9H2.91667V7H0V9ZM8.75 9H14.5833V7H8.75V9ZM20.4167 9H26.25V7H20.4167V9ZM32.0833 9H37.9167V7H32.0833V9ZM43.75 9H49.5833V7H43.75V9ZM55.4167 9H61.25V7H55.4167V9ZM67.0833 9H70V7H67.0833V9Z" fill="#156634" />
                            </svg>
                        </div>
                        <FlowchartStep
                            number={3}
                            icon="https://d3e4m6b6rxmux9.cloudfront.net/Quy_Trinh_3_31de2d1718.png"
                            text={locale === "en" ? "ECHO MEDI submits customer documents to the insurance company for evaluation. The insurance company typically provides results within 30-60 minutes." : "ECHO MEDI gửi hồ sơ của khách hàng tới công ty bảo hiểm để thẩm định. Kết quả được trả về sau 30 phút - 60 phút."}
                        />
                    </div>
                    <div className="flex items-center justify-center flex-col md:flex-row gap-12 md:mt-8">
                        <div className="flex flex-col-reverse md:flex-row">
                            <FlowchartStep
                                number={5}
                                icon="https://d3e4m6b6rxmux9.cloudfront.net/Quy_Trinh_5_1b42b1ff4c.png"
                                text={locale === "en" ? "The customer signs the necessary documents, receives medication (if any), and completes the process." : "Khách hàng ký tên vào các giấy tờ liên quan, nhận thuốc (nếu có) và kết thúc quá trình khám bệnh."}
                            />
                            <div className="hidden md:block -rotate-180 self-center mr-8">
                                <svg width="121" height="16" viewBox="0 0 121 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M70.7071 8.70711C71.0976 8.31658 71.0976 7.68342 70.7071 7.29289L64.3431 0.928932C63.9526 0.538408 63.3195 0.538408 62.9289 0.928932C62.5384 1.31946 62.5384 1.95262 62.9289 2.34315L68.5858 8L62.9289 13.6569C62.5384 14.0474 62.5384 14.6805 62.9289 15.0711C63.3195 15.4616 63.9526 15.4616 64.3431 15.0711L70.7071 8.70711ZM0 9H2.91667V7H0V9ZM8.75 9H14.5833V7H8.75V9ZM20.4167 9H26.25V7H20.4167V9ZM32.0833 9H37.9167V7H32.0833V9ZM43.75 9H49.5833V7H43.75V9ZM55.4167 9H61.25V7H55.4167V9ZM67.0833 9H70V7H67.0833V9Z" fill="#156634" />
                                </svg>
                            </div>
                            <FlowchartStep
                                number={4}
                                icon="https://d3e4m6b6rxmux9.cloudfront.net/Quy_Trinh_4_bfde6013ae.png"
                                text={locale === "en" ? "Inform customers about the assessment results from the insurance company. The customer is responsible for any costs not covered by insurance and must pay the clinic accordingly." : "Thông báo với khách hàng kết quả thẩm định từ công ty bảo hiểm. Khách hàng chi trả cho Phòng khám những chi phí không được bảo lãnh."}
                            />
                            <div className="hidden md:block self-center -mt-36">
                                <svg width="77" height="151" viewBox="0 0 47 151" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M73.9849 0.82692C73.8893 0.28297 73.3709 -0.0804973 72.8269 0.0150923C72.283 0.110682 71.9195 0.62913 72.0151 1.17308L73.9849 0.82692ZM0.**********.496C-0.**********.973 0.**********.586 0.**********.864L8.27157 150.397C8.74869 150.675 9.36097 150.514 9.63912 150.036C9.91728 149.559 9.75598 148.947 9.27885 148.669L2.36755 144.64L6.39669 137.728C6.67485 137.251 6.51355 136.639 6.03642 136.361C5.5593 136.083 4.94702 136.244 4.66887 136.721L0.**********.496ZM1.25475 145.967C2.59256 145.615 3.90826 145.241 5.20217 144.848L4.62013 142.934C3.3505 143.32 2.05898 143.687 0.745251 144.033L1.25475 145.967ZM12.8808 142.179C15.4534 141.168 17.9295 140.07 20.3122 138.892L19.4257 137.099C17.0941 138.252 14.6696 139.327 12.1491 140.318L12.8808 142.179ZM27.4574 134.955C29.8124 133.516 32.0628 131.991 34.2126 130.389L33.0173 128.785C30.9165 130.351 28.7169 131.842 26.4145 133.249L27.4574 134.955ZM40.5086 125.225C42.5466 123.389 44.4784 121.477 46.3084 119.497L44.8398 118.14C43.0503 120.076 41.1619 121.945 39.1701 123.739L40.5086 125.225ZM51.5678 113.275C53.2344 111.118 54.7993 108.899 56.2675 106.629L54.5881 105.543C53.1492 107.768 51.6165 109.94 49.9852 112.052L51.5678 113.275ZM60.4133 99.6237C61.7054 97.2333 62.9039 94.7996 64.014 92.3324L62.19 91.5118C61.0991 93.9366 59.922 96.3267 58.6539 98.6726L60.4133 99.6237ZM67.0946 84.8104C68.0399 82.2649 68.9006 79.695 69.6821 77.1104L67.7677 76.5315C66.9976 79.0783 66.15 81.609 65.2197 84.1141L67.0946 84.8104ZM71.8055 69.2755C72.4428 66.6306 73.0039 63.9805 73.4943 61.3356L71.5278 60.971C71.0436 63.5826 70.4898 66.1979 69.8611 68.807L71.8055 69.2755ZM74.7719 53.3308C75.1364 50.6216 75.4315 47.9287 75.6633 45.2632L73.6708 45.0899C73.4416 47.7253 73.1499 50.3871 72.7898 53.0641L74.7719 53.3308ZM76.1823 37.1806C76.2973 34.4281 76.3482 31.719 76.3419 29.0667L74.3419 29.0714C74.3481 31.695 74.2978 34.3746 74.1841 37.0972L76.1823 37.1806ZM76.1439 20.961C76.0139 18.177 75.8233 15.479 75.5816 12.8849L73.5902 13.0705C73.8291 15.6338 74.0176 18.301 74.1461 21.0543L76.1439 20.961ZM74.6268 4.83281C74.4263 3.45397 74.2117 2.11745 73.9849 0.82692L72.0151 1.17308C72.2384 2.44365 72.4499 3.7607 72.6476 5.12061L74.6268 4.83281Z" fill="#156634" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            {showModal && (
                <div className="fixed inset-0 z-[100] flex items-center justify-center bg-slate-50 bg-opacity-50">
                    <div className="transition-all duration-500 sm:max-w-lg sm:w-full m-5 sm:mx-auto">
                        <div className="flex flex-col bg-white rounded-2xl py-4 px-5">
                            <div className="flex justify-between items-center pb-4 border-b border-gray-200">
                                <h4 className="text-sm text-gray-900 font-medium">
                                    {locale === "vi" ? "Quy Trình Bảo Lãnh Viện Phí Tại ECHO MEDI" : 'Outpatient Guarantee of Payment Procedure at ECHO MEDI'}
                                </h4>
                                <button onClick={handleShowModal} className="block cursor-pointer">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.75732 7.75739L16.2426 16.2427M16.2426 7.75739L7.75732 16.2427" stroke="black" strokeWidth="1.6" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div className="overflow-y-auto py-4 min-h-[100px]">
                                <Image
                                    placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(500, 312))}`}
                                    alt="Echo Medi banner"
                                    src={locale === "en" ? "https://d3e4m6b6rxmux9.cloudfront.net/ENG_QT_BLVP_ab4b05eeda.png" : "https://d3e4m6b6rxmux9.cloudfront.net/QUY_TRINH_BAO_LANH_VIEN_PHI_NGOAI_TRU_8553851402.svg"}
                                    width={500}
                                    height={312}
                                    priority
                                />
                            </div>
                            <div className="flex items-center justify-end pt-4 border-t border-gray-200 space-x-4">
                                <button
                                    type="button"
                                    onClick={handleShowModal}
                                    className="py-2.5 px-5 text-xs border border-[#14813d] hover:bg-[#14813d] hover:text-white rounded-full cursor-pointer font-semibold text-center shadow-xs transition-all duration-500 text-[#156634]"
                                >
                                    {locale === "en" ? "Cancel" : "Hủy"}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            <Contact />
            <Toaster position="bottom-center" />
        </>
    )
}
export default InsurancePages;

const FlowchartStep = ({ number, icon, text }: { number: number; icon: string; text: string }) => (
    <>
        <div className='my-4 bg-[#FFFFFF] p-2 rounded-xl'>
            <div className="w-8 h-8 text-white bg-[#156634] rounded-full flex items-center justify-center font-bold">
                {number}
            </div>
            <div className="flex flex-col items-center md:w-72">
                <div className="w-16 h-16 mb-4">
                    <img src={icon} alt={`Step ${number} icon`} className="w-full h-full object-contain" />
                </div>
                <p className="text-center text-sm">{text}</p>
            </div>
        </div>
    </>
)
