import type {
  InferGetStaticPropsType,
} from 'next';
import React from 'react';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import { useState } from 'react';
import Image from 'next/image'
import { addToCart, shimmer, toBase64 } from '../../../lib/ui';
import { getStaticPathsPackagesPediatricHealthcare, getStaticPropsPackage } from '../../../lib/getStatic';
import { CardPackageItem, CardPackageSlideItem } from '../../../components/CardPackage/CardPackageItem';
import ModalBooking from '../../../components/BookingService';
export { getStaticPathsPackagesPediatricHealthcare as getStaticPaths, getStaticPropsPackage as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsPackage>) => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showModal, setShowModal] = useState(false);
  const [message, setMessage] = useState("");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng đặt lịch khám ở phòng khám về gói" + title)
  };
  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta name="description" content={locale == "en" ? (props.en_desc ?? "") : (props.desc ?? "")} />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section>
        <div className="mx-auto noselect">
          <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
            <div className="w-full relative">
              <Image
                src="/banner/banner_new.webp"
                alt="Banner"
                width={1920}
                height={500}
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                className="object-center"
                layout="responsive"
              />
            </div>
            <div className="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 2xl:w-2/3 absolute md:px-16 px-4">
              <h2 className="text-left font-bold md:text-[28px] text-lg text-[#156634] uppercase pb-2 hidden md:block">
                {locale === "vi" ? "Chăm Sóc Sức Khỏe Nhi" : "PEDIATRIC\n Healthcare"}
              </h2>
              <h2 className="text-left font-bold md:text-[28px] text-lg text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                {locale === "vi" ? "Chăm Sóc Sức Khỏe Nhi" : "PEDIATRIC\n Healthcare"}
              </h2>
            </div>
          </div>
        </div>
      </section>

      <section className='mx-auto max-w-screen-2xl'>
        <div className="md:px-16 px-4">
          <div className="mx-auto text-left noselect pb-8 md:mt-8">
            {props.sub_packages?.map((sp: any, id: any) =>
              <>
                <section id='care_package_1' className='hidden md:block'>
                  <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                    {sp.services.map((sv: any) =>
                      <>
                        <section key={sv.id}>
                          <CardPackageItem sv={sv} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </>
                    )}
                  </div>
                </section>
                <section id='care_package_10' className='block md:hidden' >
                  <div className="mx-auto py-6">
                    <section key={sp.id}>
                      <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                    </section>
                  </div>
                </section>
              </>
            )
            }
          </div>
        </div>
        {showModal && (
          <ModalBooking
            visible={showModal}
            onClose={() => setShowModal(false)}
            currentBlog={currentBlog}
            locale={locale}
          />
        )}
      </section >
      <Contact />
    </>
  );
};

export default Blog;
