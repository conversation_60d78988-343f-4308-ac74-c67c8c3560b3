import type { NextPage } from "next";
import { useState, useEffect } from "react"
import axios from "axios"
import ProgressBarSteps from "../../components/ProgressBarSteps"
import ErrorModal from "../../components/ErrorModal"
import { useSearchParams } from "next/navigation"
import { makeStaticProps } from '../../lib/getStatic';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [
    {
      params: {
        locale: 'en',
        slug: "test",
        label: "test2",
      }
    },
    {
      params: {
        locale: 'vi',
        slug: "test",
        label: "test2",
      }
    }],
})

export { getStaticPaths, getStaticProps }

interface Gift {
  id: number
  name: string
  description: string
  maxCount: number
  currentCount: number
}
const GiftForm: NextPage = () => {
  const searchParams = useSearchParams()
  const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [fetchingGifts, setFetchingGifts] = useState(true)
  const [gifts, setGifts] = useState<Gift[]>([])
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [phoneError, setPhoneError] = useState("")
  const [formData, setFormData] = useState({
    phoneNumber: "",
    gamePlayed: "",
    score: "30",
    selectedGift: 1,
    giftSource: "",
  })
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false)
  const [errorModalMessage, setErrorModalMessage] = useState("")
  useEffect(() => {
    const phoneNumberParam = searchParams.get('phoneNumber')
    if (phoneNumberParam) {
      setFormData(prev => ({
        ...prev,
        phoneNumber: phoneNumberParam
      }))
    }
  }, [searchParams])
  useEffect(() => {
    const fetchGifts = async () => {
      setFetchingGifts(true)
      try {
        const response = await axios.get("https://api.echomedi.com/api/gifts")
        if (response.data.success && response.data.data) {
          setGifts(response.data.data)
          if (response.data.data.length > 0) {
            setFormData((prev) => ({
              ...prev,
              selectedGift: response.data.data[0].id,
              giftSource: response.data.data[0].description,
            }))
          }
        } else {
          setError("Không thể tải danh sách quà tặng")
        }
      } catch (error) {
        console.error("Error fetching gifts:", error)
        setError("Đã xảy ra lỗi khi tải danh sách quà tặng")
      } finally {
        setFetchingGifts(false)
      }
    }

    fetchGifts()
  }, [])
  const validatePhoneNumber = (phone: string) => {
    if (!phone) {
      setPhoneError("Vui lòng nhập số điện thoại")
      return false
    }

    if (!phoneRegex.test(phone)) {
      setPhoneError("Vui lòng nhập đúng định dạng số điện thoại")
      return false
    }

    setPhoneError("")
    return true
  }

  const handleGameSelect = (game: string) => {
    const defaultScore = game === "Bắn bóng đổi quà" ? "30" : "80"
    setFormData({ ...formData, gamePlayed: game, score: defaultScore })
    setStep(2)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target

    if (name === "phoneNumber") {
      setFormData({
        ...formData,
        [name]: value,
      })
      validatePhoneNumber(value)
    }
    else if (name === "selectedGift") {
      const selectedGiftId = Number.parseInt(value)
      const selectedGift = gifts.find((g) => g.id === selectedGiftId)
      setFormData({
        ...formData,
        selectedGift: selectedGiftId,
        giftSource: selectedGift ? selectedGift.description : "",
      })
    } else {
      setFormData({
        ...formData,
        [name]: value,
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const isPhoneValid = validatePhoneNumber(formData.phoneNumber)
    if (!isPhoneValid) {
      return
    }

    setLoading(true)
    try {
      await axios.post("https://api.echomedi.com/api/events/update-game", formData)
      setSuccess(true)
      setStep(3)
    } catch (error: any) {
      if (error.response && error.response.data.message === "Event not found for this phone number") {
        setErrorModalMessage("Vui lòng kiểm tra lại số điện thoại hoặc đảm bảo bạn đã đăng ký tham gia chương trình.")
        setIsErrorModalOpen(true)
      } else if (
        error.response &&
        error.response.status === 400 &&
        error.response.data.message === "User has already received 2 gifts"
      ) {
        setErrorModalMessage("Bạn đã hết lượt nhận quà cho chương trình này. Đừng lo! Sắp tới sẽ có nhiều ưu đãi khác dành cho bạn!")
        setIsErrorModalOpen(true)
      }
      else if (
        error.response &&
        error.response.status === 400 &&
        error.response.data.message === "This gift has already been selected"
      ) {
        setErrorModalMessage("Bạn đã chọn quà tặng này rồi. Vui lòng chọn quà khác.")
        setIsErrorModalOpen(true)
      }
      else {
        setErrorModalMessage("Đã xảy ra lỗi khi gửi dữ liệu.")
        setIsErrorModalOpen(true)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleBack = () => {
    setStep(step - 1)
  }

  const getFilteredGifts = () => {
    let allowedIds: number[] = []
    if (formData.gamePlayed === "Bé rửa tay") {
      allowedIds = [1, 2, 6, 7, 8]
    } else if (formData.gamePlayed === "Bắn bóng đổi quà" && formData.score === "30") {
      allowedIds = [1, 2, 6, 7, 8]
    } else if (formData.gamePlayed === "Bắn bóng đổi quà" && formData.score === "80") {
      allowedIds = [6, 7, 8, 9, 3, 4, 5]
    } else {
      allowedIds = gifts.map((g) => g.id)
    }
    return gifts.filter((g) => allowedIds.includes(g.id) && g.currentCount < g.maxCount)
  }

  useEffect(() => {
    const filtered = getFilteredGifts()
    if (filtered.length > 0) {
      const isSelectedValid = filtered.some((g) => g.id === formData.selectedGift)
      if (!isSelectedValid) {
        setFormData((prev) => ({
          ...prev,
          selectedGift: filtered[0].id,
          giftSource: filtered[0].description,
        }))
      }
    }
  }, [formData.gamePlayed, formData.score, gifts])

  return (
    <main className="min-h-screen bg-white pb-12 px-4">
      <div className="max-w-lg mx-auto p-6">
        <section className={`text-center my-6 ${step === 1 ? "pt-20" : ""}`}>
          <h1 className="text-2xl font-bold uppercase">Đăng ký nhận quà</h1>
        </section>
        <ProgressBarSteps totalSteps={3} currentStep={step} />
        {step === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-bold text-center">Bước 1: Chọn trò chơi</h2>
            <div className="grid grid-cols-1 gap-6">
              <section className="flex flex-col flex-wrap">
                <div className="flex flex-row flex-wrap justify-center">
                  <div onClick={() => handleGameSelect("Bé rửa tay")} className="flex justify-center text-center m-2 h-12 w-64">
                    <div className="flex-shrink-0 rounded-full bg-white border-2 border-[#14813D] z-10 pr-1 flex items-center justify-center w-12 h-12">
                      <img src="https://api.echomedi.com/uploads/wash_hand_1_f219422318.svg" alt="icon" />
                    </div>
                    <div className="flex flex-col text-left bg-[#156634] hover:bg-[#14813d] text-white text-xs self-center pl-16 pr-4 py-2 -ml-12 rounded-full w-56">
                      <h3 className="text-lg">Bé rửa tay</h3>
                    </div>
                  </div>
                </div>
              </section>
              <section className="flex flex-col flex-wrap">
                <div className="flex flex-row flex-wrap justify-center">
                  <div onClick={() => handleGameSelect("Bắn bóng đổi quà")} className="flex justify-center text-center m-2 h-12 w-64">
                    <div className="flex-shrink-0 rounded-full bg-white border-2 border-[#14813D] z-10 flex items-center justify-center w-12 h-12">
                      <img src="https://api.echomedi.com/uploads/Ball_1_6cbd545058.svg" alt="icon" />
                    </div>
                    <div className="flex flex-col text-left bg-[#156634] hover:bg-[#14813d] text-white text-xs self-center pl-16 pr-4 py-2 -ml-12 rounded-full w-56">
                      <h3 className="text-lg">Bắn bóng đổi quà</h3>
                    </div>
                  </div>
                </div>
              </section>
            </div>
            <div className="flex justify-center items-center pt-4">
              <img src="https://api.echomedi.com/uploads/Gift_4155a1b093.svg" alt="gift" />
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-2">
            <h2 className="text-xl font-bold text-center">Bước 2: Nhập thông tin</h2>
            <p className="text-center">
              Trò chơi đã chọn: <span className="font-semibold">{formData.gamePlayed}</span>
            </p>
            <form onSubmit={handleSubmit} className="space-y-4 pt-4">
              <div>
                <label className="block text-sm font-medium mb-1">Số điện thoại</label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  placeholder="Nhập số điện thoại của bạn"
                  className={`w-full h-10 border px-3 py-2 rounded-full outline-none ${phoneError ? "border-red-500" : "border-gray-100"
                    }`}
                />
                {phoneError && (
                  <p className="mt-1 text-sm text-red-500">
                    {phoneError}
                  </p>
                )}
              </div>

              {formData.gamePlayed === "Bắn bóng đổi quà" && (
                <div>
                  <label className="block text-sm font-medium mb-2">Điểm số</label>
                  <div className="space-y- flex items-center justify-between">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="score"
                        value="30"
                        checked={formData.score === "30"}
                        onChange={handleChange}
                        className="h-4 w-4"
                      />
                      <span className="">Dưới 50 điểm</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="score"
                        value="80"
                        checked={formData.score === "80"}
                        onChange={handleChange}
                        className="h-4 w-4"
                      />
                      <span className="">Từ 50 đến 100 điểm</span>
                    </label>
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-1">Chọn quà</label>
                {fetchingGifts ? (
                  <div className="flex items-center space-x-2 text-sm py-2">
                    <span className="inline-block h-4 w-4 border-2 border-gray-100 border-t-transparent rounded-full animate-spin"></span>
                    <span>Đang tải danh sách quà tặng...</span>
                  </div>
                ) : error ? (
                  <div className="text-red-500 text-sm py-2">{error}</div>
                ) : (
                  <select
                    name="selectedGift"
                    value={formData.selectedGift}
                    onChange={handleChange}
                    className="w-full h-10 border border-gray-100 bg-white py-2 rounded-full outline-none"
                  >
                    {getFilteredGifts().map((gift) => (
                      <option key={gift.id} value={gift.id}>
                        {gift.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4">
                <button
                  type="button"
                  onClick={handleBack}
                  className="border-2 border-[#156634] bg-white hover:bg-[#156634] text-[#156634] hover:text-white py-2.5 rounded-full transition-colors"
                >
                  Quay lại
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="bg-[#156634] hover:bg-[#14813d] text-white py-2.5 rounded-full transition-colors"
                >
                  {loading ? (
                    <span className="inline-block h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  ) : null}
                  {loading ? "Đang gửi..." : "Gửi thông tin"}
                </button>
              </div>
            </form>
          </div>
        )}

        {step === 3 && success && (
          <div className="fixed inset-0 z-50 flex items-center justify-center px-4 bg-black/50">
            <div className="bg-white rounded-xl shadow-lg max-w-md w-full px-6 py-12 relative animate-in fade-in zoom-in duration-500">
              <div className="flex flex-col items-center text-center">
                <div className="!w-32 !h-32 mx-auto mb-4 rounded-full overflow-hidden bg-gray-100">
                  <img
                    src="https://api.echomedi.com/uploads/success_66e7882952.png"
                    className="!w-32 !h-32 object-cover"
                  />
                </div>
                <div>
                  <h2 className="text-xl font-bold">Nhận quà thành công!</h2>
                  <p className="">Cảm ơn bạn đã tham gia chương trình.</p>
                  {gifts.find((g) => g.id === formData.selectedGift) && (
                    <div className="mt-6">
                      <p className="text-lg font-bold">{gifts.find((g) => g.id === formData.selectedGift)?.name}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

        )}
        <ErrorModal message={errorModalMessage} isOpen={isErrorModalOpen} onClose={() => setIsErrorModalOpen(false)} />
      </div>
    </main>
  )
}

export default GiftForm
