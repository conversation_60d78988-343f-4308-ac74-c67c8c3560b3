import type {
  InferGetStaticPropsType,
} from 'next';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import React from "react";

import { getStaticPathsBlogs, getStaticPropsBlog } from '../../../lib/getStatic';
import SmallHero from '../../../components/Hero/SmallHero';
import parse from 'html-react-parser';

export { getStaticPathsBlogs as getStaticPaths, getStaticPropsBlog as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsBlog>) => {
  return (
    <>
      <Head>
        <meta
          name="description"
          content="ECHO MEDI"
        />
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <SmallHero heading={""} message={""} sub_message={[]} image_url={"https://api.echomedi.com" + props.image_url} />
      <div className="p-4 mx-auto max-w-[864px] mb-10">
        <h1 className='mb-8 text-center text-3xl'>{props.label}</h1>
        <div className='markdown-container'>
          {parse(props.article ?? "")}
        </div>
      </div>

      <Contact />
    </>
  );
};

export default Blog;
