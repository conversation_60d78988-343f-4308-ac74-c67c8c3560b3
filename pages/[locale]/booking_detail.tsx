import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import {  formatStrapiObj } from "../../utils/strapi"
import { formatDate } from "../../utils/dateTime"
import { getBranchAddress } from "../../utils/string";
import dayjs from "dayjs";
import React from 'react';

require("dayjs/locale/vi");
dayjs.locale("vi");

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }


const BookingDetails = () => {
  const router = useRouter()
  const { code } = router.query;
  const locale = router.query.locale as string || 'vi';
  const [booking, setBooking] = useState({
    contactFullName: "",
    contactPhoneNumber: "",
    contactAddress: "",
    contactEmail: "",
    bookingDate: "",
    branch: "",
    status: "",
    patient: {
      phone: "",
      full_name: "",
      address: "",
      email: "",
    }
  });

  useEffect(() => {
    if (code) {
      axios.get('https://api.echomedi.com' + '/api/bookings/' + code + "?populate=*")
        .then(function (response) {
          const booking = formatStrapiObj(response.data.data);
          booking.patient = formatStrapiObj(booking.patient.data);
          setBooking(booking);
        })
        .catch(function (error) {
        });
    }
  }, [code]);

  const parseAddress = (address:any) => {
    let result = "";
    result = address?.address + ", " + address?.ward?.name + ", " + address?.district?.name + ", " + address?.province?.name;
    return result;
  }

  return <>
    <Head>
      <title>ECHO MEDI</title>
      <meta
        name="ECHO MEDI"
        content="ECHO MEDI"
      />
      <meta name="keywords" content="ECHO MEDI"></meta>
      <link rel="icon" href="/favicon1.png" />
    </Head>

    <div className="container mx-auto mt-10">
      {booking.status == "scheduled" &&
        <div>
          <div className='p-3'>
            <h1 className='text-center font-bold uppercase lg:text-xl text-sm'>{locale === "en" ? "Thank you for scheduling your appointment with us" : "CÁM ƠN BẠN ĐÃ ĐẶT HẸN KHÁM SỨC KHỎE TẠI ECHO MEDI"}</h1>
            <svg className="mx-auto mt-2 lg:w-[120px] lg:h-[120px] w-[80px] h-[80px]" fill="#000000" viewBox="0 0 14 14" role="img" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"><path fill="green" d="M13 4.1974q0 .3097-.21677.5265L7.17806 10.329l-1.0529 1.0529q-.21677.2168-.52645.2168-.30968 0-.52645-.2168L4.01935 10.329 1.21677 7.5264Q1 7.3097 1 7t.21677-.5265l1.05291-1.0529q.21677-.2167.52645-.2167.30968 0 .52645.2167l2.27613 2.2839 5.07871-5.0864q.21677-.2168.52645-.2168.30968 0 .52645.2168l1.05291 1.0529Q13 3.8877 13 4.1974z" /></svg>
            <p className="text-center lg:text-sm text-xs mt-2">{locale === "en" ? "We have received your information. Our team member will reach out to you shortly. Please stay tuned!" : "Chúng tôi đã nhận được thông tin của bạn, và sẽ liên hệ bạn sớm nhất có thể. Bạn vui lòng chờ cuộc gọi từ ECHO MEDI nhé!"}</p>
          </div>
        </div>}
    </div>
    <div className='h-3 bg-slate-100 mt-5 lg:hidden'></div>
    <div className="bg-gray-100">
        <div className="lg:max-w-3xl mx-auto max-w-full lg:p-10">
          <div className="bg-white p-4 shadow-md lg:rounded-md">
        <div className="w-4/4 sm:w-3/4 px-10 pt-3 pb-5">
          <div className="flex justify-between">
            <p className="font-semibold text-xl">{locale === "en" ? "BOOKING DETAILS" : "CHI TIẾT LỊCH HẸN"}</p>
          </div>
          <h3 className='pt-5 font-bold text-green-800'>{locale === "en" ? "Customer" : "Khách hàng"}</h3>
          <div className="mt-3 text-sm grid grid-cols-6">
            <div className='font-bold col-span-2'>{locale === "en" ? "Full name" : "Họ và tên"}:</div>
            <div className="col-span-4">{booking?.contactFullName ?? booking?.patient?.full_name}</div>
          </div>
          <div className="mt-2 text-sm grid grid-cols-6">
            <div className='mr-4 font-bold col-span-2'>{locale === "en" ? "Phone" : "Số điện thoại"}:</div>
            <div className="col-span-4">{booking?.contactPhoneNumber ?? booking?.patient?.phone}</div>
          </div>
          <div className="mt-2 text-sm grid grid-cols-6">
            <div className='mr-4 font-bold col-span-2'>{locale === "en" ? "Address" : "Địa chỉ"}:</div>
            <div className="col-span-4">{booking?.contactAddress ?? parseAddress(booking?.patient?.address)}</div>
          </div>
          <div className="mt-2 text-sm grid grid-cols-6">
            <div className='mr-4 font-bold col-span-2'>Email:</div>
            <div className="col-span-4">{booking?.contactEmail ?? booking?.patient?.email}</div>
          </div>
          <h3 className='pt-5 font-bold text-green-800'>{locale === "en" ? "Booking" : "Lịch hẹn"}</h3>
          <div className="mt-3 text-sm grid grid-cols-6">
            <div className='mr-4 font-bold col-span-2'>{locale === "en" ? "Appointment" : "Thời gian"}</div>
            <div className="col-span-4">{formatDate(booking?.bookingDate, "H:mm, DD MMMM, YYYY")}</div>
          </div>
          <div className="mt-2 text-sm grid grid-cols-6">
            <span className='mr-4 font-bold col-span-2'>{locale === "en" ? "Location" : "Địa điểm"}</span>
            <span className="col-span-4">{getBranchAddress(booking?.branch, locale)}</span>
          </div>
        </div>
      </div>
    </div>
    </div>
    <div className="mt-5 mb-10 flex justify-center">
      <a href='/'>
      <button className="bg-transparent hover:bg-green-700 text-green-800 font-semibold hover:text-white py-2 px-4 border border-green-800 hover:border-transparent rounded">{locale == "en" ? "Back" : "Trở lại"}</button>
      </a>
    </div>
    <Contact />
  </>
}

export default BookingDetails
