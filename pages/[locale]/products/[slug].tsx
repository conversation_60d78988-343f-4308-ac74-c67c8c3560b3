import type { InferGetStaticPropsType } from "next";
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from "next/router";
import React, { useState } from "react";
import {
  getStaticPathsProducts,
  getStaticPropsProduct,
} from "../../../lib/getStatic";
import parse from "html-react-parser";
import { shimmer, toBase64 } from "../../../lib/ui";
import ModalBooking from "../../../components/BookingService";
import LinkComponent from "../../../components/Link";
import { convertString } from "../../../utils/convertString";
import Image from "next/image";
import ModalBookingProduct from "../../../components/BookingServiceProduct";
export {
  getStaticPathsProducts as getStaticPaths,
  getStaticPropsProduct as getStaticProps,
};

const Product = (
  props: InferGetStaticPropsType<typeof getStaticPropsProduct>
) => {
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";
  const [quantity, setQuantity] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [message, setMessage] = useState("");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng cần tư vấn gói nhà thuốc" + title)
  };
  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta
          name="description"
          content={locale === "en" ? props.en_desc : props.desc}
        />
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section>
        <div className="w-full mx-auto md:px-16 px-4 pt-2 pb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="inline-flex items-center -space-x-1 md:space-x-1">
              <li className="inline-flex items-center">
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Home" : "Trang chủ"}
                  </span>
                </LinkComponent>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                  </svg>
                  <LinkComponent locale={locale} skipLocaleHandling={false} href={"/store"}>
                    <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                      {locale === "en" ? "Pharmacy" : "Nhà Thuốc"}
                    </span>
                  </LinkComponent>
                </div>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                  </svg>
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                    {locale === "vi" ? `${convertString(props.label)}` : `${convertString(props.en_label)}`}
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </div>
      </section>
      <div className="mx-auto hidden md:block">
        <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
          <div className="w-full relative">
            <Image
              src="https://api.echomedi.com/uploads/banner_store_abafbdfa81.png"
              alt="Banner"
              width={1920}
              height={300}
              placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
              className="object-center"
              layout="responsive"
            />
          </div>
          <div className="w-full absolute md:px-16 px-4">
            <div className="noselect px-4 mx-auto mt-10 flex flex-col sm:flex-row">
              <div className="w-1/3 flex items-center justify-center">
                <Image
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(320, 320))}`}
                  alt={
                    "https://api.echomedi.com" + props.image_placeholder_url
                  }
                  width={320}
                  height={320}
                  loading='lazy' className="rounded-full object-cover w-80 h-80 pb-1 bg-[#166534]" src={"https://api.echomedi.com" + props.image_url} />
              </div>
              <div className="ml-8 mb-4 justify-center flex flex-col w-2/3">
                <h2 className="text-start md:text-[26px] text-2xl text-[#156634] uppercase font-bold">{locale === "vi" ? `${convertString(props.label)}` : `${convertString(props.en_label)}`}</h2>
                <p className="text-justify mt-2">
                  {parse(locale === "en" ? props.en_desc : props.desc)}
                </p>
                <div className="flex items-center justify-start py-4">
                  <button onClick={() => handleShowModal(props.label, props.en_label)} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-medium text-white">
                    {locale === "en" ? "Booking a consultation" : "Đặt lịch tư vấn"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <section className="block md:hidden px-4">
        <div className="flex items-center justify-center gap-4 mb-8">
          <div className="w-3/5">
            <Image
              placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(288, 288))}`}
              alt={
                "https://api.echomedi.com" + props.image_placeholder_url
              }
              width={288}
              height={288}
              loading='lazy' className="rounded-full object-cover w-48 h-48 pb-2 bg-[#166534]" src={"https://api.echomedi.com" + props.image_url} />
          </div>
          <div className="w-2/5">
            <h2 className="text-start text-xl text-[#156634] uppercase font-bold">{locale === "vi" ? `${convertString(props.label)}` : `${convertString(props.en_label)}`}</h2>
          </div>
        </div>
        <div className="mb-4 justify-center flex flex-col">
          <p className="text-justify mt-2">
            {parse(locale === "en" ? props.en_desc : props.desc)}
          </p>
          <div className="flex items-center justify-center py-4">
            <button onClick={() => handleShowModal(props.label, props.en_label)} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-medium text-white">
            {locale === "en" ? "Booking a consultation" : "Đặt lịch tư vấn"}
            </button>
          </div>
        </div>
      </section>
      <div className="md:px-16 px-4 noselect">
        <div className="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-8 my-8">
          {props.medicines?.map((m: any) => (
            <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] md:mt-4 bg-white">
              <h1 className="font-bold text-center md:text-left text-xl text-[#156634] my-2">{locale === "en" ? m.en_label : m.label}</h1>
              <div className="flex items-center justify-center flex-col sm:flex-row gap-4">
                <Image loading='lazy' width={300} height={300} alt="Iamge" src={"https://api.echomedi.com" + m.image?.url} className="md:w-1/2" />
                <p className="md:text-base text-sm text-justify">{parse(locale == "en" ? m.en_desc ?? "" : m.desc ?? "")}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
     
      {showModal && (
        <ModalBookingProduct
          visible={showModal}
          onClose={() => setShowModal(false)}
          currentBlog={currentBlog}
          locale={locale}
        />
      )}
      <Contact />
    </>
  );
};

function numberWithCommas(x: number) {
  return x?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Product;
