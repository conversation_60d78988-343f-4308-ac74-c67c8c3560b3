import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import { formatDate } from "../../utils/dateTime"
import dayjs from "dayjs";
import React from 'react';

require("dayjs/locale/vi");
dayjs.locale("vi");

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

const Order = () => {
  const router = useRouter()
  const [data, setData] = useState([]);
  const locale = router.query.locale as string || 'vi';
  const token = localStorage.getItem('token');

  useEffect(() => {
    if (token) {
      axios.post('https://api.echomedi.com' + '/api/medical-record/getRelatedMedicalRecords',
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        .then(function (response) {
          setData(response.data.medicalRecords)
          toast.success('Thành công');
        })
        .catch(function (error) {
          if (error.response.status == 401) {
            toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
            localStorage.removeItem("token");
            window.location.href = '/login';
          }
        });
    }
  }, [token]);

  const downloadShortenMedicalRecordV2 = (id: String) => {
    const toastId = toast.loading("Đang tải")
    axios
      .post(
        "https://api.echomedi.com/api/product/downloadShortenMedicalRecordV2",
        {
          id,
        },
        {
          responseType: "arraybuffer",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/pdf",
          },
        }
      )
      .then((response) => {
        const b = new Blob([response.data], { type: "application/pdf" })
        var url = window.URL.createObjectURL(b)
        window.open(url)
        setTimeout(() => window.URL.revokeObjectURL(url), 100)
      })
      .finally(() => {
        toast.dismiss(toastId)
      })
  }

  return <>
    <Head>
      <title>ECHO MEDI</title>
      <meta
        name="ECHO MEDI"
        content="ECHO MEDI"
      />
      <meta name="keywords" content="ECHO MEDI"></meta>
      <link rel="icon" href="/favicon1.png" />
    </Head>
    <div className="bg-gray-100">
      <div className="lg:max-w-3xl mx-auto max-w-full lg:p-10">
        <div className="bg-white p-4 shadow-md lg:rounded-md">
          <div className="w-4/4 px-4 py-4">
            <div className="flex justify-between border-b pb-8">
              <p className="font-semibold text-xl">{locale === "en" ? "Medical records" : "Bệnh án"}</p>
            </div>
            {data?.map((line: any) =>
              <div className="flex items-center hover:bg-gray-100 -mx-8 px-6 py-5">
                <div className="flex w-1/1">
                  <div className="flex flex-col justify-between flex-grow">
                    <button onClick={e => downloadShortenMedicalRecordV2(line.id)}>
                      <svg className='inline mr-2' xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="#000000" height="20px" width="20px" version="1.1" id="Capa_1" viewBox="0 0 489.9 489.9" xmlSpace="preserve">
                        <g>
                          <g>
                            <path d="M328.2,0H49.4v489.9h391.1V112.3L328.2,0z M122.7,120.5H154V89.6c0-13.9,11.2-25.1,25.1-25.1l0,0    c13.9,0,25.1,11.2,25.1,25.1v30.9h31.3c13.9,0,25.1,11.2,25.1,25.1l0,0c0,13.9-11.2,25.1-25.1,25.1h-31.3v31.2    c0,13.9-11.2,25.1-25.1,25.1l0,0c-13.9,0-25.1-11.2-25.1-25.1v-31.3h-31.3c-13.9,0-25.1-11.2-25.1-25.1l0,0    C97.6,131.7,108.9,120.5,122.7,120.5z M381.9,418.3H108.1c-5.4,0-10.1-4.3-10.1-10.1c0-5.4,4.3-10.1,10.1-10.1h273.3    c5.4,0,10.1,4.3,10.1,10.1C391.6,414,387.3,418.3,381.9,418.3z M381.9,332.8H108.1c-5.4,0-10.1-4.3-10.1-10.1    c0-5.4,4.3-10.1,10.1-10.1h273.3c5.4,0,10.1,4.3,10.1,10.1C391.6,328.1,387.3,332.8,381.9,332.8z M308,132.1V19.8l112.8,112.4H308    V132.1z" />
                          </g>
                        </g>
                      </svg>
                      <span className="font-bold text-sm">ID: {line.uid} - {locale == "vi" ? "Thời gian" : "Ngày"} {formatDate(line.createdAt)}</span>
                    </button>
                  </div>
                </div>
              </div>)}
          </div>
        </div>
      </div>
    </div>
    <Contact />
  </>
}

export default Order
