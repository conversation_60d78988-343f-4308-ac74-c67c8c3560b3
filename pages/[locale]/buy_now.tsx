import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import React, { useEffect, useState } from "react";
import axios from "axios";
import toast from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import LinkComponent from "../../components/Link";
import useUserData from "../../hooks/useUserData";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi'
  const [token, setToken] = useState<string | null>(null)
  const [lines, setCartLines] = useState<any[]>([])
  const [totalPrice, setTotalPrice] = useState(0)
  const [price, setPrice] = useState(0)
  const [originalTotal, setOriginalTotalPrice] = useState(0)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('momo')
  const { userData, setUserData } = useUserData(token);
  useEffect(() => {
    const storedToken = localStorage.getItem('token')
    if (storedToken) {
      setToken(storedToken)
      fetchCartData(storedToken)
    }
  }, [])

  const fetchCartData = async (token: string) => {
    const toastId = toast.loading('Loading...')
    try {
      const response = await axios.get('https://api.echomedi.com/api/product/getCart', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      processCartData(response.data.user.cart_lines ?? [])
    } catch (error: any) {
      handleApiError(error)
    } finally {
      toast.dismiss(toastId)
    }
  }

  const processCartData = (lines: any[]) => {
    let total = 0
    let discount = 0
    let originalTotal = 0

    lines.forEach((line: any) => {
      const currentPrice = line.product ? line.product.price : parseInt(line.service.price)
      const originalPrice = line.product?.original_price || parseInt(line.service?.original_price) || currentPrice

      total += currentPrice * line.quantity
      if (originalPrice > currentPrice) {
        discount += (originalPrice - currentPrice) * line.quantity
        originalTotal += originalPrice * line.quantity
      } else {
        originalTotal += currentPrice * line.quantity
      }
    })

    setTotalPrice(total)
    setPrice(discount)
    setOriginalTotalPrice(originalTotal)
    setCartLines(lines)
  }
  const handleQuantityChange = (index: number, id: string, change: number) => {
    const newQuantity = lines[index].quantity + change
    if (newQuantity > 0) {
      updateCart(index, id, newQuantity)
    }
  }
  const updateCart = async (index: number, id: string, quantity: number) => {
    const token = localStorage.getItem('token');
    if (!token) return
    try {
      await axios.post('https://api.echomedi.com/api/cart/updateCartLine',
        { id, cnt: quantity },
        { headers: { 'Authorization': `Bearer ${token}` } }
      )
      const updatedLines = lines.map((line, i) => (i === index ? { ...line, quantity } : line))
      setCartLines(updatedLines)
      processCartData(updatedLines)
      toast.success("Đã cập nhật số lượng thành công")
    } catch (error: any) {
      handleApiError(error)
    }
  }

  const handlePaymentMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedPaymentMethod(event.target.value)
  }

  const getPaymentURL = async () => {
    if (!token) {
      toast.error("Vui lòng đăng nhập để tiếp tục")
      router.push('/login')
      return
    }

    const toastId = toast.loading('Đang chuyển đến trang thanh toán')
    let url = ''
    let requestData = {}

    switch (selectedPaymentMethod) {
      case 'vnpay':
        url = 'https://api.echomedi.com/api/orders/createPaymentUrl'
        break
      case 'momo':
        url = 'https://api.echomedi.com/api/orders/createMomoPaymentUrl'
        requestData = { requestType: 'captureWallet' }
        break
      case 'visa':
        url = 'https://api.echomedi.com/api/orders/createMomoPaymentUrl'
        requestData = { requestType: 'payWithCC' }
        break
      case 'cash':
        toast.dismiss(toastId)
        return
      default:
        toast.error("Vui lòng chọn phương thức thanh toán")
        toast.dismiss(toastId)
        return
    }

    try {
      const response = await axios.post(url, requestData, {
        headers: { 'Authorization': `Bearer ${token}` },
      })
      const paymentUrl = response.data.url || response.data.payUrl
      if (paymentUrl) {
        window.location.href = paymentUrl
      }
    } catch (error: any) {
      handleApiError(error)
    } finally {
      toast.dismiss(toastId)
    }
  }

  const handleApiError = (error: any) => {
    if (error.response?.status === 401) {
      toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại")
      localStorage.removeItem("token")
      setToken(null)
      router.push('/login')
    } 
  }
  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Giỏ hàng" : "Cart"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className="py-4 px-4 md:px-16">
        <div className="flex items-start md:items-center justify-start md:justify-between flex-col md:flex-row">
          <div className="flex items-center h-10 rounded gap-2">
            <svg
              className="h-3 w-3 font-bold cursor-pointer rotate-90"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              fill="#4B4B4B"
            >
              <path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z" />
            </svg>
            <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
              <p className="text-base text-[#4B4B4B]">{locale === "en" ? "Back" : "Quay lại"}</p>
            </LinkComponent>
          </div>
          <div>
            <h2 className="hidden md:block text-center font-bold md:text-[28px] text-2xl text-[#156634] uppercase">
              {locale === "en" ? "Order Information" : "Thông tin đơn hàng"}
            </h2>
          </div>
          <div></div>
        </div>
      </div>
      <h2 className="block md:hidden text-center font-bold md:text-[28px] text-2xl text-[#156634] uppercase">
        {locale === "en" ? "Order Information" : "Thông tin đơn hàng"}
      </h2>
      {lines.length === 0 ? (
        <div className="container mx-auto">
          <div className="p-10">
            <img src="https://d3e4m6b6rxmux9.cloudfront.net/preview_b0649685a8.png?updated_at=2023-07-04T09:54:15.960Z" className='rounded-lg w-[250px] mx-auto' />
            <h1 className="text-xl text-gray-600 text-center font-bold">
              {locale === "en" ? "No items in the shopping cart" : "Không có sản phẩm trong giỏ hàng"}
            </h1>
            <div className="mt-8 flex justify-center">
              <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                <button className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-md hover:bg-green-700 font-bold">{locale == "en" ? "Continue shopping" : "Tiếp tục mua hàng"}</button>
              </LinkComponent>
            </div>
          </div>
        </div>
      ) : (
        <div className="px-4 md:px-16 pb-12">
          <div className="mt-4 flex flex-col xl:flex-row jusitfy-center place-items-stretch w-full xl:space-x-4 space-y-4 md:space-y-4 xl:space-y-0">
            <div className="flex flex-col justify-start items-start w-full">
              <div className="w-full">
                <div className="overflow-x-auto rounded-xl  bg-white p-5">
                  <p className="text-xl text-[#156634] font-bold mb-2">{locale === "en" ? "Products" : "Sản phẩm"}</p>
                  <div className="hidden md:flex pt-4 font-bold text-base border-t-2 text-[#8E8E8E]">
                    <div className="w-[30%] text-base">{locale === "en" ? "Product" : "Sản phẩm"}</div>
                    <div className="w-[20%] text-center text-base">{locale === "en" ? "Price" : "Giá tiền"}</div>
                    <div className="w-[30%] text-center text-base">{locale === "en" ? "Quantity" : "Số lượng"}</div>
                    <div className="w-[20%] text-center text-base">{locale === "en" ? "Total" : "Thành tiền"}</div>
                  </div>
                  {lines.map((line: any, index: number) =>
                    <>
                      <div className="hidden md:flex py-5 items-center" key={index}>
                        <div className="w-[30%] flex items-center">
                          <p className="text-base font-medium ">
                            {locale === "en" ? (line.service.en_label) : (line.service.label)}</p>
                          {/* {line.service.original_price &&
                            <span className="bg-[#FBE7E9] rounded-md p-1 ml-4 text-xs font-bold text-[#AF0E18]">-50%</span>
                          } */}
                        </div>
                        <div className="w-[20%] text-center text-[#156634] text-base font-medium flex flex-col">
                          <span className="text-[#156634]">
                            {numberWithCommas((line.service.price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                          </span>
                          {line.service.original_price &&
                            <span className="line-through text-[#8E8E8E]">
                              {numberWithCommas((line.service.original_price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                            </span>
                          }
                        </div>
                        <div className="w-[30%] flex justify-center buttons_added items-center">
                          <button onClick={() => handleQuantityChange(index, line.id, -1)}><input className="minus is-form" type="button" value="-" /></button>
                          <input aria-label="quantity" className="input-qty w-10" type="number" value={line.quantity} />
                          <button onClick={() => handleQuantityChange(index, line.id, 1)}><input className="plus is-form" type="button" value="+" /></button>
                        </div>
                        <div className="w-[20%] justify-center flex text-[#156634]">
                          {numberWithCommas((line.service.price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                        </div>
                      </div>
                      <div className="md:hidden py-5" key={index}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <p className="text-base font-medium ">
                              {locale === "en" ? (line.service.en_label) : (line.service.label)}</p>
                            {/* {line.service.original_price &&
                              <span className="bg-[#FBE7E9] rounded-md p-1 ml-4 text-xs font-bold text-[#AF0E18]">-50%</span>
                            } */}
                          </div>
                        </div>
                        <div className="flex items-center justify-between space-y-2">
                          <div className="text-center mt-[6px] text-[#156634] text-base font-medium flex flex-col">
                            <span className="text-[#156634]">
                              {numberWithCommas((line.service.price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                            </span>
                            {line.service.original_price &&
                              <span className="line-through text-[#8E8E8E]">
                                {numberWithCommas((line.service.original_price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                              </span>
                            }
                          </div>
                          <div className="flex justify-center buttons_added items-center">
                            <button onClick={() => handleQuantityChange(index, line.id, -1)}><input className="minus is-form" type="button" value="-" /></button>
                            <input aria-label="quantity" className="input-qty w-10" type="number" value={line.quantity} />
                            <button onClick={() => handleQuantityChange(index, line.id, 1)}><input className="plus is-form" type="button" value="+" /></button>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>

                <div className="my-4 md:my-8">
                  <div className="w-full p-4 md:p-5 mx-auto bg-white rounded-xl">
                    <p className="text-xl text-[#156634] font-bold mb-2">{locale === "en" ? " Recipient" : "Người nhận"}</p>
                    <div className="w-full flex-col justify-start items-start gap-4 inline-flex border-t-2 pt-2">
                      <div className="w-full flex-col justify-start items-start gap-4 flex">
                        <div className="w-full flex-col justify-start items-start gap-4 flex">
                          <div className="w-full justify-start items-start gap-4 flex sm:flex-row flex-col">
                            <div className="w-full flex-col justify-start items-start flex">
                              <label htmlFor="" className="flex gap-1 items-center text-[#1F1F1F] text-base font-medium leading-relaxed">{locale == "en" ? "Phone" : "Số điện thoại"}:
                                <svg xmlns="http://www.w3.org/2000/svg" width="7" height="7" viewBox="0 0 7 7" fill="none">
                                  <path d="M3.11222 6.04545L3.20668 3.94744L1.43679 5.08594L0.894886 4.14134L2.77415 3.18182L0.894886 2.2223L1.43679 1.2777L3.20668 2.41619L3.11222 0.318182H4.19105L4.09659 2.41619L5.86648 1.2777L6.40838 2.2223L4.52912 3.18182L6.40838 4.14134L5.86648 5.08594L4.09659 3.94744L4.19105 6.04545H3.11222Z" fill="#EF4444" />
                                </svg>
                              </label>
                              <input disabled value={userData.phone} type="tel" id="phone" name="phone" onChange={(e) => {
                                setUserData(prev => ({
                                  ...prev,
                                  phone: e.target.value
                                }));
                              }}
                                className="h-10 border border-gray-300 text-gray-600 bg-white text-base rounded-lg block w-full py-2.5 px-4 focus:outline-none"
                              />
                            </div>
                            <div className="w-full flex-col justify-start items-start flex">
                              <label htmlFor="" className="flex gap-1 items-center text-[#1F1F1F] text-base font-medium leading-relaxed">{locale == "en" ? "Full name" : "Họ và tên"}:
                              </label>
                              <input disabled value={userData.patient.full_name} type="text" id="name" name="name" onChange={(e) => {
                                setUserData(prev => ({
                                  ...prev,
                                  patient: {
                                    ...prev.patient,
                                    full_name: e.target.value
                                  }
                                }));
                              }}
                                className="h-10 border border-gray-300 text-gray-600 bg-white text-base rounded-lg block w-full py-2.5 px-4 focus:outline-none"

                              />
                            </div>
                          </div>
                          <div className="w-full flex-col justify-start items-start flex">
                            <label htmlFor="" className="flex gap-1 items-center text-[#1F1F1F] text-base font-medium leading-relaxed">{locale == "en" ? "Notes" : "Ghi chú"}:</label>
                            <input type="tel" id="phone" name="phone" onChange={(e) => {
                              setUserData(prev => ({
                                ...prev,
                                note: e.target.value
                              }));
                            }}
                              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-[#156634] text-xs lg:text-sm h-10"
                              required />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="gap-8 flex flex-col">
              <div className="bg-white rounded-xl w-full xl:w-96 flex justify-between items-center md:items-start px-5 py-6 flex-col">
                <div className="flex flex-col xl:flex-col justify-start items-stretch h-full w-full md:space-x-6 lg:space-x-8 xl:space-x-0">
                  <div className="flex justify-center items-center w-full space-y-4 flex-col">
                    <div className="flex justify-between items-center w-full border-gray-200 border-b pb-3">
                      <p className="text-xl text-[#156634] font-bold">{locale === "en" ? " Payment" : "Thanh toán"}</p>
                    </div>
                    <div className="flex justify-between w-full">
                      <p className="text-base">{locale === "en" ? "Total price" : "Tổng tiền"}</p>
                      <p className="text-base font-semibold">
                        {numberWithCommas(originalTotal)}  {locale === "en" ? " VND" : "VND"}
                      </p>
                    </div>
                    <div className="flex justify-between items-center w-full border-gray-200 border-b pb-4">
                      <p className="text-base">{locale === "en" ? "Discount" : "Giảm giá"}</p>
                      <p className="text-base text-[#E98E39] font-semibold">
                        - {numberWithCommas(price)}  {locale === "en" ? " VND" : "VND"}
                      </p>
                    </div>
                    <div className="flex justify-between items-center w-full">
                      <p className="text-xl font-semibold">{locale === "en" ? "Provisional" : "Tạm tính"}</p>
                      <p className="text-xl font-semibold text-[#156634]">{numberWithCommas(totalPrice)} {locale === "en" ? " VND" : "VND"}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-xl w-full flex justify-between items-center py-6 px-5 flex-col ">
                <div className="flex flex-col justify-start items-center h-full w-full">
                  <div className="flex justify-center items-center w-full space-y-4 flex-col">
                    <div className="flex justify-between items-center w-full border-gray-200 border-b pb-3">
                      <p className="text-xl text-[#156634] font-bold">{locale === "en" ? "Payment method" : "Phương thức thanh toán"}</p>
                    </div>
                    <p className="text-base text-[#1F1F1F]">{locale === "en" ? "Please select one of the following payment methods:" : "Vui lòng lựa chọn 1 trong các phương thức thanh toán sau:"}</p>
                    <div className="flex items-center w-full gap-4">
                      <div className="flex items-center">
                        <input
                          id="vnpay"
                          type="radio"
                          name="payment-method"
                          value="vnpay"
                          checked={selectedPaymentMethod === 'vnpay'}
                          onChange={handlePaymentMethodChange}
                        />
                        <img src="https://d3e4m6b6rxmux9.cloudfront.net/vnpay_10b151a67e.png" className="object-cover border border-[#CACACA] rounded h-12" />
                      </div>
                      <label htmlFor="vnpay" className="text-start cursor-pointer text-[#1F1F1F] text-base">
                        {locale === "en" ? "Pay with VNPAY" : "Thanh toán trực tuyến bằng VNPAY"}
                      </label>
                    </div>
                    <div className="flex items-center w-full gap-4">
                      <div className="flex items-center">
                        <input
                          id="momo"
                          type="radio"
                          name="payment-method"
                          value="momo"
                          checked={selectedPaymentMethod === 'momo'}
                          onChange={handlePaymentMethodChange}
                        />
                        <img src="https://d3e4m6b6rxmux9.cloudfront.net/momo_cd67e6e6d8.png" className="object-cover border border-[#CACACA] rounded h-12" />
                      </div>
                      <label htmlFor="momo" className="text-start cursor-pointer text-[#1F1F1F] text-base">
                        {locale === "en" ? "Pay with MOMO e-wallet" : "Thanh toán bằng ví điện tử MOMO"}
                      </label>
                    </div>
                    <div className="flex items-center w-full gap-4">
                      <div className="flex items-center">
                        <input
                          id="visa"
                          type="radio"
                          name="payment-method"
                          value="visa"
                          checked={selectedPaymentMethod === 'visa'}
                          onChange={handlePaymentMethodChange}
                        />
                        <img src="https://d3e4m6b6rxmux9.cloudfront.net/Visa_f1c8204f93.png" className="object-cover border border-[#CACACA] rounded h-12" />
                      </div>
                      <label htmlFor="visa" className="text-start cursor-pointer text-[#1F1F1F] text-base">
                        {locale === "en" ? "Pay with VISA Master JCB" : "Thanh toán bằng VISA Master JCB"}
                      </label>
                    </div>
                    {/* <div className="flex items-center w-full gap-4">
                      <div className="flex items-center">
                        <input
                          id="cash"
                          type="radio"
                          name="payment-method"
                          value="cash"
                          onChange={handlePaymentMethodChange}
                        />
                        <img src="https://d3e4m6b6rxmux9.cloudfront.net/Cash_2cccf1e659.png" className="object-cover border border-[#CACACA] rounded h-12" />
                      </div>
                      <label htmlFor="cash" className="text-start cursor-pointer text-[#1F1F1F] text-base">
                        {locale === "en" ? "Pay cash on delivery" : "Thanh toán tiền mặt khi nhận hàng"}
                      </label>
                    </div> */}
                  </div>
                </div>
              </div>
              <button
                onClick={getPaymentURL}
                disabled={!selectedPaymentMethod}
                className={`py-2 text-center w-full inline text-base font-medium rounded-full px-5 ${!selectedPaymentMethod ? "bg-[#156634] cursor-not-allowed opacity-50 text-white" : "bg-[#156634] hover:bg-[#14813d] text-white"}`}
              >
                {locale === "vi" ? "Thanh Toán" : "Payment"}
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="lg:block hidden"><Contact /></div>
    </>
  );
};

export default Home;
