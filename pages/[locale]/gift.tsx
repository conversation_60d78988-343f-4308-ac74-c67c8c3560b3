import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import axios from 'axios';
import React, { useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import SmallHero from "../../components/Hero/SmallHero";
import { makeStaticProps } from '../../lib/getStatic';
import Modal from "../../components/components/Modal";
import AccordionGift from "../../components/AccordionGift";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }


const Home: NextPage = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const [email, setEmail] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [o1, setO1] = useState(-1);
    const [o2, setO2] = useState(-1);
    const [option, setOption] = useState("1");
    const [active, setActive] = useState(-1);

    const sendEmailSubscription = () => {
        axios.post('https://api.echomedi.com' + '/api/packages/subscribeInfo', {
            "email": email,
        })
            .then(function (response) {
                toast.success('Thành công');
            })
            .catch(function (error) {
                if (error.response.status == 401) {
                    toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
                    localStorage.removeItem("token");
                    window.location.href = '/login';
                }
            });
    }

    const getTitle = () => {
        switch (option) {
            case "1":
                return locale === "en" ? "For Grandpa/Father" : "Dành tặng Ông/Bố";
                break;
            case "2":
                return locale === "en" ? "For Grandma/Mom" : "Dành tặng Bà/Mẹ";
                break;
            case "3":
                return locale === "en" ? "For Siblings/Relatives/Friends" : "Dành tặng Anh chị em/Họ hàng/Bạn bè"
                break;
            case "4":
                return locale === "en" ? "For Corporate/ Employees" : "Dành tặng Doanh nghiệp/ Nhân viên"
                break;
            case "5":
                return locale === "en" ? "For Members" : "Dành tặng thành viên"
                break;
        }
    }

    return (
        <>
            <Head>
                <title>ECHO MEDI - Quà tặng người thân</title>
                <meta
                    name="ECHO MEDI"
                    content="ECHO MEDI"
                />
                <meta name="keywords" content="ECHO MEDI"></meta>
                <link rel="icon" href="/favicon1.png" />
            </Head>
            <SmallHero heading={locale === "en" ? "Gifting" : "QUÀ TẶNG"} message={""} sub_message={[]} image_url={"https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_358_a9729888a4.png"} />
            <div className="mx-auto max-w-screen-2xl py-4">
                <div className="">
                    <div className="w-full h-full col-span-2 md:col-span-1 row-span-2">
                        <p className='text-center text-green-800 text-xl sm:text-3xl font-bold mb-4'>{locale === "en" ? "Give Love - Wellness Delivered" : "Trao Yêu Thương - Tặng Sức Khỏe"}</p>
                        <p className='text-center text-xl font-medium mb-4'>{locale === "en" ? "Members receive on-demand access to a full spectrum of concierge medical services" : "Thành viên được tiếp cận đầy đủ các dịch vụ trợ giúp y tế đặc biệt, phù hợp với yêu cầu từng cá nhân"}</p>
                    </div>
                    <div className="grid grid-rows-none  grid-cols-1 sm:grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mx-4 my-6">
                        <div className="border border-1 shadow-md rounded-3xl mt-1 relative h-[280px] md:h-[350px] overflow-hidden">
                            <img className="max-h-[60%] object-cover w-full" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_270_589964988b.png" />
                            <p className="sm:text-lg font-bold mt-4 text-base px-2">{locale === "en" ? "For Grandpa/Father" : "Dành tặng Ông/Bố"}</p>
                            <button
                                onClick={e => { setShowModal(true); setOption("1"); }}
                                className="absolute bottom-0 left-0 right-0 m-auto sm:mt-0 mb-4 hover:underlinetext-center hover:bg-emgreen text-emgreen hover:text-white py-1 px-4 hover:border-transparent hover:text-white rounded-full bg-emgreen text-center text-white mt-2 w-[150px] font-bold mt-4">
                                {locale === "en" ? "Learn More" : "Tìm hiểu thêm"}
                            </button>
                        </div>
                        <div className="border border-1 shadow-md rounded-3xl mt-1 relative h-[280px] md:h-[350px] overflow-hidden">
                            <img className="max-h-[60%] object-cover w-full" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_361_1f3c5401ae.png" />
                            <p className="sm:text-lg font-bold mt-4 text-base px-2">{locale === "en" ? "For Grandma/Mom" : "Dành tặng Bà/Mẹ"}</p>
                            <button
                                onClick={e => { setShowModal(true); setOption("2"); }} className="absolute bottom-0 left-0 right-0 m-auto sm:mt-0 mb-4 hover:underlinetext-center hover:bg-emgreen text-emgreen hover:text-white py-1 px-4 hover:border-transparent hover:text-white rounded-full bg-emgreen text-center text-white mt-2 w-[150px] font-bold mt-4">
                                {locale === "en" ? "Learn More" : "Tìm hiểu thêm"}
                            </button>
                        </div>
                        <div className="border border-1 shadow-md rounded-3xl mt-1 relative h-[280px] md:h-[350px] overflow-hidden">
                            <img className="max-h-[60%] object-cover w-full" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_363_518fba836c.png" />
                            <p className="sm:text-lg font-bold mt-4 text-base px-2">{locale === "en" ? "For Siblings/Relatives/Friends" : "Dành tặng Anh chị em/Họ hàng/Bạn bè"}</p>
                            <button
                                onClick={e => { setShowModal(true); setOption("3"); }} className="absolute bottom-0 left-0 right-0 m-auto sm:mt-0 mb-4 hover:underlinetext-center hover:bg-emgreen text-emgreen hover:text-white py-1 px-4 hover:border-transparent hover:text-white rounded-full bg-emgreen text-center text-white mt-2 w-[150px] font-bold mt-4">
                                {locale === "en" ? "Learn More" : "Tìm hiểu thêm"}
                            </button>
                        </div>
                        <div className="border border-1 shadow-md rounded-3xl mt-1 relative h-[280px] md:h-[350px] overflow-hidden">
                            <img className="max-h-[60%] object-cover w-full" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_369_74de40c2fb.png" />
                            <p className="sm:text-lg font-bold mt-4 text-base px-2">{locale === "en" ? "For Corporate/ Employees" : "Dành tặng Doanh nghiệp/ Nhân viên"}</p>
                            <button
                                onClick={e => { setShowModal(true); setOption("4"); }} className="absolute bottom-0 left-0 right-0 m-auto sm:mt-0 mb-4 hover:underlinetext-center hover:bg-emgreen text-emgreen hover:text-white py-1 px-4 hover:border-transparent hover:text-white rounded-full bg-emgreen text-center text-white mt-2 w-[150px] font-bold mt-4">
                                {locale === "en" ? "Learn More" : "Tìm hiểu thêm"}
                            </button>
                        </div>
                        <div className="border border-1 shadow-md rounded-3xl mt-1 relative h-[280px] md:h-[350px] overflow-hidden">
                            <img className="max-h-[60%] object-cover w-full" src="https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_370_793b55d932.png" />
                            <p className="sm:text-lg font-bold mt-4 text-base px-2">{locale === "en" ? "For Members" : "Dành tặng thành viên"}</p>
                            <button
                                onClick={e => { setShowModal(true); setOption("5"); }} className="absolute bottom-0 left-0 right-0 m-auto sm:mt-0 mb-4 hover:underlinetext-center hover:bg-emgreen text-emgreen hover:text-white py-1 px-4 hover:border-transparent hover:text-white rounded-full bg-emgreen text-center text-white mt-2 w-[150px] font-bold mt-4">
                                {locale === "en" ? "Learn More" : "Tìm hiểu thêm"}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {/* <div className="mx-auto max-w-screen-xl rounded-3xl px-4 pb-10">
                <p className="text-center text-xl font-light">{locale === "en" ? "Subscribe To Our Newsletter:" : "Đăng ký nhận thông tin của chúng tôi:"}</p>
                <div className="mx-auto mt-4 flex max-w-xl flex-col border-gray-600 bg-white sm:flex-row sm:rounded-full sm:border">
                    <input
                        value={email}
                        onChange={e => {
                            setEmail(e.target.value)
                        }}
                        className="m-2 h-12 rounded-full px-4 text-gray-500 sm:w-full border" placeholder={locale === "en" ? "Your Email Address ..." : "Địa chỉ email của bạn"} type="email" name="email" />
                    <button
                        onClick={() => sendEmailSubscription()}
                        className="shrink-0 m-2 rounded-full bg-green-800 px-8 py-3 text-white focus:bg-green-800 focus:outline-none hover:bg-green-900 font-bold">{locale === "en" ? "Subscribe Now" : "Đăng ký ngay"}</button>
                </div>
            </div> */}
            <Modal
                showCloseButton
                fill="white"
                visibleModal={showModal}
                wrapperClassName="!w-[800px] max-w-[96vw] z-[99999]"
                contentClassName="!min-h-[0] !bg-[#426044]" onClose={() => setShowModal(false)}
            >
                <p className="text-2xl text-center font-bold mt-2 text-[#DFFFE0]">{locale === "en" ? "GIVE A GIFT" : "TRAO MÓN QUÀ"}</p>
                <p style={{ width: "360px" }}
                    className="text-lg text-center mx-auto text-white font-bold">{getTitle()}</p>
                {/* <p className="text-center text-md mt-4 text-[#DFFFE0]">{locale === "en" ? "Register to receive personalized advice from ECHO MEDI" : "Đăng ký gửi quà để được ECHO MEDI tư vấn cụ thể."}</p> */}
                {/* <p className="text-white mb-4 text-center text-md mt-2">{locale === "en" ? "Invest in your loved ones' health with ECHO MEDI's comprehensive health packages. Our packages cover four essential pillars of health, ensuring that your loved ones receive the best care possible. Choose from a wide variety of gifts to accompany your health package and show your loved ones that you care." : "ECHO MEDI cung cấp các gói sức khỏe dựa trên 4 nền tảng quản lý sức khỏe, hãy chọn những món quà để dành tặng cho những người thân yêu của mình."}</p> */}
                <div className="rounded-2xl bg-white p-3" id="send-gift">
                    <div className="m-auto">
                        <div className=" h-full col-span-2 md:col-span-1 row-span-2 m-auto">
                            <div className="flex">
                                <div className="mb-3 w-full text-left p-1 rounded-md">
                                    <p className="font-semibold ">{locale == "en" ? "Choose your Gifting packages" : "Chọn gói khám muốn dành tặng"}</p>
                                    <div className="grid grid-cols-1 sm:grid-cols-1 gap-2">
                                        {(option == "1" || option == "2" || option == "3" || option == "4") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 1} onClick={e => setO1(1)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={1} title={locale == "en" ? "General Healthcare" : "Gói Khám Sức Khỏe Tổng Quát"}
                                                    content={
                                                        locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra cơ bản nhằm phát hiện bệnh sớm cũng như các yếu tố gây bệnh để kịp thời điều chỉnh lối sống, nâng cao chất lượng sống, không để xảy ra bệnh.<br />
                                                            <strong>A/ Khám lâm sàng</strong><br />
                                                            - Kiểm tra sinh hiệu.<br />
                                                            - Chỉ số trọng lượng cơ thể.<br />
                                                            - Thăm hỏi bệnh sử, tiền sử.<br />
                                                            - Khám tổng quát và chuyên khoa.<br />
                                                            - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                            - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                            <strong>B/ Xét nghiệm máu<br /></strong>
                                                            - Công thức máu.<br />
                                                            - Kiểm tra chức năng gan.<br />
                                                            - Kiểm tra chức năng thận.<br />
                                                            - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                            <strong>C/ Xét nghiệm nước tiểu<br /></strong>
                                                            - Xét nghiệm nước tiểu<br />

                                                            <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh<br /></strong>
                                                            - Điện tâm đồ.<br />
                                                            - Siêu âm.<br />
                                                        </div> :
                                                            <div className='font-medium text-justify whitespace-pre-wrap'>
                                                                General checkups with comprehensive examinations are performed to early detect disease and their causes. Thus, we can promptly intervene and adjust lifestyle, improve life quality, and prevent illnesses.<br />
                                                                <strong>A/ Clinical examination</strong><br />
                                                                - Check vital signs<br />
                                                                - Body mass index (BMI)<br />
                                                                - Examining past medical history<br />
                                                                - Conduct General and Specialized examination<br />
                                                                - Evaluate nutrition and living habits<br />
                                                                - Counseling test results<br />
                                                                <strong>B/ Blood test</strong><br />
                                                                - Complete blood count<br />
                                                                - Liver function tests<br />
                                                                - Renal function tests<br />
                                                                - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                                <strong>C/ Urinalysis</strong><br />
                                                                - Urinalysis<br />

                                                                <strong>D/ Diagnostic imaging and functional exploration</strong><br />
                                                                - Electrocardiogram<br />
                                                                - Ultrasound<br />
                                                            </div>
                                                    } />
                                            </div>}
                                        {(option == "1" || option == "3") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 2} onClick={e => setO1(2)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={2} title={locale == "en" ? "Men's Health Care" : "Gói Khám Sức Khỏe Tổng Quát Dành Cho Nam Giới"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        <p>Khám và thực hiện các xét nghiệm kiểm tra các cơ quan trong cơ thể (gan, thận) và các bệnh mạn tính thường gặp như: Tăng huyết áp, đái tháo đường, rối loạn lipid máu, gout, … <br /></p>
                                                        <p>Nhằm điều chỉnh thói quen và lối sống, hoặc lên kế hoạch điều trị kịp thời nếu cần thiết.</p>
                                                        <strong>A/ Khám lâm sàng</strong><br />
                                                        - Kiểm tra sinh hiệu.<br />
                                                        - Chỉ số trọng lượng cơ thể.<br />
                                                        - Thăm hỏi bệnh sử, tiền sử.<br />
                                                        - Khám tổng quát và chuyên khoa.<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />

                                                        <strong>B/ Xét nghiệm máu</strong>
                                                        - Công thức máu.<br />
                                                        - Kiểm tra chức năng gan.<br />
                                                        - Kiểm tra chức năng thận.<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                        - Kiểm tra chức năng tuyến giáp.<br />

                                                        <strong>C/ Xét nghiệm miễn dịch</strong><br />
                                                        - Xét nghiệm miễn dịch.<br />

                                                        <strong>D/ Xét nghiệm nước tiểu</strong><br />
                                                        - Xét nghiệm nước tiểu.<br />

                                                        <strong>E/ Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                        - Điện tâm đồ.<br />
                                                        - Siêu âm.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Performing a health check-up and conducting tests to examine the vital organs in the body such as liver, kidney, as well as common chronic diseases like hypertension, diabetes, lipid disorders, gout, etc. This aims to adjust habits and lifestyle, or to plan for timely treatment if necessary.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />
                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Liver function tests<br />
                                                            - Renal function tests<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                            - Thyroid function tests <br />
                                                            <strong>C/ Immune test</strong><br />
                                                            - Immune tests<br />

                                                            <strong>D/ Urinalysis</strong><br />
                                                            - Urinalysis<br />

                                                            <strong>E/ Diagnostic imaging and functional exploration</strong><br />
                                                            - Electrocardiogram<br />
                                                            - Ultrasound<br />
                                                        </div>
                                                } />
                                            </div>}

                                        {(option == "2" || option == "3") && <div className="flex">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o1 == 3} onClick={e => setO1(3)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={3} title={locale == "en" ? "Women's Health Care" : "Gói Khám Sức Khỏe Tổng Quát Dành Cho Phụ Nữ"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Thăm khám và thực hiện các xét nghiệm kiểm tra toàn diện sức khỏe, nhằm phát hiện bệnh trong giai đoạn sớm. Từ đó, giúp đảm bảo nền tảng sức khỏe tốt, lên kế hoạch điều trị kịp thời, hoặc tư vấn đến các chuyên khoa sâu nếu cần thiết.<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu.<br />
                                                    - Chỉ số trọng lượng cơ thể.<br />
                                                    - Thăm hỏi bệnh sử, tiền sử.<br />
                                                    - Khám tổng quát và chuyên khoa.<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />

                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu.<br />
                                                    - Kiểm tra chức năng gan.<br />
                                                    - Kiểm tra chức năng thận.<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                    - Kiểm tra chức năng tuyến giáp.<br />
                                                    - Xét nghiệm sinh hóa.<br />

                                                    <strong>C/ Xét nghiệm miễn dịch</strong><br />
                                                    - Xét nghiệm miễn dịch.<br />

                                                    <strong>D/ Xét nghiệm nước tiểu</strong><br />
                                                    - Xét nghiệm nước tiểu.<br />

                                                    <strong>E/ Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                    - Điện tâm đồ.<br />
                                                    - Siêu âm<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        General checkups with comprehensive examinations are performed to detect diseases from an early stage and their causes. Thus, help ensure an excellent health foundation, or promptly intervene and consult other specialists if necessary.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />

                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Liver function tests<br />
                                                        - Renal function tests<br />
                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                        - Thyroid function tests<br />
                                                        - Biochemical tests<br />

                                                        <strong>C/ Immune test</strong><br />
                                                        - Immune tests<br />

                                                        <strong>D/ Urinalysis</strong><br />
                                                        - Urinalysis<br />

                                                        <strong>E/ Diagnostic imaging and functional exploration</strong><br />
                                                        - Electrocardiogram<br />
                                                        - Ultrasound<br />
                                                    </div>
                                            } />
                                        </div>

                                            // <div>
                                            //     <input checked={o1 == 3} onClick={e => setO1(3)} type="checkbox" className="text-sm mr-2 inline border-gray-300 rounded h-4 w-4" /><span className="">
                                            //         {locale == "en" ? "Women's Health Care" : "Gói Khám Sức Khỏe Tổng Quát Dành Cho Phụ Nữ"}
                                            //     </span>
                                            // </div>
                                        }
                                        {(option == "1" || option == "2") && <div className="flex">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o1 == 4} onClick={e => setO1(4)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={4} title={locale == "en" ? "Hypertension Management" : "Gói Quản Lý Ngoại Trú Bệnh Tăng Huyết Áp"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Lên kế hoạch điều trị, theo dõi lâu dài. Khám và thực hiện các xét nghiệm định kỳ để kiểm tra và đánh giá triệu chứng, biến chứng gây ra do tăng huyết áp. Tư vấn chế độ ăn, chế độ luyện tập phù hợp.<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu.<br />
                                                    - Chỉ số trọng lượng cơ thể.<br />
                                                    - Thăm hỏi bệnh sử, tiền sử.<br />
                                                    - Khám tổng quát và chuyên khoa.<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu.<br />
                                                    - Kiểm tra chức năng gan.<br />
                                                    - Kiểm tra chức năng thận.<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                    - Xét nghiệm sinh hóa.<br />
                                                    <strong>C/ Xét nghiệm nước tiểu</strong><br />
                                                    - Xét nghiệm nước tiểu.<br />
                                                    <strong>D/Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                    - Điện tâm đồ.<br />
                                                    - Siêu âm.<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Customize a health plan for treatment and long-term follow-up. Examine and perform periodic tests to check and evaluate symptoms and complications caused by high blood pressure. Advice on proper diet and exercise regimen.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />
                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Liver function tests<br />
                                                        - Renal function tests<br />
                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                        - Biochemical tests<br />
                                                        <strong>C/ Urinalysis</strong><br />
                                                        - Urinalysis<br />
                                                        <strong>D/Diagnostic imaging and functional exploration</strong><br />
                                                        - Electrocardiogram<br />
                                                        - Ultrasound<br />
                                                    </div>
                                            } />
                                        </div>
                                        }
                                        {(option == "1" || option == "2") &&

                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 5} onClick={e => setO1(5)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={5} title={locale == "en" ? "Diabetes" : "Gói Quản Lý Ngoại Trú Bệnh Đái Tháo Đường"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Lên kế hoạch theo dõi, tái khám theo hẹn và điều trị lâu dài. Tư vấn chế độ ăn, lối sống phù hợp. Thực hiện các xét nghiệm toàn diện để kiểm tra, nhằm kiểm soát đường huyết và phát hiện sớm các biến chứng của đái tháo đường lên các cơ quan, giúp lên kế hoạch điều trị kịp thời.<br />
                                                        <strong>A/ Khám lâm sàng</strong><br />
                                                        - Kiểm tra sinh hiệu.<br />
                                                        - Chỉ số trọng lượng cơ thể.<br />
                                                        - Thăm hỏi bệnh sử, tiền sử.<br />
                                                        - Khám tổng quát và chuyên khoa.<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                        <strong>B/ Xét nghiệm máu</strong><br />
                                                        - Công thức máu.<br />
                                                        -Kiểm tra chức năng gan.
                                                        - Kiểm tra chức năng thận.<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                        - Xét nghiệm sinh hóa.<br />
                                                        <strong>C/ Xét nghiệm nước tiểu</strong><br />
                                                        - Xét nghiệm nước tiểu.<br />
                                                        <strong>D/Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                        -Điện tâm đồ.
                                                        - Soi đáy mắt.<br />
                                                        - Đo tỷ số huyết áp động mạch cổ chân - cánh tay (ABI).<br />
                                                        -Siêu âm.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Customize a health plan that helps to monitor, follow up and provide long-term treatment, thus providing advice about nutrition, diet, and lifestyle changes. We also perform comprehensive tests to check, control blood sugar, and detect diabetes complications early on to organs, hence helping plan timely treatment.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />
                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Liver function tests<br />
                                                            - Renal function tests<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                            - Biochemical tests<br />
                                                            <strong>C/ Urinalysis</strong><br />
                                                            - Urinalysis<br />
                                                            <strong>D/Diagnostic imaging and functional exploration</strong><br />
                                                            - Electrocardiogram<br />
                                                            - Ophthalmological examination<br />
                                                            - ABI examination<br />
                                                            - Ultrasound<br />
                                                        </div>
                                                } />
                                            </div>

                                            // <div>
                                            //     <input checked={o1 == 5} onClick={e => setO1(5)} type="checkbox" className="text-sm mr-2 inline border-gray-300 rounded h-4 w-4" /><span className="">
                                            //         {locale == "en" ? "Diabetes" : "Gói Quản Lý Ngoại Trú Bệnh Đái Tháo Đường"}
                                            //     </span>
                                            // </div>
                                        }
                                        {/* {(option == "3") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 6} onClick={e => setO1(6)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={6} title={locale == "en" ? "Reproductive Health Screening" : "Tầm Soát Sức Khỏe Sinh Sản"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra cơ bản nhằm phát hiện bệnh sớm cũng như các yếu tố gây bệnh để kịp thời điều chỉnh lối sống, nâng cao chất lượng sống, không để xảy ra bệnh.<br />
                                                        <strong>A/ Khám lâm sàng</strong><br />
                                                        - Kiểm tra sinh hiệu.<br />
                                                        - Chỉ số trọng lượng cơ thể.<br />
                                                        - Thăm hỏi bệnh sử, tiền sử.<br />
                                                        - Khám tổng quát và chuyên khoa.<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                        <strong>B/ Xét nghiệm máu<br /></strong>
                                                        - Công thức máu.<br />
                                                        - Kiểm tra chức năng gan.<br />
                                                        - Kiểm tra chức năng thận.<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                        <strong>C/ Xét nghiệm nước tiểu<br /></strong>
                                                        - Xét nghiệm nước tiểu<br />

                                                        <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh<br /></strong>
                                                        - Điện tâm đồ.<br />
                                                        - Siêu âm.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            General checkups with comprehensive examinations are performed to early detect disease and their causes. Thus, we can promptly intervene and adjust lifestyle, improve life quality, and prevent illnesses.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />
                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Liver function tests<br />
                                                            - Renal function tests<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                            <strong>C/ Urinalysis</strong><br />
                                                            - Urinalysis<br />

                                                            <strong>D/ Diagnostic imaging and functional exploration</strong><br />
                                                            - Electrocardiogram<br />
                                                            - Ultrasound<br />
                                                        </div>
                                                } />
                                            </div>
                                        } */}
                                        {(option == "4") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 7} onClick={e => setO1(7)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={7} title={locale == "en" ? "Post COVID-19" : "Gói Khám Sức Khỏe Hậu COVID-19"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Khám và đánh giá các triệu chứng hậu COVID-19, thực hiện các xét nghiệm kiểm tra toàn diện về chức năng gan, thận và các rối loạn chuyển hóa. Tư vấn và lên kế hoạch điều trị và kiểm soát triệu chứng.<br />
                                                        <strong>A/ Khám lâm sàng</strong><br />
                                                        - Kiểm tra sinh hiệu.<br />
                                                        - Chỉ số trọng lượng cơ thể.<br />
                                                        - Thăm hỏi bệnh sử, tiền sử.<br />
                                                        - Khám tổng quát và chuyên khoa.<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                        <strong>B/ Xét nghiệm máu</strong><br />
                                                        - Công thức máu.<br />
                                                        - Kiểm tra chức năng gan.<br />
                                                        - Kiểm tra chức năng thận.<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                        - Xét nghiệm sinh hóa.<br />
                                                        - Xét nghiệm tình trạng viêm nhiễm.<br />
                                                        <strong>C/Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                        - Điện tâm đồ.<br />
                                                        - Siêu âm.<br />
                                                        - Xquang.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            General checkups to evaluate post-Covid-19 symptoms, combined with comprehensive examinations of internal organs (liver, kidney, etc.) and metabolic disorders. Then, we will consult and design a plan for treatment and symptom control.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />
                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Liver function tests<br />
                                                            - Renal function tests<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                            - Biochemical tests<br />
                                                            - Infection tests<br />
                                                            <strong>C/Diagnostic imaging and functional exploration</strong><br />
                                                            - Electrocardiogram<br />
                                                            - Ultrasound<br />
                                                            - Radiography<br />
                                                        </div>
                                                } />
                                            </div>
                                        }
                                        {(option == "5") &&
                                            <div className="flex mt-2">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 8} onClick={e => setO1(8)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={8} title={locale == "en" ? "Psychological Consultation 60 Minutes" : "Tham Vấn Tâm Lý"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra cơ bản nhằm phát hiện bệnh sớm cũng như các yếu tố gây bệnh để kịp thời điều chỉnh lối sống, nâng cao chất lượng sống, không để xảy ra bệnh.<br />
                                                        <strong>A/ Khám lâm sàng</strong><br />
                                                        - Kiểm tra sinh hiệu.<br />
                                                        - Chỉ số trọng lượng cơ thể.<br />
                                                        - Thăm hỏi bệnh sử, tiền sử.<br />
                                                        - Khám tổng quát và chuyên khoa.<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                        <strong>B/ Xét nghiệm máu<br /></strong>
                                                        - Công thức máu.<br />
                                                        - Kiểm tra chức năng gan.<br />
                                                        - Kiểm tra chức năng thận.<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                        <strong>C/ Xét nghiệm nước tiểu<br /></strong>
                                                        - Xét nghiệm nước tiểu<br />

                                                        <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh<br /></strong>
                                                        - Điện tâm đồ.<br />
                                                        - Siêu âm.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            General checkups with comprehensive examinations are performed to early detect disease and their causes. Thus, we can promptly intervene and adjust lifestyle, improve life quality, and prevent illnesses.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />
                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Liver function tests<br />
                                                            - Renal function tests<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                            <strong>C/ Urinalysis</strong><br />
                                                            - Urinalysis<br />

                                                            <strong>D/ Diagnostic imaging and functional exploration</strong><br />
                                                            - Electrocardiogram<br />
                                                            - Ultrasound<br />
                                                        </div>
                                                } />
                                            </div>

                                            // <div>
                                            //     <input checked={o1 == 7} onClick={e => setO1(7)} type="checkbox" className="text-sm mr-2 inline border-gray-300 rounded h-4 w-4" /><span className="">
                                            //         {locale == "en" ? "Psychological Consultation 60 Minutes" : "Tham Vấn Tâm Lý"}
                                            //     </span>
                                            // </div>
                                        }
                                        {(option == "4") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 9} onClick={e => setO1(9)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={9} title={locale == "en" ? "Musculoskeletal Screening" : "Tầm Soát Bệnh Lý Cơ Xương Khớp"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Thăm hỏi tiền sử, bệnh sử, khám đánh giá nguy cơ, thực hiện xét nghiệm chuyên biệt nhằm phát hiện sớm các loại bệnh thường xuất hiện ở nhân viên văn phòng bao gồm: Tăng huyết áp, đái tháo đường, rối loạn mỡ máu, gout, cơ xương khớp. Từ đó giúp thay đổi lối sống, thói quen giúp ngăn ngừa bệnh hoặc làm chậm tiến triển bệnh và phòng ngừa biến cố trong tương lai.<br />
                                                        <strong>A/ Khám lâm sàng</strong><br />
                                                        - Kiểm tra sinh hiệu.<br />
                                                        - Chỉ số trọng lượng cơ thể.<br />
                                                        - Thăm hỏi bệnh sử, tiền sử.<br />
                                                        - Khám tổng quát và chuyên khoa.<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                        <strong>B/ Xét nghiệm máu</strong><br />
                                                        - Công thức máu.<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                        - Xét nghiệm sinh hóa.<br />
                                                        <strong>C/ Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                        - Siêu âm.<br />
                                                        - Xquang.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Conduct medical history examinations, risk assessments, and specialized tests to detect early diseases that often develop with office workers (hypertension, diabetes, dyslipidemia, gout, and musculoskeletal). This helps in changing lifestyles and habits to prevent illness or slow down disease progression, and prevent future events.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />
                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                            - Biochemical tests<br />
                                                            <strong>C/ Diagnostic imaging and functional exploration</strong><br />
                                                            - Ultrasound<br />
                                                            - Radiography<br />
                                                        </div>
                                                } />
                                            </div>

                                            // <div>
                                            //     <input checked={o1 == 8} onClick={e => setO1(8)} type="checkbox" className="text-sm mr-2 inline border-gray-300 rounded h-4 w-4" /><span className="">
                                            //         {locale == "en" ? "Musculoskeletal Screening" : "Tầm Soát Bệnh Lý Cơ Xương Khớp"}
                                            //     </span>
                                            // </div>
                                        }
                                        {(option == "1" || option == "2" || option == "3" || option == "4") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 10} onClick={e => setO1(10)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={10} title={locale == "en" ? "Gold Membership" : "Gói Thành Viên Vàng"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Gói thành viên vàng là gói chăm sóc sức khỏe nâng cao cho thành viên với các đặc quyền như: Miễn phí không giới hạn tư vấn 24/7, miễn phí khám bệnh tại phòng khám và khám bệnh từ xa 2 lần mỗi tháng, và nhận được các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên `vàng` với mức chi phí tiết kiệm và cố định hằng năm.<br />
                                                        <table>
                                                            <tbody>
                                                                <tr>
                                                                    <td><strong>Dịch vụ</strong></td>
                                                                    <td><strong>Khách hàng thông thường</strong></td>
                                                                    <td><strong>Khi là Thành Viên</strong></td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Khám tại phòng khám với bác sĩ chuyên khoa nội</td>
                                                                    <td>350.000/lần</td>
                                                                    <td>Miễn phí 1 lần/tháng</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Khám bệnh từ xa với bác sĩ chuyên khoa nội</td>
                                                                    <td>250.000đ/lần</td>
                                                                    <td>Miễn phí không giới hạn</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Tư vấn với nhân viên y tế online 24/7</td>
                                                                    <td>50.000đ/lần</td>
                                                                    <td>Miễn phí, không giới hạn</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Gói khám sức khỏe tổng quát</td>
                                                                    <td>2.200.000đ/lần</td>
                                                                    <td>Miễn phí 1 lần/năm</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Dịch vụ giao thuốc tận nhà (Đối với đơn &gt; 500.000 VNĐ và &lt; 5km)</td>
                                                                    <td>50.000đ/lần</td>
                                                                    <td>Miễn phí, không giới hạn</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Dịch vụ khám bệnh tại nhà</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Ưu đãi 10%</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Các dịch vụ tại phòng khám</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Ưu đãi 10%</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Các gói khám sức khỏe tại phòng khám</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Ưu đãi 10%</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Mua thuốc và thực phẩm chức năng tại Nhà thuốc ECHO MEDI</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Ưu đãi 5%</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Tư vấn lên kế hoạch chăm sóc sức khỏe *</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Cơ bản</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Tư vấn tiêm chủng</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Miễn phí</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Điều dưỡng chăm sóc tại nhà</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Ưu đãi&nbsp;<strong>10%</strong></td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Tư vấn thuốc và thực phẩm chức năng cùng dược sĩ</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Miễn phí, không giới hạn</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Kiểm tra đường huyết định kỳ</td>
                                                                    <td>Không được ưu đãi</td>
                                                                    <td>Miễn phí&nbsp;<strong>1 lần/tháng</strong></td>
                                                                </tr>
                                                            </tbody>

                                                        </table>
                                                        * Dịch vụ này chỉ áp dụng cho membership<br />
                                                        - Gói "cơ bản" bao gồm:<br />
                                                        +Lên nội dung kế hoạch chăm sóc sức khỏe cá nhân theo bệnh lý trọn 1 năm.<br />
                                                        +Nhận bài viết cập nhật thông tin sức khỏe hàng tháng.<br />
                                                        +Ưu tiên tham gia các chương trình tư vấn sức khỏe miễn phí của phòng khám.<br />
                                                        - Gói "nâng cao" bao gồm:<br />
                                                        +Cá nhân hóa nội dung kế hoạch chăm sóc sức khỏe theo yêu cầu trọn 1 năm.<br />
                                                        +Hướng dẫn chăm sóc sức khỏe dinh dưỡng theo nhu cầu (không kèm thực đơn mẫu).<br />
                                                        +Nhận bài viết cập nhật thông tin sức khỏe hàng tháng.<br />
                                                        +Ưu tiên tham gia các chương trình tư vấn sức khỏe miễn phí của phòng khám.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Gold membership is an enhanced health care package for members that includes benefits such as free unlimited online consultations 24/7, free clinic check-ups and telemedicine examinations twice a month, and discounts while utilizing clinic services and purchasing medications. Join as a Gold member for a low, set yearly price.		<br />
                                                            <table><tbody><tr><td><strong>Services</strong></td><td><strong>Non-members</strong></td><td><strong>Membership</strong></td></tr><tr><td>In-clinic consultation with internal doctor</td><td>350.000 VND per one visit</td><td>Free once a month</td></tr><tr><td>Telemedicine consultation with internal doctor</td><td>250.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Online 24/7 healthcare consultation with paramedics.</td><td>50.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Periodic general check-up package</td><td>2.200.000 VND per one time</td><td>Once a year</td></tr><tr><td>Delivery for pharmacy orders (For prescriptions &lt; 500.000VNĐ and &lt;5km)</td><td>50.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Home services</td><td>No discount</td><td>10% discount</td></tr><tr><td>Additonal in-clinic and telemedicine consultation after free consultation</td><td>No discount</td><td>10% discount</td></tr><tr><td>Packages</td><td>No discount</td><td>10% discount</td></tr><tr><td>Drugs and supplements</td><td>No discount</td><td>5% discount</td></tr><tr><td>Customized health plan to monitor and enhance your health *</td><td>No discount</td><td>Standard</td></tr><tr><td>Vaccination consultation</td><td>No discount</td><td>Free, unlimited</td></tr><tr><td>Nursing care at home</td><td>No discount</td><td><strong>10%</strong> discount</td></tr><tr><td>Medicine and supplements consultation with pharmacists</td><td>No discount</td><td>Free, unlimited</td></tr><tr><td>Check blood sugar regularly</td><td>No discount</td><td>Free&nbsp;<strong>1 time/month</strong></td></tr></tbody></table>
                                                            *Only applicable for memberships:<br />
                                                            - "Standard" package includes:<br />
                                                            + Create a personal health care strategy for a year.<br />
                                                            + Get weekly health-related information updates.<br />
                                                            + Priority to participate in the clinic's free health consultation programs.<br />
                                                            - "Advanced" Package includes:<br />
                                                            + Customize the content of a 1-year on-demand wellness package.<br />
                                                            + On-demand nutritional health care advice (no sample menu included).<br />
                                                            + Get weekly health-related information updates.<br />
                                                            + Priority to participate in the clinic's free health consultation programs.<br />
                                                        </div>
                                                } />
                                            </div>
                                        }
                                        {(option == "1" || option == "2" || option == "3" || option == "4") &&
                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o1 == 11} onClick={e => setO1(11)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={11} title={locale == "en" ? "Platinum Membership" : "Gói Thành Viên Bạch Kim"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        <p>Gói thành viên bạch kim là gói chăm sóc sức khỏe toàn diện với các đặc quyền:  Miễn phí không giới hạn tư vấn online, miễn phí không giới hạn khám tại phòng khám và khám bệnh từ xa, miễn phí kiểm tra sức khỏe tổng quát 6 tháng/lần, kèm các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên bạch kim với chi phí tiết kiệm và cố định hàng năm.		<br /></p>
                                                        <table><tbody><tr><td><strong>Dịch vụ</strong></td><td><strong>Khách hàng thông thường</strong></td><td><strong>Khi là Thành Viên</strong></td></tr><tr><td>Khám tại phòng khám với bác sĩ chuyên khoa nội</td><td>350.000đ/lần</td><td>Miễn phí không giới hạn</td></tr><tr><td>Khám bệnh từ xa với bác sĩ chuyên khoa nội</td><td>250.000đ/lần</td><td>Miễn phí, không giới hạn</td></tr><tr><td>Tư vấn với nhân viên y tế online 24/7</td><td>50.000đ/lần</td><td>Miễn phí, không giới hạn</td></tr><tr><td>Gói khám sức khỏe tổng quát</td><td>2.200.000đ/lần</td><td>Miễn phí 2 lần/năm</td></tr><tr><td>Dịch vụ giao thuốc tận nhà (Đối với đơn &gt;500.000VNĐ và &lt;5 km)</td><td>50.000đ/lần</td><td>Miễn phí, không giới hạn</td></tr><tr><td>Dịch vụ khám bệnh tại nhà</td><td>Không được ưu đãi</td><td>Ưu đãi 30%</td></tr><tr><td>Dịch vụ tại phòng khám</td><td>Không được ưu đãi</td><td>Ưu đãi 30%</td></tr><tr><td>Gói khám sức khỏe tại phòng khám</td><td>Không được ưu đãi</td><td>Ưu đãi 30%</td></tr><tr><td>Mua thuốc và thực phẩm chức năng tại nhà thuốc Echo Medi</td><td>Không được ưu đãi</td><td>Ưu đãi 15%</td></tr><tr><td>Tư vấn lên kế hoạch chăm sóc sức khỏe *</td><td>Không được ưu đãi</td><td>Nâng cao</td></tr><tr><td>Tư vấn tiêm chủng</td><td>Không được ưu đãi</td><td>Miễn phí</td></tr><tr><td>Điều dưỡng chăm sóc tại nhà</td><td>Không được ưu đãi</td><td>Ưu đãi 30%</td></tr><tr><td>Tư vấn thuốc và thực phẩm chức năng cùng dược sĩ</td><td>Không được ưu đãi</td><td>Miễn phí, không giới hạn</td></tr><tr><td>Kiểm tra đường huyết định kỳ</td><td>Không được ưu đãi</td><td>Miễn phí 1 lần/ tháng</td></tr></tbody></table>
                                                        * Dịch vụ này chỉ áp dụng cho membership<br />
                                                        - Gói "cơ bản" bao gồm:<br />
                                                        +Lên nội dung kế hoạch chăm sóc sức khỏe cá nhân theo bệnh lý trọn 1 năm.<br />
                                                        +Nhận bài viết cập nhật thông tin sức khỏe hàng tháng.<br />
                                                        +Ưu tiên tham gia các chương trình tư vấn sức khỏe miễn phí của phòng khám.<br />
                                                        - Gói "nâng cao" bao gồm:<br />
                                                        +Cá nhân hóa nội dung kế hoạch chăm sóc sức khỏe theo yêu cầu trọn 1 năm.<br />
                                                        +Hướng dẫn chăm sóc sức khỏe dinh dưỡng theo nhu cầu (không kèm thực đơn mẫu).<br />
                                                        +Nhận bài viết cập nhật thông tin sức khỏe hàng tháng.<br />
                                                        +Ưu tiên tham gia các chương trình tư vấn sức khỏe miễn phí của phòng khám.<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Platinum membership is a comprehensive health care package that includes the following benefits: free unlimited online consultation, free unlimited clinic and telemedicine examination, free general health check every 6 months, and discounts for using the service and purchasing medicine at the clinic. Join as a Platinum member for a low, set yearly price.<br />
                                                            <table><tbody><tr><td><strong>Services</strong></td><td><strong>Non-members</strong></td><td><strong>Membership</strong></td></tr><tr><td>In-clinic consultation with internal doctor</td><td>350.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Telemedicine consultation with internal doctor</td><td>250.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Online 24/7 healthcare consultation with paramedics.</td><td>50.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Periodic general check-up package</td><td>2.200.000 VND per one visit</td><td>Twice a year</td></tr><tr><td>Delivery for pharmacy orders (For prescriptions &lt; 500.000VNĐ and &lt;5km)</td><td>50.000 VND per one visit</td><td>Free, unlimited</td></tr><tr><td>Home services</td><td>No discount</td><td>30% discount</td></tr><tr><td>Additonal in-clinic and telemedicine consultation after free consultation</td><td>No discount</td><td>30% discount</td></tr><tr><td>Packages</td><td>No discount</td><td>30% discount</td></tr><tr><td>Drugs and supplements</td><td>No discount</td><td>15% discount</td></tr><tr><td>Customized health plan to monitor and enhance your health *</td><td>No discount</td><td>Advanced</td></tr><tr><td>Vaccination consultation</td><td>No discount</td><td>Free, unlimited</td></tr><tr><td>Nursing care at home</td><td>No discount</td><td><strong>30%</strong> discount</td></tr><tr><td>Medicine and supplements consultation with pharmacists</td><td>No discounts</td><td>Free, unlimited</td></tr><tr><td>Check blood sugar regularly</td><td>No discounts</td><td>Free&nbsp;<strong>1 time/month</strong></td></tr></tbody></table>
                                                            *Only applicable for memberships:<br />
                                                            - "Standard" package includes:<br />
                                                            + Create a personal health care strategy for a year.<br />
                                                            + Get weekly health-related information updates.<br />
                                                            + Priority to participate in the clinic's free health consultation programs.<br />
                                                            - "Advanced" Package includes:<br />
                                                            + Customize the content of a 1-year on-demand wellness package.<br />
                                                            + On-demand nutritional health care advice (no sample menu included).<br />
                                                            + Get weekly health-related information updates.<br />
                                                            + Priority to participate in the clinic's free health consultation programs.<br />
                                                        </div>
                                                } />
                                            </div>
                                        }
                                        {(option == "1" || option == "2" || option == "3" || option == "4") && <div className="flex">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o1 == 12} onClick={e => setO1(12)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={12} title={locale == "en" ? "Your Personal Doctor Package" : "Gói Bác Sĩ riêng của bạn"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Thăm khám toàn diện và thực hiện các xét nghiệm kiểm tra cơ bản nhằm phát hiện bệnh sớm cũng như các yếu tố gây bệnh để kịp thời điều chỉnh lối sống, nâng cao chất lượng sống, không để xảy ra bệnh.<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu.<br />
                                                    - Chỉ số trọng lượng cơ thể.<br />
                                                    - Thăm hỏi bệnh sử, tiền sử.<br />
                                                    - Khám tổng quát và chuyên khoa.<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                    <strong>B/ Xét nghiệm máu<br /></strong>
                                                    - Công thức máu.<br />
                                                    - Kiểm tra chức năng gan.<br />
                                                    - Kiểm tra chức năng thận.<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                    <strong>C/ Xét nghiệm nước tiểu<br /></strong>
                                                    - Xét nghiệm nước tiểu<br />

                                                    <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh<br /></strong>
                                                    - Điện tâm đồ.<br />
                                                    - Siêu âm.<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        General checkups with comprehensive examinations are performed to early detect disease and their causes. Thus, we can promptly intervene and adjust lifestyle, improve life quality, and prevent illnesses.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />
                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Liver function tests<br />
                                                        - Renal function tests<br />
                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                        <strong>C/ Urinalysis</strong><br />
                                                        - Urinalysis<br />

                                                        <strong>D/ Diagnostic imaging and functional exploration</strong><br />
                                                        - Electrocardiogram<br />
                                                        - Ultrasound<br />
                                                    </div>
                                            } />
                                        </div>
                                        }
                                    </div>
                                    {/* {option != "5" && <p className="font-semibold mt-5">
                                        {locale == "en" ? "Add-on package" : "Gói khám bổ sung"}</p>} */}
                                    {(option == "1" || option == "4") &&
                                        <>
                                            <p className="font-semibold mt-5">
                                                {locale == "en" ? "Add-on package" : "Gói khám bổ sung"}</p>

                                            <div className="flex">
                                                <div className="w-[24px] h-[24px] mr-2">
                                                    <input checked={o2 == 1} onClick={e => setO2(o2 == 1 ? -1 : 1)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                                </div>
                                                <AccordionGift active={active} setActive={setActive} id={13} title={locale == "en" ? "Smoking Cessation Program" : "Chương Trình Cai Thuốc Lá"} content={
                                                    locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Chương trình cai thuốc lá là một kế hoạch điều trị được cá nhân hóa, giúp khách hàng từ bỏ thói quen này. Lộ trình của ECHO MEDI hỗ trợ từng cá nhân trong việc chấm dứt thói quen hút thuốc, sát sao đồng hành cùng khách hàng nhằm sẵn sàng hỗ trợ bạn tốt nhất có thể.<br />
                                                        <strong>A/ Khám lâm sàng:</strong><br />
                                                        - Kiểm tra sinh hiệu<br />
                                                        - Chỉ số trọng lượng cơ thể<br />
                                                        - Thăm hỏi bệnh sử, tiền sử<br />
                                                        - Khám tổng quát và chuyên khoa<br />
                                                        - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt<br />
                                                        - Tư vấn trước và sau khi có kết quả xét nghiệm<br />

                                                        <strong>B/ Xét nghiệm máu:</strong><br />
                                                        - Công thức máu<br />
                                                        - Kiểm tra chức năng gan<br />
                                                        - Kiểm tra chức năng thận<br />
                                                        - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…)<br />

                                                        <strong>C/ Xét nghiệm nước tiểu:</strong><br />
                                                        - Xét nghiệm nước tiểu<br />

                                                        <strong>D/Thăm dò chức năng và chẩn đoán hình ảnh:</strong><br />
                                                        - Điện tâm đồ<br />
                                                        - Siêu âm<br />
                                                        - Xquang<br />
                                                    </div> :
                                                        <div className='font-medium text-justify whitespace-pre-wrap'>
                                                            Design a personalized treatment plan for smoking cessation. Our program also offers individualized assistance to help you succeed beginning with the best advice for your situation.<br />
                                                            <strong>A/ Clinical examination</strong><br />
                                                            - Check vital signs<br />
                                                            - Body mass index (BMI)<br />
                                                            - Examining past medical history<br />
                                                            - Conduct General and Specialized examination<br />
                                                            - Evaluate nutrition and living habits<br />
                                                            - Counseling test results<br />

                                                            <strong>B/ Blood test</strong><br />
                                                            - Complete blood count<br />
                                                            - Liver function tests<br />
                                                            - Renal function tests<br />
                                                            - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />

                                                            <strong>C/ Urinalysis</strong><br />
                                                            - Urinalysis<br />

                                                            <strong>D/Diagnostic imaging and functional exploration</strong><br />
                                                            - Electrocardiogram<br />
                                                            - Ultrasound<br />
                                                            - Radiography<br />
                                                        </div>
                                                } />
                                            </div>
                                        </>
                                    }

                                    {/* {(option == "2" || option == "4") &&

                                        <div className="flex mt-2">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o2 == 2} onClick={e => setO2(o2 == 2 ? -1 : 2)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={14} title={locale == "en" ? "Mental Wellbeing" : "Sức Khỏe Tinh Thần"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Đội ngũ ECHO MEDI sẽ hỗ trợ bạn xây dựng kết nối giữa tâm trí và cơ thể. Bằng việc giảm thiểu những căng thẳng và lo lắng, từ đó giúp thể chất tốt hơn lên để bạn có thể phục hồi sức khỏe tinh thần của mình.<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu<br />
                                                    - Chỉ số trọng lượng cơ thể<br />
                                                    - Thăm hỏi bệnh sử, tiền sử<br />
                                                    - Khám tổng quát và chuyên khoa<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm<br />

                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu<br />
                                                    - Kiểm tra chức năng gan<br />
                                                    - Kiểm tra chức năng thận<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…)<br />

                                                    <strong>C/ Xét nghiệm nước tiểu</strong><br />
                                                    - Xét nghiệm nước tiểu<br />

                                                    <strong>D/Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                    - Điện tâm đồ<br />
                                                    - Siêu âm<br />
                                                    - Xquang<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Our experts guide you through stress, anxiety, and the physical symptoms they cause, helping you repair the mind-body connection and regain your mental health.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />

                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Liver function tests<br />
                                                        - Renal function tests<br />
                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />

                                                        <strong>C/ Urinalysis</strong><br />
                                                        - Urinalysis<br />

                                                        <strong>D/Diagnostic imaging and functional exploration</strong><br />
                                                        - Electrocardiogram<br />
                                                        - Ultrasound<br />
                                                    </div>
                                            } />
                                        </div>

                                        // <div>
                                        //     <input checked={o2 == 2} onClick={e => setO2(2)} type="checkbox" className="mr-2 inline border-gray-300 rounded h-4 w-4" /><span className="">
                                        //         {locale == "en" ? "Mental Wellbeing" : "Sức Khỏe Tinh Thần"}</span>
                                        // </div>
                                    } */}
                                    {/* {(option == "1" || option == "2" || option == "3" || option == "4") &&
                                        <div className="flex mt-2">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o2 == 3} onClick={e => setO2(o2 == 3 ? -1 : 3)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={15} title={locale == "en" ? "Weight Management" : "Quản Lý Cân Nặng"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Đội ngũ ECHO MEDI sẽ giúp bạn lên kế hoạch và đặt mục tiêu thực tế. Chúng tôi sẽ đồng hành cùng bạn trên hành trình xây dựng một lối sống lành mạnh và kiên định với mục tiêu của mình. Qua chứng minh lâm sàng cho thấy chương trình này rất dễ thực hiện và duy trì. Nhờ việc tập trung vào ăn uống lành mạnh, khách hàng cũng có thêm lợi ích về giảm huyết áp và cholesterol.<br />
                                                    <strong>Tác Động / Triệu Chứng<br /></strong>

                                                    - Minh chứng thực tế cho lý do chúng ta cần giảm cân<br />
                                                    - Giảm cân cải thiện sức khỏe như thế nào?<br />
                                                    - Giảm huyết áp.<br />
                                                    - Giảm nồng độ chất béo trung tính.<br />
                                                    - Giảm nguy cơ mắc bệnh tim mạch.<br />
                                                    - Cải thiện khả năng vận động và giảm đau.<br />
                                                    - Quan hệ tình dục tốt hơn và ít rối loạn cương dương hơn.<br />
                                                    - Cải thiện tâm trạng.<br />
                                                    - Ngủ ngon hơn.<br />
                                                    - Tự tin hơn.<br />
                                                    <br />
                                                    <strong>Echo Medi Giải Quyết Bệnh Lý Như Thế Nào?<br /></strong>

                                                    <strong>ECHO MEDI xem xét bệnh sử của bạn từ mọi góc độ:<br /></strong>
                                                    - Kiểm tra chế độ dinh dưỡng<br />
                                                    - Đánh giá tình trạng thể chất và tinh thần<br />
                                                    - Kiểm tra bệnh lý<br />


                                                    <strong>ECHO MEDI thực hiện các xét nghiệm chẩn đoán chuyên sâu:<br /></strong>
                                                    - Xét nghiệm máu<br />
                                                    - Xét nghiệm di truyền<br />
                                                    - Theo dõi đường - mỡ máu<br />


                                                    <strong>ECHO MEDI chỉ định các kế hoạch toàn diện, được xây dựng cho từng cá nhân:<br /></strong>
                                                    - Giảm cân bắt đầu từ dinh dưỡng<br />
                                                    - Nhận thức và tự chủ trong dinh dưỡng<br />
                                                    - Tăng cường vận động<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Experts at ECHO MEDI are qualified to work with you to develop a plan and identify necessary objectives. We'll support and keep you on track as you work toward a healthy lifestyle. By combining a simplified weight loss approach with scientific evidences, we will help you maintain the weight loss as well as other health benefits such as lowering blood pressure and cholesterol level.<br />
                                                        <strong>Impacts/Symtoms<br /></strong>

                                                        - Realistic objectives for why we need weight loss program<br />
                                                        - How weight loss improves your health<br />
                                                        - Lower blood pressure<br />
                                                        - Lower levels of triglycerides.<br />
                                                        - Less risk of heart disease.<br />
                                                        - Improved mobility and reduced pain.<br />
                                                        - Better sex and less erectile dysfunction.<br />
                                                        - Improved mood.<br />
                                                        - Better sleep.<br />
                                                        - Higher self-esteem.<br />

                                                        <strong>How We Treat Condition ?<br /></strong>

                                                        <strong>We look at your health history from every angle<br /></strong>
                                                        - Check your nutrition habits<br />
                                                        - Evaluation your physical and mental state<br />
                                                        - Morbidities screening<br />


                                                        <strong>We run advanced diagnostic testing<br /></strong>
                                                        - Blood Tests<br />
                                                        - Genetics Testing<br />
                                                        - Glucose and Lipid Monitoring<br />


                                                        <strong>We prescribe holistic, personalized plans<br /></strong>
                                                        - Giảm cân bắt đầu từ dinh dưỡng<br />
                                                        - Nhận thức và tự chủ trong dinh dưỡng<br />
                                                        - Tăng cường vận động<br />
                                                    </div>
                                            } />
                                        </div>
                                    } */}
                                    {/* {(option == "1") &&

                                        <div className="flex mt-2">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o2 == 4} onClick={e => setO2(o2 == 4 ? -1 : 4)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={16} title={locale == "en" ? "Heart Health Program" : "Chương Trình Sức Khỏe Tim Mạch"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Tim mạch đóng một vai trò quan trọng đối với sức khỏe của bạn. Bác sĩ của ECHO MEDI sẽ thiết kế một lộ trình riêng qua việc kiểm tra chế độ dinh dưỡng, đánh giá tình trạng thể chất và tinh thần nhằm phòng ngừa bệnh lý hay giúp kiểm soát các bệnh mạn tính (huyết áp cao, béo phì, v.v.).<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu<br />
                                                    - Chỉ số trọng lượng cơ thể<br />
                                                    - Thăm hỏi bệnh sử, tiền sử<br />
                                                    - Khám tổng quát và chuyên khoa<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm<br />

                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu<br />
                                                    - Kiểm tra chức năng gan<br />
                                                    - Kiểm tra chức năng thận<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…)<br />

                                                    - Xét nghiệm sinh hoá<br />
                                                    - Xét nghiệm tình trạng viêm nhiễm<br />
                                                    <strong>C/ Xét nghiệm nước tiểu</strong><br />
                                                    - Xét nghiệm nước tiểu<br />

                                                    <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                    - Điện tâm đồ<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Your heart condition can critically impact your overall health. Clinician-led program at ECHO MEDI will develop strategies to help control symptoms like high blood pressure or prevent future diseases like diabetes and stroke..<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />

                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Liver function tests<br />
                                                        - Renal function tests<br />
                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                        - Biochemical tests<br />

                                                        - Infection tests<br />
                                                        <strong>C/ Urinalysis</strong><br />
                                                        - Urinalysis<br />

                                                        <strong>D/ Diagnostic imaging and functional exploration</strong><br />
                                                        - Electrocardiogram<br />
                                                    </div>
                                            } />
                                        </div>
                                    } */}
                                    {/* {(option == "2") &&
                                        <div className="flex mt-2">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o2 == 5} onClick={e => setO2(o2 == 5 ? -1 : 5)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={17} title={locale == "en" ? "Hormonal Health" : "Sức Khỏe Nội Tiết Tố"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Thăm hỏi tiền sử, bệnh sử, khám đánh giá nguy cơ, xét nghiệm chuyên biệt nội tiết tố nữ nhằm phát hiện sớm bệnh các rối loạn chuyển hóa từ khi chưa có triệu chứng rõ rệt. Qua đó giúp thay đổi lối sống nhằm ngăn ngừa bệnh, giữ gìn nhan sắc và sức khỏe toàn diện.<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu.<br />
                                                    - Chỉ số trọng lượng cơ thể.<br />
                                                    - Thăm hỏi bệnh sử, tiền sử.<br />
                                                    - Khám tổng quát và chuyên khoa.<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt.<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm.<br />
                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu.<br />
                                                    - Kiểm tra chức năng gan.<br />
                                                    - Kiểm tra chức năng thận.<br />
                                                    - Kiểm tra chức năng tuyến giáp.<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…).<br />
                                                    <strong>C/ Xét nghiệm nội tiết tố</strong><br />
                                                    - Xét nghiệm nội tiết tố.<br />
                                                    <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                    - Siêu âm.<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Conduct medical history examination, risk assessment examination, and specialized testing of female hormones to detect diseases promptly even when there are no obvious symptoms. This helps in changing lifestyles to prevent disease, maintain the beauty and comprehensive health.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />

                                                        - Examining past medical history<br />

                                                        - Conduct General and Specialized examination<br />

                                                        - Evaluate nutrition and living habits<br />

                                                        - Counseling test results<br />

                                                        <strong>B/ Blood test</strong><br />

                                                        - Complete blood count<br />

                                                        - Liver function tests<br />

                                                        - Renal function tests<br />

                                                        - Thyroid function tests<br />

                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />

                                                        <strong>C/ Hormonal tests</strong><br />

                                                        - Hormonal tests<br />

                                                        <strong>D/ Diagnostic imaging and functional exploration</strong><br />

                                                        - Ultrasound<br />
                                                    </div>
                                            } />
                                        </div>
                                    }
                                    {(option == "3") &&
                                        <div className="flex mt-2">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o2 == 6} onClick={e => setO2(o2 == 6 ? -1 : 6)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={17} title={locale == "en" ? "Metabolism And Heart Health" : "Khả Năng Trao Đổi Chất Và Sức Khỏe Tim Mạch"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Tim mạch đóng một vai trò quan trọng đối với sức khỏe của bạn. Bác sĩ của ECHO MEDI sẽ thiết kế một lộ trình riêng qua việc kiểm tra chế độ dinh dưỡng, đánh giá tình trạng thể chất và tinh thần nhằm phòng ngừa bệnh lý hay giúp kiểm soát các bệnh mạn tính (huyết áp cao, béo phì, v.v.).<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu<br />
                                                    - Chỉ số trọng lượng cơ thể<br />
                                                    - Thăm hỏi bệnh sử, tiền sử<br />
                                                    - Khám tổng quát và chuyên khoa<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm<br />

                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu<br />
                                                    - Kiểm tra chức năng gan<br />
                                                    - Kiểm tra chức năng thận<br />
                                                    - Tầm soát rối loạn chuyển hoá (Đái tháo đường, Mỡ máu, Gout,…)<br />

                                                    - Xét nghiệm sinh hoá<br />
                                                    - Xét nghiệm tình trạng viêm nhiễm<br />
                                                    <strong>C/ Xét nghiệm nước tiểu</strong><br />
                                                    - Xét nghiệm nước tiểu<br />

                                                    <strong>D/ Thăm dò chức năng và chẩn đoán hình ảnh</strong><br />
                                                    - Điện tâm đồ<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Your heart condition can critically impact your overall health. Clinician-led program at ECHO MEDI will develop strategies to help control symptoms like high blood pressure or prevent future diseases like diabetes and stroke.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />

                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Liver function tests<br />
                                                        - Renal function tests<br />
                                                        - Metabolic syndrome screening (Diabetes, Hyperlipidemia, Gout, ...)<br />
                                                        - Biochemical tests<br />

                                                        - Infection tests<br />
                                                        <strong>C/ Urinalysis</strong><br />
                                                        - Urinalysis<br />

                                                        <strong>D/ Diagnostic imaging and functional exploration</strong><br />
                                                        - Electrocardiogram<br />
                                                    </div>
                                            } />
                                        </div>

                                    } */}
                                    {/* {(option == "3") &&
                                        <div className="flex mt-2">
                                            <div className="w-[24px] h-[24px] mr-2">
                                                <input checked={o2 == 7} onClick={e => setO2(o2 == 7 ? -1 : 7)} type="checkbox" className="text-sm mr-2 inline rounded w-[24px] h-[24px]" />
                                            </div>
                                            <AccordionGift active={active} setActive={setActive} id={18} title={locale == "en" ? "Skincare And Anti-aging Therapy" : "Chăm Sóc Da Và Ngăn Ngừa Lão Hóa"} content={
                                                locale == "vi" ? <div className='font-medium text-justify whitespace-pre-wrap'>
                                                    Trong lộ trình tư vấn chăm sóc da, tóc, móng của ECHO MEDI, đội ngũ chuyên môn sẽ tập trung mục tiêu của từng khách hàng, từ đó đề ra các phương pháp điều trị tối ưu nhất cho từng đặc điểm và nhu cầu của bạn.<br />
                                                    <strong>A/ Khám lâm sàng</strong><br />
                                                    - Kiểm tra sinh hiệu<br />
                                                    - Chỉ số trọng lượng cơ thể<br />
                                                    - Thăm hỏi bệnh sử, tiền sử<br />
                                                    - Khám tổng quát và chuyên khoa<br />
                                                    - Kiểm tra chế độ dinh dưỡng và thói quen sinh hoạt<br />
                                                    - Tư vấn trước và sau khi có kết quả xét nghiệm<br />

                                                    <strong>B/ Xét nghiệm máu</strong><br />
                                                    - Công thức máu<br />
                                                    - Xét nghiệm sinh hóa<br />
                                                </div> :
                                                    <div className='font-medium text-justify whitespace-pre-wrap'>
                                                        Our medical professional will evaluate your target areas and your aesthetic objectives during our virtual skin, hair and nails care consultation and will make the best treatment recommendations based on your preferences and skin, hair, nails types.<br />
                                                        <strong>A/ Clinical examination</strong><br />
                                                        - Check vital signs<br />
                                                        - Body mass index (BMI)<br />
                                                        - Examining past medical history<br />
                                                        - Conduct General and Specialized examination<br />
                                                        - Evaluate nutrition and living habits<br />
                                                        - Counseling test results<br />

                                                        <strong>B/ Blood test</strong><br />
                                                        - Complete blood count<br />
                                                        - Biochemical tests<br />
                                                    </div>
                                            } />
                                        </div>
                                    } */}
                                    <div className="w-full col-span-2 md:col-span-1 row-span-2 flex flex-col sm:flex-row sm:justify-start justify-center h-auto">
                                        <div onClick={e => setShowModal(false)} className="py-2 px-8 inline bg-green-900 rounded-2xl text-white font-bold mt-4 mr-0 sm:mr-4 text-center">
                                            {locale == "vi" ? "Đóng" : "Close"}
                                        </div>

                                        {/* {o1 != -1 && <div className="py-2 px-8 inline bg-green-900 rounded-2xl text-white font-bold mt-4 text-center mr-0 sm:mr-4 ">
                                            <LinkComponent locale={locale} skipLocaleHandling={false} href={"/cart_gift?" + "o1=" + o1 + "&o2=" + o2}>{locale == "vi" ? "Tiếp tục" : "Proceed"}</LinkComponent>
                                        </div>} */}
                                        <div className="sm:ml-2 ml-0 py-2 px-8 inline bg-green-900 rounded-2xl text-white font-bold mt-4 mr-0 sm:mr-4 text-center">
                                            <a href="tel:1900638408">{locale == "vi" ? "Liên hệ 1900 638 408" : "Contact us 1900 638 408"}</a>
                                        </div>
                                    </div>
                                </div></div></div></div></div>
            </Modal>
            <Contact />
            <Toaster
                position="bottom-center"
            />
        </>
    );
};

export default Home;
