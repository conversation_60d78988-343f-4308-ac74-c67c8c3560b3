import type { NextPage } from "next"
import Head from "next/head"
import { useEffect, useState } from "react"
import axios from "axios"
import { makeStaticProps } from '../../lib/getStatic';

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [
        {
            params: {
                locale: 'en',
                slug: "test",
                label: "test2",
            }
        },
        {
            params: {
                locale: 'vi',
                slug: "test",
                label: "test2",
            }
        }],
})

export { getStaticPaths, getStaticProps }

interface Gift {
  id: number
  name: string
  description: string
  maxCount: number
  currentCount: number
  value: string
}

const EventsPMH: NextPage = () => {
    const [form, setForm] = useState({
        fullName: "",
        phoneNumber: "",
        eventName: "EVENTS",
        eventCode: "EVENTS",
        selectedGift: 1,
        giftSource: "",
    })

    const [success, setSuccess] = useState(false)
    const [phoneError, setPhoneError] = useState(false)
    const [nameError, setNameError] = useState(false)
    const [error, setError] = useState("")
    const [loading, setLoading] = useState(false)
    const [gifts, setGifts] = useState<Gift[]>([])
    const [fetchingGifts, setFetchingGifts] = useState(true)

    useEffect(() => {
        const fetchGifts = async () => {
            setFetchingGifts(true)
            try {
                const response = await axios.get("https://api.echomedi.com/api/gifts")
                if (response.data.success && response.data.data) {
                    const availableGifts = response.data.data.filter((g: Gift) => g.currentCount < g.maxCount)
                    setGifts(availableGifts)
                    if (availableGifts.length > 0) {
                        setForm((prev) => ({
                            ...prev,
                            selectedGift: availableGifts[0].id,
                            giftSource: availableGifts[0].description,
                        }))
                    }
                } else {
                    setError("Không thể tải danh sách quà tặng")
                }
            } catch (error) {
                console.error("Error fetching gifts:", error)
                setError("Đã xảy ra lỗi khi tải danh sách quà tặng")
            } finally {
                setFetchingGifts(false)
            }
        }

        fetchGifts()
    }, [])

    const getFilteredGifts = () => {
        return gifts.filter((g) => g.currentCount < g.maxCount)
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target

        if (name === "phoneNumber") {
            const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/
            setPhoneError(!phoneRegex.test(value))
        }

        if (name === "fullName") {
            const nameRegex = /^[A-Za-zÀ-ỹ\s]{2,}$/
            setNameError(!nameRegex.test(value))
        }

        if (name === "selectedGift") {
            const selectedGift = gifts.find(gift => gift.id === parseInt(value))
            setForm({ 
                ...form, 
                [name]: parseInt(value),
                giftSource: selectedGift?.description || ""
            })
        } else {
            setForm({ ...form, [name]: value })
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setLoading(true)
        setError("")

        let hasError = false

        if (form.fullName.trim() === "") {
            setNameError(true)
            hasError = true
        }

        if (form.phoneNumber.trim() === "") {
            setPhoneError(true)
            hasError = true
        }

        if (phoneError || nameError || hasError) {
            setLoading(false)
            return
        }

        try {
            const res = await axios.post("https://api.echomedi.com/api/events/create-event", form)

            if (res.status === 201) {
                setSuccess(true)
                const availableGifts = gifts.filter((g) => g.currentCount < g.maxCount)
                setForm({
                    fullName: "",
                    phoneNumber: "",
                    eventName: "EVENTS",
                    eventCode: "EVENTS",
                    selectedGift: availableGifts.length > 0 ? availableGifts[0].id : 1,
                    giftSource: availableGifts.length > 0 ? availableGifts[0].description : "",
                })
            }
        } catch (error: any) {
            if (axios.isAxiosError(error)) {
                if (error.response?.data?.message === "Phone number already exists") {
                    setError("Số điện thoại này đã được đăng ký trước đó. Vui lòng kiểm tra lại.");
                } else {
                    setError("Có lỗi xảy ra khi gửi thông tin. Vui lòng thử lại sau.")
                }
            } else {
                setError("Đã xảy ra lỗi không xác định. Vui lòng thử lại.")
            }
        } finally {
            setLoading(false)
        }
    }

    return (
        <>
            <Head>
                <title>HealTalks - Sự kiện PMH</title>
            </Head>
            <main className="min-h-screen bg-gray-50 py-12 px-4">
                <div className="max-w-lg mx-auto space-y-10">
                    <section>
                        {success && (
                            <div className="space-y-6 bg-white px-6 py-24 rounded-lg shadow-sm">
                                <div className="!w-32 !h-32 mx-auto mb-4 rounded-full overflow-hidden bg-gray-100">
                                    <img
                                        src="https://api.echomedi.com/uploads/success_66e7882952.png"
                                        className="!w-32 !h-32 object-cover"
                                        alt="Success"
                                    />
                                </div>
                                <h1 className="text-center font-bold uppercase lg:text-xl text-sm text-green-600">
                                    Bạn đã nhận quà thành công
                                </h1>
                            </div>
                        )}

                        {!success && (
                            <form className="space-y-6 bg-white p-6 rounded-lg shadow-sm" onSubmit={handleSubmit}>
                                <div className="text-center mb-6">
                                    <h2 className="text-2xl font-bold uppercase">Đăng ký nhận quà tặng hấp dẫn</h2>
                                </div>

                                <div>
                                    <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Họ và tên <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="fullName"
                                        name="fullName"
                                        value={form.fullName}
                                        onChange={handleChange}
                                        placeholder="Nhập họ và tên của bạn"
                                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#156634] ${
                                            nameError ? "border-red-500" : "border-gray-300"
                                        }`}
                                    />
                                    {nameError && (
                                        <p className="mt-1 text-sm text-red-500">
                                            Vui lòng nhập họ tên hợp lệ (ít nhất 2 ký tự, chỉ chứa chữ cái)
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                                        Số điện thoại <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        value={form.phoneNumber}
                                        onChange={handleChange}
                                        placeholder="Nhập số điện thoại"
                                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#156634] ${
                                            phoneError ? "border-red-500" : "border-gray-300"
                                        }`}
                                    />
                                    {phoneError && (
                                        <p className="mt-1 text-sm text-red-500">
                                            Vui lòng nhập đúng định dạng số điện thoại (10 số, bắt đầu bằng 03, 05, 07, 08, 09)
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <label htmlFor="selectedGift" className="block text-sm font-medium text-gray-700 mb-1">
                                        Chọn quà tặng <span className="text-red-500">*</span>
                                    </label>
                                    {fetchingGifts ? (
                                        <div className="flex items-center space-x-2 text-sm py-3 px-3 border border-gray-300 rounded-md bg-gray-50">
                                            <span className="inline-block h-4 w-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin"></span>
                                            <span>Đang tải danh sách quà tặng...</span>
                                        </div>
                                    )  : (
                                        <>
                                            <select
                                                id="selectedGift"
                                                name="selectedGift"
                                                value={form.selectedGift}
                                                onChange={handleChange}
                                                className="w-full h-11 px-3 py-2 border border-gray-300 bg-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#156634]"
                                            >
                                                {gifts.map((gift) => (
                                                    <option key={gift.id} value={gift.id}>
                                                        {gift.name}
                                                    </option>
                                                ))}
                                            </select>
                                        </>
                                    )}
                                </div>

                                {error && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                                        <p className="text-sm text-red-600">{error}</p>
                                    </div>
                                )}

                                <button
                                    type="submit"
                                    className="mx-auto w-[200px] font-medium flex items-center justify-center px-6 py-3 text-white rounded-full bg-[#156634] hover:bg-[#14813d] focus:outline-none focus:ring-2 focus:ring-[#156634] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    disabled={phoneError || nameError || loading || fetchingGifts || getFilteredGifts().length === 0}
                                >
                                    {loading ? (
                                        <>
                                            <span className="inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                                            Đang gửi...
                                        </>
                                    ) : (
                                        "Gửi thông tin"
                                    )}
                                </button>
                            </form>
                        )}
                    </section>
                </div>
            </main>
        </>
    )
}

export default EventsPMH