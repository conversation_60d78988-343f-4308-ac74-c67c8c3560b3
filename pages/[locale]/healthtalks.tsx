import { NextPage } from 'next';
import Image from 'next/image'
import { useRouter } from 'next/router';
import React, { useState } from 'react'
import { shimmer, toBase64 } from '../../lib/ui';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Toaster } from "react-hot-toast";
import Contact from "../../components/Contact/Contact";
import { dataImage, dataImageCompany } from '../../utils/dataBusiness';
import useIsMobile from '../../utils/detectMob';
import { makeStaticProps } from '../../lib/getStatic';
import BookingBusiness from '../../components/BookingBusiness';
import Head from 'next/head';
import ModalBooking from '../../components/BookingService';
import { dataImageHeathyWorkshop } from '../../components/InstagramGallery/Partner';
import ImageSlider from '../../components/Slider/HealthTalksSlider';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

import { EffectCoverflow, Pagination, Navigation } from 'swiper';
import HealthSlider, { cardDatas } from '../../components/Slider/HealthSlider';
import BookingHealthTalks from '../../components/BookingHealthTalks';
import HealthTalksMainCore, { dataHealthIcon } from '../../components/HealthTalks/HealthTalksMainCore';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }

const FamilyDoctorPages: NextPage = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const isMobile = useIsMobile();
    const [showModal, setShowModal] = useState(false);
    const handleShowModal = () => {
        setShowModal(true);
    };
    const dataHealth = dataHealthIcon(locale);
    return (
        <>
            <Head>
                <title>ECHO MEDI - Health Talks</title>
                <meta name="description" content="Khám phá chương trình tọa đàm sức khỏe của Echo Medi, nơi cung cấp thông tin và giải đáp về các vấn đề y tế, giúp bạn chăm sóc sức khỏe toàn diện." />
                <meta name="keywords" content="Echo Medi, tọa đàm sức khỏe, chương trình sức khỏe, thông tin y tế, chăm sóc sức khỏe" />
                <meta property="og:title" content="Echo Medi - Chương Trình Tọa Đàm Sức Khỏe" />
                <meta property="og:description" content="Tham gia chương trình Health Talks của Echo Medi để cập nhật kiến thức y tế và chăm sóc sức khỏe." />
                <meta property="og:image" content="/banner/Family-Doctor-VIE.webp" />
                <meta property="og:type" content="website" />
                <meta property="twitter:card" content="summary_large_image" />
                <meta property="twitter:title" content="Echo Medi - Health Talks (Tọa Đàm Sức Khỏe)" />
                <meta property="twitter:description" content="Echo Medi mang đến chương trình tọa đàm sức khỏe với những thông tin hữu ích, giúp bạn hiểu rõ hơn về sức khỏe và phòng ngừa bệnh tật." />
                <meta property="twitter:image" content="/banner/Family-Doctor-VIE.webp" />
            </Head>
            <section>
                <div className="mx-auto">
                    <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                        <div className="w-full relative">
                            <Image
                                src="/banner/bannerhealthdiscussion.webp"
                                alt="Banner Health Discussion"
                                width={1920}
                                height={300}
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                                className="object-center"
                                layout="responsive"
                            />
                        </div>
                        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 2xl:w-1/2 absolute md:px-16 px-4">
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block whitespace-pre-line leading-[calc(1em+12px)]">
                                {locale === "vi" ? "chương trình TỌA ĐÀM SỨC KHỎE" : "Health Talks"}
                            </h2>
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                                {locale === "vi" ? "chương trình\n TỌA ĐÀM SỨC KHỎE" : "Health Talks"}
                            </h2>
                            <p className="text-base text-justify hidden md:block">
                                {locale === "en" ? "To foster a healthy working environment, ECHO MEDI offers health seminars on practical topics, tailored to businesses' needs, promoting proactive care and enhancing employees' physical and psychological well-being." : "Nhằm xây dựng môi trường làm việc khỏe mạnh, ECHO MEDI cung cấp tọa đàm sức khỏe với các chủ đề thiết thực, cá nhân hóa theo nhu cầu của doanh nghiệp, khuyến khích chủ động chăm sóc và cải thiện sức khỏe thể chất, tinh thần cho nhân viên."}
                            </p>
                        </div>
                    </div>
                    <p className="text-sm my-6 text-justify px-4 block md:hidden">
                        {locale === "en" ? "To foster a healthy working environment, ECHO MEDI offers health seminars on practical topics, tailored to businesses' needs, promoting proactive care and enhancing employees' physical and psychological well-being." : "Nhằm xây dựng môi trường làm việc khỏe mạnh, ECHO MEDI cung cấp tọa đàm sức khỏe với các chủ đề thiết thực, cá nhân hóa theo nhu cầu của doanh nghiệp, khuyến khích chủ động chăm sóc và cải thiện sức khỏe thể chất, tinh thần cho nhân viên."}
                    </p>
                </div>
            </section>
            <section className="relative">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="w-full md:bg-[#F9FFFC] md:py-12 md:px-12 rounded-[32px] md:mt-12">
                        <div className="flex flex-col items-center justify-between md:flex-row md:gap-12">
                            <div className="relative hidden md:block border border-[#14813D] rounded-lg md:w-1/2">
                                <div className="flex items-center justify-center text-xl absolute whitespace-nowrap -top-4 left-1/2 transform -translate-x-1/2 bg-white px-4 font-semibold text-[#156634]">
                                    {locale == "en" ? "Some notable corporate partners" : "Một số đối tác tiêu biểu"}
                                </div>
                                <div className="grid grid-cols-3 gap-4 py-4 place-items-center">
                                    {
                                        dataImageHeathyWorkshop.map((urlImage, index) => (
                                            <div key={index} className="flex justify-center items-center bg-white h-16 rounded-md relative">
                                                <Image loading='lazy' width={130} height={64} alt="Image một số đối tác tiêu biểu" src={urlImage.image} />
                                            </div>
                                        ))
                                    }
                                </div>
                            </div>
                            <section className='block md:hidden'>
                                <h2 className="text-center font-bold md:text-2xl text-lg uppercase mt-6 text-[#156634]">{locale == "en" ? "Some notable corporate partners" : "Một số đối tác tiêu biểu"}</h2>
                                <div className="grid grid-cols-5 gap-2 py-4 place-items-center">
                                    {
                                        dataImageHeathyWorkshop.map((urlImage, index) => (
                                            <div key={index} className="flex justify-center items-center bg-white h-16 rounded-md relative">
                                                <Image loading='lazy' width={130} height={64} alt="Image Tọa đàm sức khỏe" src={urlImage.image} />
                                            </div>
                                        ))
                                    }
                                </div>
                            </section>
                            <div className="relative p-2 rounded-lg md:w-1/2 flex flex-col items-center">
                                <h2 className="text-center font-bold md:text-2xl text-lg uppercase mb-2 text-[#156634]">{locale === "vi" ? "Thông Tin Chương Trình" : "Program Information"}</h2>
                                <section className='hidden md:block'>
                                    <div className="bg-[#14A54A] text-white px-8 py-2 rounded-full mb-6 flex items-center gap-2">
                                        <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="11.5" cy="12.166" r="11.5" fill="#F0FDF4" />
                                            <g clip-path="url(#clip0_7608_4771)">
                                                <path d="M18.5 10.25C18.4244 10.6736 18.2561 11.0752 18.0071 11.426L12.8837 18.321C12.7212 18.5309 12.5132 18.701 12.2753 18.8186C12.0374 18.9362 11.7759 18.9982 11.5105 18.9998C11.2452 19.0014 10.9829 18.9427 10.7436 18.828C10.5043 18.7134 10.2942 18.5458 10.1292 18.3379L4.98067 11.3C4.76196 10.9831 4.60757 10.6264 4.52625 10.25H8.18258L10.9552 17.4594C10.9974 17.5697 11.072 17.6646 11.1693 17.7316C11.2666 17.7985 11.3819 17.8343 11.5 17.8343C11.6181 17.8343 11.7334 17.7985 11.8307 17.7316C11.928 17.6646 12.0026 17.5697 12.0448 17.4594L14.8174 10.25H18.5ZM14.825 9.08333H18.4749C18.3862 8.68866 18.2159 8.3169 17.975 7.99192L16.4828 5.97708C16.2667 5.67507 15.9818 5.4289 15.6516 5.25898C15.3214 5.08906 14.9554 5.00028 14.5841 5H13.3007L14.825 9.08333ZM10.9797 5L9.42858 9.08333H13.5772L12.0571 5H10.9797ZM8.18083 9.08333L9.73133 5H8.37683C8.00862 4.99964 7.64558 5.08664 7.31753 5.25386C6.98948 5.42108 6.70579 5.66374 6.48975 5.96192L5.04717 7.85308C4.77114 8.21477 4.58373 8.63612 4.5 9.08333H8.18083ZM13.5673 10.25H9.43267L11.5 15.6248L13.5673 10.25Z" fill="#14A54A" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_7608_4771">
                                                    <rect width="14" height="14" fill="white" transform="translate(4.5 5)" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                        {locale == "en" ? "Personalize topics for each business" : "Cá nhân hóa chủ để cho từng doanh nghiệp"}
                                    </div>
                                </section>
                                <section className='mt-8 hidden md:block'>
                                    <div className="flex-col justify-start items-start gap-4 grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1">
                                        {dataHealth.map((data) => (
                                            <div key={data.id} className="flex flex-row p-4 items-center justify-start gap-4 bg-white min-h-full">
                                                <div className="flex-shrink-0">
                                                    <img src={data.image} alt="" className='object-cover h-full' />
                                                </div>
                                                <div className="flex flex-col items-start justify-center">
                                                    <h4 className="text-base font-medium text-left">{data.title}</h4>
                                                    <p className="text-sm text-left">{data.desc}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="relative block md:hidden">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="w-full flex-col md:items-center inline-flex">
                        <div className="bg-[#14A54A] text-white px-8 py-2 rounded-full mb-6 flex items-center gap-2 text-sm">
                            <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="11.5" cy="12.166" r="11.5" fill="#F0FDF4" />
                                <g clip-path="url(#clip0_7608_4771)">
                                    <path d="M18.5 10.25C18.4244 10.6736 18.2561 11.0752 18.0071 11.426L12.8837 18.321C12.7212 18.5309 12.5132 18.701 12.2753 18.8186C12.0374 18.9362 11.7759 18.9982 11.5105 18.9998C11.2452 19.0014 10.9829 18.9427 10.7436 18.828C10.5043 18.7134 10.2942 18.5458 10.1292 18.3379L4.98067 11.3C4.76196 10.9831 4.60757 10.6264 4.52625 10.25H8.18258L10.9552 17.4594C10.9974 17.5697 11.072 17.6646 11.1693 17.7316C11.2666 17.7985 11.3819 17.8343 11.5 17.8343C11.6181 17.8343 11.7334 17.7985 11.8307 17.7316C11.928 17.6646 12.0026 17.5697 12.0448 17.4594L14.8174 10.25H18.5ZM14.825 9.08333H18.4749C18.3862 8.68866 18.2159 8.3169 17.975 7.99192L16.4828 5.97708C16.2667 5.67507 15.9818 5.4289 15.6516 5.25898C15.3214 5.08906 14.9554 5.00028 14.5841 5H13.3007L14.825 9.08333ZM10.9797 5L9.42858 9.08333H13.5772L12.0571 5H10.9797ZM8.18083 9.08333L9.73133 5H8.37683C8.00862 4.99964 7.64558 5.08664 7.31753 5.25386C6.98948 5.42108 6.70579 5.66374 6.48975 5.96192L5.04717 7.85308C4.77114 8.21477 4.58373 8.63612 4.5 9.08333H8.18083ZM13.5673 10.25H9.43267L11.5 15.6248L13.5673 10.25Z" fill="#14A54A" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_7608_4771">
                                        <rect width="14" height="14" fill="white" transform="translate(4.5 5)" />
                                    </clipPath>
                                </defs>
                            </svg>
                            {locale == "en" ? "Personalize topics for each business" : "Cá nhân hóa chủ đề cho từng doanh nghiệp"}
                        </div>
                        <HealthTalksMainCore />
                    </div>
                </div>
            </section>
            <HealthSlider />
            <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto pt-12">
                <h2 className="font-bold md:text-2xl text-lg text-[#156634] uppercase text-center">{locale === "en" ? "Health Talk Topics" : "Chủ đề buổi tọa đàm"}</h2>
                <p className="text-sm md:text-base mt-2 pb-4 text-justify md:text-center px-4">
                    {locale === "en" ? "Corporates can select topics from a pre-made list or customize them flexibly according to their needs and goals, ensuring relevance and practical value." : "Các chủ đề có thể được lựa chọn từ danh sách có sẵn hoặc tùy chỉnh linh hoạt theo nhu cầu và mục tiêu của doanh nghiệp, đảm bảo phù hợp và mang lại giá trị thiết thực cho người tham gia."}
                </p>
                <section className='hidden md:block pt-8'>
                    <div className='flex items-start md:gap-12 gap-6 flex-col md:flex-row'>
                        <div className="md:w-1/2 w-full">
                            <FlipCard
                                frontImage="https://api.echomedi.com/uploads/Suc_Khoe_The_Chat_5e3ad6b599.png"
                                title="Sức Khỏe Thể Chất"
                                title_en="Physical Health"
                                desc="Sức khỏe thể chất là yếu tố quan trọng góp phần nâng cao năng suất lao động, giảm thiểu tình trạng nghỉ phép do vấn đề sức khỏe và duy trì tính liên tục trong công việc. Từ đó hỗ trợ sự phát triển bền vững của doanh nghiệp."
                                desc_en="Physical health plays a crucial role in enhancing labor productivity, decreasing absenteeism due to health issues, and ensuring workplace continuity. This, in turn, supports the sustainable development of the business."
                                locale={locale}
                                onContactClick={handleShowModal}
                            />
                        </div>
                        <div className="md:w-1/2 w-full">
                            <FlipCard
                                frontImage="https://api.echomedi.com/uploads/Suc_Khoe_Tinh_Than_6ee49d248c.png"
                                title="Sức khỏe tinh thần"
                                title_en="Psychological health"
                                desc="Sức khoẻ tinh thần là yếu tố quan trọng đóng trong việc gắn kết nhân viên với môi trường làm việc. Sự quan tâm và chăm sóc đúng mức đến sức khỏe tinh thần không chỉ nâng cao khả năng giải quyết vấn đề và hiệu suất làm việc, mà còn xây dựng một môi trường làm việc tích cực và sáng tạo."
                                desc_en="Psychological health is a crucial factor in connecting employees to the work environment. Proper attention and care for mental health not only improve problem-solving and work performance but also foster a positive and creative work atmosphere."
                                locale={locale}
                                saleImage={locale === "en" ? "https://api.echomedi.com/uploads/Pho_bien_7d2f939850.png" : "https://api.echomedi.com/uploads/Pho_bien_English_8e739b62ec.png"}
                                onContactClick={handleShowModal}
                            />
                        </div>
                    </div>
                </section>
                <section className='block md:hidden space-y-4 pt-8'>
                    <div className="w-full h-full bg-white p-8 flex flex-col justify-between rounded-xl">
                        <div>
                            <h2 className="text-xl font-bold mb-4">{locale === "en" ? "Physical Health" : "Sức khỏe thể chất"}</h2>
                            <p className="text-sm mb-6">{locale === "en" ? "Physical health plays a crucial role in enhancing labor productivity, decreasing absenteeism due to health issues, and ensuring workplace continuity. This, in turn, supports the sustainable development of the business." : "Sức khỏe thể chất là yếu tố quan trọng góp phần nâng cao năng suất lao động, giảm thiểu tình trạng nghỉ phép do vấn đề sức khỏe và duy trì tính liên tục trong công việc. Từ đó hỗ trợ sự phát triển bền vững của doanh nghiệp."}</p>
                        </div>
                        <div className="py-2 w-52 text-center text-base inline-block text-white border bg-[#14813d] hover:bg-[#14813d] rounded-full hover:text-white font-medium">
                            <a href="tel:**********">{locale === "en" ? "Contact to choose topic" : "Liên hệ chọn chủ đề"}</a>
                        </div>
                    </div>
                    <div className="w-full h-full bg-white p-8 flex flex-col justify-between rounded-xl">
                        <div>
                            <h2 className="text-xl font-bold mb-4">{locale === "en" ? "Psychological health" : "Sức Khỏe Tinh Thần"}</h2>
                            <p className="text-sm mb-6">{locale === "en" ? "Psychological health is a crucial factor in connecting employees to the work environment. Proper attention and care for mental health not only improve problem-solving and work performance but also foster a positive and creative work atmosphere." : "Sức khoẻ tinh thần là yếu tố quan trọng đóng trong việc gắn kết nhân viên với môi trường làm việc. Sự quan tâm và chăm sóc đúng mức đến sức khỏe tinh thần không chỉ nâng cao khả năng giải quyết vấn đề và hiệu suất làm việc, mà còn xây dựng một môi trường làm việc tích cực và sáng tạo."}</p>
                        </div>
                        <div className="py-2 w-52 text-center text-base inline text-[#166534] border border-[#14813d] hover:bg-[#14813d] rounded-full hover:text-white font-medium">
                            <a href="tel:**********">{locale === "en" ? "Contact to choose topic" : "Liên hệ chọn chủ đề"}</a>
                        </div>
                    </div>
                </section>
            </div>
            <section className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto  mt-16">
                <h2 className="font-bold md:text-2xl text-lg text-[#156634] uppercase my-8 text-center">{locale === "en" ? "Conducted Health Talks" : "Các chương trình tọa đàm đã thực hiện"}</h2>
                <section className='pb-16'>
                    <ImageSlider />
                </section>
            </section>
            <section className="relative pb-12">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="mx-auto">
                        {isMobile ? (
                            <div className="relative">
                                <img
                                    src="https://api.echomedi.com/uploads/contact_company_83d7196677.png"
                                    alt="Background Image"
                                    className="w-full h-auto object-cover"
                                />
                                <div className="absolute pt-12 inset-0 flex flex-col items-center space-y-6 p-5 bg-white bg-opacity-75 shadow-lg rounded-2xl">
                                    <h2 className="text-center font-bold text-base text-[#156634]">
                                        {locale === "en" ? "ECHO MEDI focuses on comprehensive healthcare, addressing both physical and mental well-being, with medical treatment being a key component." : "Đầu tư vào chăm sóc sức khỏe trong công ty giúp tạo ra một lực lượng lao động khỏe mạnh, hạnh phúc và làm việc hiệu quả, dẫn đến thành công lâu dài!"}
                                    </h2>
                                    <Image
                                        alt="Image Doanh Nghiêp"
                                        width={1000}
                                        height={400}
                                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1000, 400))}`}
                                        className='object-cover'
                                        src={"https://api.echomedi.com/uploads/contact_company_01_a2176cb15b.png"}
                                    />
                                    <button onClick={() => handleShowModal()} className="font-bold px-6 py-2 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]">
                                        <span className="text-center text-white text-sm font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</span>
                                    </button>
                                </div>
                            </div>

                        ) : (
                            <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                                <div className="w-full relative">
                                    <Image
                                        alt="Image Doanh Nghiêp"
                                        width={1000}
                                        height={400}
                                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1000, 400))}`}
                                        className='object-cover w-full'
                                        src={"https://d3e4m6b6rxmux9.cloudfront.net/banner_doanh_nghiep_ec670ebece.png"}
                                    />
                                </div>
                                <div className="max-w-3xl absolute md:px-16 px-4">
                                    <h2 className="text-justify font-bold text-sm md:text-2xl text-[#156634] pb-2">
                                        {locale === "vi" ? "Đầu tư vào chăm sóc sức khỏe trong công ty giúp tạo ra một lực lượng lao động khỏe mạnh, hạnh phúc và làm việc hiệu quả, dẫn đến thành công lâu dài!" : "ECHO MEDI focuses on comprehensive healthcare, addressing both physical and mental well-being, with medical treatment being a key component."}
                                    </h2>
                                    <button onClick={() => handleShowModal()} className="mt-6 font-bold flex px-6 py-2 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]">
                                        <span className="text-center text-white text-sm font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</span>
                                    </button>
                                </div>
                            </div>
                        )}

                    </div>
                </div>
            </section>
            {showModal && (
                <BookingHealthTalks
                    onClose={() => setShowModal(false)}
                    locale={locale}
                />
            )}
            <Contact />
            <Toaster position="bottom-center" />
        </>
    )
}
export default FamilyDoctorPages;


interface FlipCardProps {
    frontImage: string
    title: string
    title_en: string
    desc: string
    desc_en: string
    locale: string
    saleImage?: string
    onContactClick?: () => void
}

const FlipCard: React.FC<FlipCardProps> = ({
    frontImage,
    title,
    title_en,
    desc,
    desc_en,
    locale,
    saleImage,
    onContactClick,
}) => {
    const [isFlipped, setIsFlipped] = useState(false)

    return (
        <div
            className="flip-card w-full h-[350px] perspective-1000"
            onMouseEnter={() => setIsFlipped(true)}
            onMouseLeave={() => setIsFlipped(false)}
        >
            <div className={`flip-card-inner relative w-full h-full transition-transform duration-1000 transform-style-preserve-3d ${isFlipped ? 'rotate-y-180' : ''}`}>
                <div className="flip-card-front absolute w-full h-full backface-hidden">
                    <div className='relative w-full h-full'>
                        <Image
                            src={frontImage}
                            alt="Banner"
                            layout="fill"
                            objectFit="cover"
                            placeholder="blur"
                            blurDataURL="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjMwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2YwZjBmMCIvPjwvc3ZnPg=="
                        />
                        <h2 className='absolute top-8 left-8 text-2xl font-bold text-white'>{locale === "en" ? title_en : title}</h2>
                        {saleImage && (
                            <Image
                                src={saleImage}
                                alt="Sale Icon"
                                width={65}
                                height={32}
                                className="absolute -top-1 -right-1"
                            />
                        )}
                    </div>
                </div>
                <div className="flip-card-back absolute w-full h-full backface-hidden rotate-y-180 bg-white p-8 flex flex-col justify-between">
                    <div>
                        <h2 className="text-2xl font-bold mb-4">{locale === "en" ? title_en : title}</h2>
                        <p className="text-base mb-6">{locale === "en" ? desc_en : desc}</p>
                    </div>

                    <div className="py-2 w-52 text-center text-base inline-block text-white border bg-[#14813d] hover:bg-[#14813d] rounded-full hover:text-white font-medium md:hidden">
                        <a href="tel:**********">{locale === "en" ? "Free consultation" : "Tư vấn miễn phí"}</a>
                    </div>

                    <button
                        className="py-2 w-52 text-center text-base text-white border bg-[#14813d] hover:bg-[#14813d] rounded-full hover:text-white font-medium hidden md:block"
                        onClick={onContactClick}
                    >
                        {locale === "en" ? "Free consultation" : "Tư vấn miễn phí"}
                    </button>
                </div>
            </div>
        </div>
    )
}

