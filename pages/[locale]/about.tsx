import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import { makeStaticProps } from '../../lib/getStatic';
import Image from 'next/image'
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }
const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Về chúng tôi" : "About Us"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta
          name="description"
          content={locale === "en" ? "ECHO MEDI offers a comprehensive healthcare system that caters to you and your family's needs. Our focus is on enhancing the \"Family Medicine\" model of advanced countries to address the current healthcare crisis in Vietnam." : "ECHO MEDI là hệ thống y tế toàn diện cho bạn và gia đình. Chúng tôi tiên phong tại Việt Nam trong việc nâng cấp mô hình “Bác sĩ gia đình” của các nước tiên tiến trên thế giới. ECHO MEDI tập hợp một đội ngũ các bác sĩ, dược sĩ, điều dưỡng và chuyên gia dinh dưỡng được đào tạo trong và ngoài nước để cùng chăm sóc, theo dõi và nâng cao sức khỏe của mọi khách hàng."}
        />
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section className="mx-auto items-center justify-center flex bg-[#FAFBFD] relative md:py-12">
        <Image loading='lazy' className="absolute z-0 hidden md:block" layout="fill" alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/Image_Bg_About_32ba349241.png" />
        <div className="flex items-center justify-between flex-col md:flex-row gap-10 md:px-16 px-4 max-w-screen-2xl z-10 noselect">
          <div className="md:w-2/5">
            <img className="rounded-lg mt-4" src="https://d3e4m6b6rxmux9.cloudfront.net/trai_tim_b36f7c8b75.png" alt=
              "image" loading="lazy" width="557" height="384" />
          </div>
          <div className="md:w-3/5">
            <h2 className="text-center mb-8 md:text-left font-bold md:text-4xl text-2xl text-[#156634] uppercase">{locale === "vi" ? "VỀ CHÚNG TÔI" : 'ABOUT US'}</h2>
            {/* <p className="md:text-base mb-2 md:mb-4 text-sm text-justify">{locale === "vi" ? "Đội ngũ bao gồm các bác sĩ, dược sĩ, điều dưỡng, chuyên gia dinh dưỡng và chuyên viên tâm lý được đào tạo bài bản tại các cơ sở trong và ngoài nước như Đại học Y Dược TP. HCM, Đại học Y khoa Phạm Ngọc Thạch, Đại học Y Dược Cần Thơ, Barca Innovation Hub, v.v. Chúng tôi luôn đồng hành cùng khách hàng trong suốt hành trình chăm sóc sức khỏe, lắng nghe và thấu hiểu để mang đến các giải pháp tối ưu, giúp tiết kiệm thời gian, chi phí, đồng thời hạn chế việc lạm dụng thuốc và thực hiện các xét nghiệm không cần thiết." : "Our team comprises highly trained professionals, including doctors, pharmacists, nurses, nutritionists, and psychological counselors from esteemed domestic and international institutions, such as Ho Chi Minh City University of Medicine and Pharmacy, Pham Ngoc Thach University of Medicine, Can Tho University of Medicine and Pharmacy, and the Barca Innovation Hub. We are dedicated to accompanying clients throughout their healthcare journey by actively listening to their needs and delivering tailored, optimal solutions that save time and costs, while minimizing unnecessary invasive procedures and preventing medication misuse."}</p> */}
            <p className="md:text-base mb-2 md:mb-4 text-sm text-justify">{locale === "vi" ? "Đội ngũ được đào tạo bài bản tại các cơ sở trong và ngoài nước như Đại học Y Dược TP. HCM, Đại học Y khoa Phạm Ngọc Thạch, Đại học Y Dược Cần Thơ, Barca Innovation Hub, v.v. Chúng tôi luôn đồng hành cùng khách hàng trong suốt hành trình chăm sóc sức khỏe, lắng nghe và thấu hiểu để mang đến các giải pháp tối ưu, giúp tiết kiệm thời gian, chi phí, đồng thời hạn chế việc lạm dụng thuốc và thực hiện các xét nghiệm không cần thiết." : "Our team comprises highly trained professionals from esteemed domestic and international institutions, such as Ho Chi Minh City University of Medicine and Pharmacy, Pham Ngoc Thach University of Medicine, Can Tho University of Medicine and Pharmacy, and the Barca Innovation Hub. We are dedicated to accompanying clients throughout their healthcare journey by actively listening to their needs and delivering tailored, optimal solutions that save time and costs, while minimizing unnecessary invasive procedures and preventing medication misuse."}</p>
            <section>
              <p className="mb-3 md:text-base text-sm text-justify">
                {locale === "en" ? "ECHO MEDI is honored to be chosen and trusted by strategic partners and medical advisors, including:" : "Đồng thời, ECHO MEDI hân hạnh được những đối tác chiến lược và cố vấn y tế lựa chọn và tín nhiệm hợp tác:"}
              </p>
              <p className="md:text-base text-justify text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Bệnh viện Cathay General Hospital - Một trong 10 bệnh viện tốt nhất tại Đài Loan." : "Cathay General Hospital - Ranked among the top 10 hospitals in Taiwan."}
              </p>
              <p className="md:text-base text-justify text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "EC Healthcare - Hệ thống phòng khám lớn nhất và được niêm yết trên Sở giao dịch chứng khoán Hồng Kông. " : "EC Healthcare - The largest clinic network in Hong Kong, publicly listed on the Hong Kong Stock Exchange."}
              </p>
              <p className="md:text-base text-justify text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Bionet Corporation - Tập đoàn hàng đầu của Đài Loan chuyên nghiên cứu và phát triển tế bào gốc, Exosome, ứng dụng Y Học Chính Xác và Y Học Tái Tạo, xét nghiệm trước sinh và sản phẩm chăm sóc da và được niêm yết trên Sở giao dịch chứng khoán Đài Loan. " : "Bionet Corporation - A leading Taiwanese corporation specializing in stem cell research and development, Exosome technology, applications in Precision Medicine and Regenerative Medicine, prenatal testing, and skincare products, publicly listed on the Taiwan Stock Exchange."}
              </p>
              <p className="md:text-base text-justify text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Dr. Lim Cheok Peng - Cố vấn y tế tại ECHO MEDI, là người đặt nền móng cho nền y học hiện đại tại Singapore và cựu Tổng giám đốc điều hành của tập đoàn chăm sóc sức khỏe Parkway Holdings hàng đầu của châu Á." : "Dr. Lim Cheok Peng - Medical Advisor at ECHO MEDI, a pioneer in establishing modern medicine in Singapore, and the former CEO of Parkway Holdings, Asia's leading healthcare group."}
              </p>
            </section>
          </div>
        </div>
      </section>
      <div className="bg-[#FAFBFD] noselect">
        <div className="2xl:container 2xl:mx-auto lg:pb-12 lg:px-20 md:py-12 md:px-6 pb-9 px-2 pt-2">
          <div className="flex flex-col lg:flex-row justify-between gap-8">
            <div className="w-full lg:w-6/12 flex flex-col justify-center text-justify p-2">
              <h1 className="text-center md:text-left font-bold text-xl text-[#156634] my-2">{locale === "en" ? "Healthcare" : "Chăm sóc sức khỏe"}</h1>
              <p className="mb-3 md:text-base text-sm">
                {locale === "en" ? "At ECHO MEDI, we believe that healthcare is a holistic approach to maintaining both physical and mental well-being - not just treating illnesses, which defines traditional \"Sick Care.\" Pioneering in Vietnam, ECHO MEDI introduces the \"Family Doctor\" model, widely adopted in advanced countries, to deliver comprehensive healthcare programs tailored to the needs of every individual and family." : "Chăm sóc sức khỏe (Healthcare) là mô hình chăm sóc toàn diện về thể chất và tinh thần thay vì chỉ tập trung vào điều trị khi có vấn đề về bệnh lý (Sick Care) như thực trạng y tế hiện nay. ECHO MEDI tiên phong tại Việt Nam trong việc nâng cấp mô hình “Bác sĩ gia đình” của các nước tiên tiến trên thế giới nhằm cung cấp những chương trình chăm sóc sức khỏe toàn diện nhất."}
              </p>
              <p className="mb-3 md:text-base text-sm">
                {locale === "en" ? "Our healthcare system is grounded in four essential pillars:" : "Để thực hiện được mục tiêu trên, hệ thống y tế ECHO MEDI xây dựng các dịch vụ chăm sóc sức khỏe dựa trên bốn nền tảng:"}
              </p>
              <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Chăm Sóc Phòng Ngừa" : "Preventive Care"}
              </p>
              <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Điều Trị Ban Đầu" : "Primary Care"}
              </p>
              <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Quản Lý Bệnh Mạn Tính" : "Chronic Disease Management"}
              </p>
              {/* <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
                {locale === "vi" ? "Sức Khỏe Toàn Diện" : "Wellness"}
              </p> */}
            </div>
            <div className="w-full lg:w-6/12">
              <img className="w-full rounded-lg" src="https://d3e4m6b6rxmux9.cloudfront.net/About_acd472532e.png" alt="office content 1" />
            </div>
          </div>
        </div>
      </div>
      <div className="2xl:container 2xl:mx-auto lg:pb-12 lg:px-20 md:py-4 md:px-6 pb-9 px-4 pt-2 noselect">
        <h2 className="text-center mt-4 md:mt-0 font-bold md:text-4xl text-2xl text-[#156634] uppercase">{locale === "vi" ? "TẠI SAO CHỌN ECHO MEDI?" : 'ABOUT US'}</h2>
        <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] mt-8 mb-4 bg-white">
          <div className="flex items-center justify-center">
            <Image loading='lazy' width={60} height={60} alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/about_1_8b084971a3.png" />
          </div>
          <h1 className="text-center font-bold text-xl text-[#156634] my-2">{locale === "en" ? "Prioritize comprehensive healthcare" : "Tập trung vào chăm sóc sức khỏe toàn diện"}</h1>
          <p className="md:text-base text-sm text-justify">{locale === "en" ? "ECHO MEDI stands apart from conventional medical approaches that focus solely on treating illnesses. We prioritize comprehensive healthcare, emphasizing screening, prevention, and primary care. By advancing the “Family Doctor” model used in many developed countries, ECHO MEDI delivers personalized physical and mental health care plans tailored to each customer. We believe that primary health care, when combined with preventative measures, creates a solid foundation for an effective and comprehensive healthcare system, ensuring timely care and long-term well-being." : "Khác với thực trạng y tế hiện nay là chỉ tập trung vào điều trị bệnh lý, ECHO MEDI tập trung vào chăm sóc sức khỏe toàn diện, từ tầm soát, phòng ngừa, điều trị ban đầu các bệnh lý cục bộ và đưa ra kế hoạch chăm sóc về thể chất và tinh thần cho mỗi khách hàng, thông qua việc nâng cấp mô hình “Bác sĩ gia đình” của các nước tiên tiến trên thế giới. ECHO MEDI tin rằng chăm sóc sức khỏe ban đầu kết hợp với tầm soát và phòng ngừa là nền tảng vững chắc của hệ thống chăm sóc sức khỏe toàn diện, đảm bảo duy trì trạng thái khỏe mạnh và được chăm sóc đúng lúc."}</p>
        </div>
        <div className="flex gap-8 flex-col md:flex-row">
          <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] md:mt-4 bg-white">
            <div className="flex items-center md:justify-start justify-center">
              <Image loading='lazy' width={60} height={60} alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/about_2_c84a26cc1e.png" />
            </div>
            <h1 className="font-bold text-center md:text-left text-xl text-[#156634] my-2">{locale === "en" ? "We have a full supply of treatment medicine and a wide range of products available" : "Cung ứng đầy đủ thuốc điều trị, đa dạng sản phẩm"}</h1>
            <p className="md:text-base text-sm">{locale === "en" ? "ECHO MEDI Pharmacy ensures a full supply of treatment medicine, a wide range of beauty products, and supplements with verified, transparent origins." : "Nhà thuốc ECHO MEDI đảm bảo cung ứng đầy đủ thuốc điều trị, đa dạng sản phẩm làm đẹp, thực phẩm chức năng và chăm sóc sức khỏe toàn diện với nguồn gốc rõ ràng."}</p>
          </div>
          <div className="p-6 rounded-2xl border-t-4 border-t-[#156634] md:mt-4 bg-white">
            <div className="flex items-center md:justify-start justify-center">
              <Image loading='lazy' width={60} height={60} alt="Iamge" src="https://d3e4m6b6rxmux9.cloudfront.net/about_3_b9a0803188.png" />
            </div>
            <h1 className="font-bold text-xl text-[#156634] text-center md:text-left my-2">{locale === "en" ? "We offer comprehensive and personalized consultation to meet your unique needs" : "Tư vấn chuyên sâu và cá nhân hóa"}</h1>
            <p className="md:text-base text-sm">{locale === "en" ? "Our pharmacists provide personalized advice and guidance to ensure customers use medication safely, reasonably, and effectively, tailored to their individual health conditions." : "Các dược sĩ của chúng tôi luôn tư vấn trực tiếp và hướng dẫn khách hàng dùng thuốc an toàn, hợp lý và hiệu quả, đúng với tình trạng sức khỏe của mỗi cá nhân."}</p>
          </div>
        </div>
      </div>
      <Contact />
    </>
  );
};

export default Home;
