import type {
    InferGetStaticPropsType,
} from 'next';
import Head from "next/head";
import Slider from "../../../components/Slider/Slider";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import React, { useEffect, useState } from "react";
import parse from 'html-react-parser';

import { getStaticPathsServicesMembership, getStaticPropsService } from '../../../lib/getStatic';
import { addToCart } from '../../../lib/ui';
export { getStaticPathsServicesMembership as getStaticPaths, getStaticPropsService as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsService>) => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const [logged, setLogged] = useState(false);

    useEffect(() => {
        if (localStorage.getItem("token")) {
            const token = localStorage.getItem("token");
            setLogged(true);

        }
    }, []);

    return (
        <>
            <Head>
                <title>{locale === "en" ? props.en_label : props.label}</title>
                <meta
                    name="description"
                    content={locale == "en" ? (props.en_desc ?? "") : (props.desc ?? "")}
                />
                <meta name="keywords" content="ECHO MEDI"></meta>
                <link rel="icon" href="/favicon1.png" />
            </Head>
            <Slider />
            <main className="noselect pt-8 pb-16 lg:pt-16 lg:pb-24 bg-white">
                <div className="justify-between px-2 mx-auto max-w-screen-xl">
                    <article className="mx-auto w-full max-w-4xl format format-sm sm:format-base lg:format-lg format-blue col-span-2">
                        <nav aria-label="breadcrumb" className="w-full">
                            <ul className="breadcrumb">
                                <li
                                    className="crumb first-crumb">
                                    <a href={locale == "en" ? "/en" : "/vi"} className="opacity-60">{locale == "en" ? ("Home" ?? "") : ("Trang chủ" ?? "")}</a>
                                </li>
                                <li
                                    className="crumb middle-crumb">
                                    <a href={locale == "en" ? "/en/membership" : "/vi/membership"} className="opacity-60">{locale == "en" ? ("Membership" ?? "") : ("Thành viên" ?? "")}</a>
                                    <span className="font-sans text-sm antialiased font-normal leading-normal pointer-events-none select-none text-blue-gray-500"></span>
                                </li>
                            </ul>
                        </nav>
                        <header className="mb-4 lg:mb-6 not-format text-sm leading-6 mt-4">
                            <h1 className="text-center mb-4 text-3xl font-extrabold leading-tight text-gray-900 lg:mb-6 lg:text-2xl uppercase">{locale == "en" ? (props.en_label ?? "") : (props.label ?? "")}</h1>
                            <p className="text-sm italic leading-6">
                                {parse(locale == "en" ? (props.en_desc ?? "") : (props.desc ?? ""))}
                            </p>
                            <hr className="my-4 border-gray-200 sm:mx-auto lg:my-4" />
                            {parse(locale == "en" ? (props.en_detail ?? "") : (props.detail ?? ""))}
                        </header>
                        <br />
                        {props.show_buy_btn && <div className='grid grid-cols-2 gap-4'>
                            <div>
                                <p className='text-sm font-bold'>{numberWithCommas(props.price)} đ</p>
                            </div>
                            <div>
                                {logged && <button

                                    onClick={() => { addToCart(parseInt(props.id), locale) }}
                                ><div style={{
                                    backgroundColor: "#416045",
                                    color: "white",
                                }}
                                    className='inline bg-green-200 p-4 rounded-full sm:ml-5 ml-0 text-black hover:bg-green-700 bg-green-800'>{locale === "en" ? "Add to cart" : "Thêm vào giỏ hàng"}</div></button>}
                            </div>
                        </div>
                        }
                    </article>
                </div>
            </main>
            <Contact />
        </>
    );
};

function numberWithCommas(x: number) {
    return x?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Blog;
