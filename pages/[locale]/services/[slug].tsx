import type { InferGetStaticPropsType } from "next";
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import parse from "html-react-parser";
import LinkComponent from "../../../components/Link";
import {
  getStaticPathsServices,
  getStaticPropsService,
} from "../../../lib/getStatic";
import ModalBooking from "../../../components/BookingService";
import { convertString } from "../../../utils/convertString";
export {
  getStaticPathsServices as getStaticPaths,
  getStaticPropsService as getStaticProps,
};

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsService>) => {
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";
  const fullUrl = `https://echomedi.com${router.asPath}`;
  const [logged, setLogged] = useState(false);
  useEffect(() => {
    if (localStorage.getItem("token")) {
      setLogged(true);
    }
    window.scrollTo({
      top: document.getElementById("scroll")?.offsetTop,
      behavior: "smooth",
    });
  }, [router]);
  const [showModal, setShowModal] = useState(false);
  const [message, setMessage] = useState("");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng đặt lịch khám ở phòng khám về gói" + title)
  };
  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta
          name="description"
          content={locale == "en" ? props.en_desc ?? "" : props.desc ?? ""}
        />
        <meta property="og:type" content="website" />
        <meta property="og:image" content={`https://api.echomedi.com${props?.genetica_image_url}`} />
        <meta name="keywords" content={props.label} />
      </Head>
      <section className='mx-auto max-w-5xl'>
        <div className="md:px-16 px-4">
          <div className="mx-auto text-left noselect my-12">
            <section className="mb-4 lg:mb-6 not-format">
              <h1 className="text-[#416044] special mb-4 text-lg font-bold leading-tight lg:mb-6 lg:text-2xl uppercase whitespace-pre-line sm:whitespace-normal text-center">
                {locale == "en" ? props.en_label ?? "" : props.label ?? ""}
              </h1>
              <div className="text-sm !leading-7 text-justify">
                {parse(locale == "en" ? props.en_desc ?? "" : props.desc ?? "")}
              </div>
              <hr className="my-2 border-gray-200 sm:mx-auto lg:mb-4" />
              <div className="flex gap-5 sm:flex-row flex-col">
                <div className="text-sm !leading-7 text-start">
                  {parse(
                    locale == "en" ? props.en_detail ?? "" : props.detail ?? ""
                  )}
                </div>
                {props?.genetica_image_url && (
                  <img
                    className="m-auto object-cover w-1/3"
                    src={"https://api.echomedi.com" + props?.genetica_image_url}
                  />
                )}
              </div>
            </section>
            <div className="flex items-center justify-center py-4">
              <button onClick={() => handleShowModal(props.label, props.en_label)} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-medium text-white">
                {locale === "en" ? "Booking" : "Đặt lịch"}
              </button>
            </div>
            {props.other_services.filter((os: any) => os.id != props.id)
              .length > 0 && (
                <h4 className="mb-4 font-bold">
                  {locale == "vi" ? "Có thể bạn quan tâm:" : "Related services:"}
                </h4>
              )}
            <div className="flex w-full flex-wrap gap-4">
              {props.other_services.filter((os: any) => os.id != props.id)
                .length > 0 &&
                props?.other_services
                  .filter((os: any) => os.id != props.id)
                  .map((os: any) => (
                    <LinkComponent
                      href={"/services/" + os.slug}
                      locale={""}
                      skipLocaleHandling={false}
                    >
                      <div className="p-2 hover:bg-[#4D6047] hover:text-white text-sm text-gray-900 bg-white border border-gray-200 rounded-lg">
                        {locale === "vi" ? `${convertString(os.label)}` : `${convertString(os.en_label)}`}
                      </div>
                    </LinkComponent>
                  ))}
            </div>
          </div>
        </div>
      </section>
      {showModal && (
        <ModalBooking
          visible={showModal}
          onClose={() => setShowModal(false)}
          currentBlog={currentBlog}
          locale={locale}
        />
      )}
      <Contact />
    </>
  );
};

export default Blog;
