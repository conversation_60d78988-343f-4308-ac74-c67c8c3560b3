import type {
    InferGetStaticPropsType,
} from 'next';
import React from 'react';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import toast from 'react-hot-toast';
import { useState } from 'react';
import axios from 'axios';

import { getStaticPathsPackagesGen, getStaticPropsPackage } from '../../../lib/getStatic';
import LinkComponent from '../../../components/Link';
import parse from 'html-react-parser';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';
export { getStaticPathsPackagesGen as getStaticPaths, getStaticPropsPackage as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsPackage>) => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const [showAll, setShowAll] = useState(false);

    return (
        <>
            <Head>
                <title>{locale === "en" ? props.en_label : props.label}</title>
                <meta
                    name="description"
                    content="ECHO MEDI"
                />
                <meta name="keywords" content="ECHO MEDI"></meta>
                <link rel="icon" href="/favicon1.png" />
            </Head>
            <p className='font-bold text-center text-3xl mt-10 mb-5 px-2'>{locale === "en" ? props.en_label : props.label}</p>
            {props.en_desc && <div className="max-w-[1048px] mx-auto text-left px-10">
                {parse(locale == "en" ? (props.en_desc ?? "") : (props.desc ?? ""))}
            </div>}

            <div className="bg-white">
                <Tabs>
                    <TabList className="lg:flex justify-center shadow-lg max-w-[600px] m-auto mt-10 p-2 box-border border rounded-xl bg-gray-50 hidden">
                        {props.sub_packages?.map((sp: any, id: any) => (
                            <Tab key={id} className="px-[60px] py-2 text-sm font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
                                {locale === "en" ? sp.en_label : sp.label}
                            </Tab>
                        ))}
                    </TabList>
                    <TabList className="flex justify-center shadow-lg max-w-[300px] m-auto mt-10 p-2 box-border border rounded-xl bg-gray-50 lg:hidden">
                        {props.sub_packages?.map((sp: any, id: any) => (
                            <Tab key={id} className="px-[20px] py-2 text-xs font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
                                {locale === "en" ? sp.en_label : sp.label}
                            </Tab>
                        ))}
                    </TabList>

                    {props.sub_packages?.map((sp: any, id: any) => (
                        <TabPanel key={id}>
                            <div className="container mx-auto lg:px-[100px] pt-6 pb-[50px]">
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4">
                                    {sp.services.map((sv: any, index: number) => {
                                        if (!showAll && index > 2) {
                                            return null;
                                        }
                                        return (

                                            <div className="group relative shadow-lg rounded-lg" id={sv.id} key={sv.id}>
                                                <div className="p-6 aspect-h-1 aspect-w-1 w-full overflow-hidden rounded-md bg-white        lg:aspect-none group-hover:opacity-75 lg:h-80">
                                                    <img src={`https://d3e4m6b6rxmux9.cloudfront.net/${sv.genetica_image.hash}${sv.genetica_image.ext}`} alt="Front of men&#039;s Basic Tee in black." className="h-full w-full object-cover object-center lg:h-full lg:w-full rounded-lg" />
                                                </div>
                                                <div className='grid grid-cols-5 px-6'>
                                                    <div className='col-span-2'>
                                                    </div>
                                                    <div className='col-span-1'></div>
                                                    <div className='col-span-1'></div>
                                                    <div className='col-span-1 pt-2'>
                                                        <img src='https://d3e4m6b6rxmux9.cloudfront.net/logo_2_6aa91abdd980eb0030e11f2c049238fc_a2153e7725.png?updated_at=2023-06-20T06:16:22.695Z' />
                                                    </div>
                                                </div>
                                                <div className="p-6">
                                                    <p className='text-xs text-green-800 font-semibold pb-3'>{locale === "en" ? sp.en_label : sp.label}</p>
                                                    <div className='grid grid-cols-5'>
                                                        <h5 className="h-[50px] col-span-3 mb-2 block font-sans text-xl font-semibold leading-snug tracking-normal antialiased">
                                                            {locale === "en" ? sv.en_label : sv.label}
                                                        </h5>
                                                        {/* <p className='col-span-2 mb-2 block font-sans text-sm font-semibold leading-snug tracking-normal text-[#15803D] antialiased text-right'>{locale === "en" ? "Coming soon" : "Sắp triển khai"}</p> */}
                                                        {sv.price && <p className='col-span-2 mb-2 block font-sans text-sm font-semibold leading-snug tracking-normal text-[#15803D] antialiased text-right'>{numberWithCommas(sv.price)} <span className='underline'>đ</span></p>}
                                                    </div>
                                                    <p className='text-xs pb-3 pt-1 text-gray-500'>{locale === "en" ? sv.en_desc : sv.desc}</p>
                                                    <hr />
                                                    {sv.en_specification && sv.specification &&
                                                        <p className="label-product-2 mt-4 block font-sans text-sm font-bold leading-relaxed h-[150px]">
                                                            {locale === "en" ? sv.en_specification[0] : sv.specification[0]}
                                                            <p className='flex font-normal pt-4'>
                                                                <svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <title />
                                                                    <g id="Complete">
                                                                        <g id="tick">
                                                                            <polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke="#166534" strokeLinecap="round" stroke-linejoin="round" strokeWidth="2" />
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                                <span className='pl-1'>
                                                                    {locale === "en" ? sv.en_specification[1] : sv.specification[1]}
                                                                </span>
                                                            </p>
                                                            <p className='flex font-normal'>
                                                                <svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <title />
                                                                    <g id="Complete">
                                                                        <g id="tick">
                                                                            <polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke="#166534" strokeLinecap="round" stroke-linejoin="round" strokeWidth="2" />
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                                <span className='pl-1'>
                                                                    {locale === "en" ? sv.en_specification[2] : sv.specification[2]}
                                                                </span>
                                                            </p>
                                                            <p className='flex font-normal'>
                                                                <svg width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <title />
                                                                    <g id="Complete">
                                                                        <g id="tick">
                                                                            <polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke="#166534" strokeLinecap="round" stroke-linejoin="round" strokeWidth="2" />
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                                <span className='pl-1'>
                                                                    {locale === "en" ? sv.en_specification[3] : sv.specification[3]}
                                                                </span>
                                                            </p>
                                                        </p>
                                                    }
                                                </div>
                                                <div className="p-6 pt-0">
                                                    <LinkComponent href={"/gen_detail/" + sv.slug} skipLocaleHandling={false} locale={""}>
                                                        <button
                                                            className="w-full select-none rounded-lg bg-green-800 py-3 px-6 text-center align-middle font-sans text-sm font-bold text-white shadow-md  transition-all hover:shadow-lg focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none mb-3"
                                                            type="button"
                                                            data-ripple-light="true"
                                                        >
                                                            {locale === "en" ? "View detail" : "Xem chi tiết"}
                                                        </button>
                                                    </LinkComponent>
                                                </div>
                                            </div>
                                        );

                                    })}</div>
                                {sp.services.length > 3 && (
                                    <div className="flex justify-center mt-[70px]">
                                        {showAll ? (
                                            <button
                                                className="text-gray-800 bg-white border border-gray-300 focus:outline-none text-sm px-5 py-2.5 mr-2 mb-2"
                                                onClick={() => setShowAll(false)}
                                            >
                                                <a className='flex'>
                                                    <span>{locale === "en" ? "Show less" : "Ẩn bớt"}</span>
                                                    <svg className="mt-[5px] ml-[8px]" fill="#000000" height="10px" width="10px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                                                        viewBox="0 0 407.436 407.436" xmlSpace="preserve">
                                                        <polygon points="203.718,91.567 0,294.621 21.179,315.869 203.718,133.924 386.258,315.869 407.436,294.621 " />
                                                    </svg>
                                                </a>
                                            </button>
                                        ) : (
                                            <button
                                                className="text-gray-800 bg-white border border-gray-300 focus:outline-none text-sm px-5 py-2.5 mr-2 mb-2"
                                                onClick={() => setShowAll(true)}
                                            >
                                                <a className='flex'>
                                                    <span>{locale === "en" ? "Show all" : "Xem tất cả"}</span>
                                                    <svg className="mt-[5px] ml-[8px]" fill="#000000" height="10px" width="10px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                                                        viewBox="0 0 407.437 407.437" xmlSpace="preserve">
                                                        <polygon points="386.258,91.567 203.718,273.512 21.179,91.567 0,112.815 203.718,315.87 407.437,112.815 " />
                                                    </svg>
                                                </a>
                                            </button>
                                        )}
                                    </div>
                                )}
                            </div>
                        </TabPanel>
                    ))}
                </Tabs>
            </div>
            <Contact />
        </>
    );
};

function numberWithCommas(x: number) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Blog;