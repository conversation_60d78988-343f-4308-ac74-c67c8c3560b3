import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import LinkComponent from '../../components/Link';
import React from 'react';
import useUserData from '../../hooks/useUserData';
import { formatDateHistory } from '../../utils/dateTime';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }


const OrderDetail = () => {
  const router = useRouter()
  const { code } = router.query;
  const locale = router.query.locale as string || 'vi';
  const [data, setData] = useState<any>({ status: "" });
  const [cartLines, setCartLines] = useState([]);
  const [token, setToken] = useState<string | null>(null)
  const [totalPrice, setTotalPrice] = useState(0)
  const [price, setPrice] = useState(0)
  const [originalTotal, setOriginalTotalPrice] = useState(0)

  const { userData } = useUserData(token);
  useEffect(() => {
    const storedToken = localStorage.getItem('token')
    if (storedToken) {
      setToken(storedToken)
    }
    if (code) {
      axios.get('https://api.echomedi.com' + '/api/orders/getOrderDetailByCode/' + code)
        .then(function (response) {
          setCartLines(response.data.order.cart.cart_lines);
          setData(response.data.order);
          processCartData(response.data.order.cart.cart_lines)

        })
        .catch(function (error) {
          if (error.response?.status == 401) {
            toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
            localStorage.removeItem("token");
            window.location.href = '/login';
          }
        });
    }
  }, [code])
  const processCartData = (lines: any[]) => {
    let total = 0
    let discount = 0
    let originalTotal = 0

    lines.forEach((line: any) => {
      const currentPrice = line.product ? line.product.price : parseInt(line.service.price)
      const originalPrice = line.product?.original_price || parseInt(line.service?.original_price) || currentPrice

      total += currentPrice * line.quantity
      if (originalPrice > currentPrice) {
        discount += (originalPrice - currentPrice) * line.quantity
        originalTotal += originalPrice * line.quantity
      } else {
        originalTotal += currentPrice * line.quantity
      }
    })

    setTotalPrice(total)
    setPrice(discount)
    setOriginalTotalPrice(originalTotal)
  }
  const handleStatus = (status: string, locale: string) => {
    switch (status) {
      case "draft":
        return locale === "en"
          ? "Cancelled"
          : "Đã hủy";
      case "canceled":
        return locale === "en"
          ? "Unpaid"
          : "Chưa thanh toán";
      case "done":
        return locale === "en"
          ? "Success"
          : "Thành công";
      default:
        return "";
    }
  };
  const statusColors = {
    draft: 'bg-[#FEFBE8] text-[#C6A811]',
    canceled: 'bg-[#EBFEFF] text-[#0098C9]',
    done: 'bg-[#F0FDF4] text-[#156634]',
  };
  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Giỏ hàng" : "Cart"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className="py-4 px-4 md:px-16">
        <div className="flex items-start md:items-center justify-start md:justify-between flex-col md:flex-row">
          <div className="flex items-center h-10 rounded gap-2">
            <svg
              className="h-3 w-3 font-bold cursor-pointer rotate-90"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              fill="#4B4B4B"
            >
              <path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z" />
            </svg>
            <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
              <p className="text-base text-[#4B4B4B]">{locale === "en" ? "Back" : "Quay lại"}</p>
            </LinkComponent>
          </div>
          <div>
            <h2 className="hidden md:block text-center font-bold md:text-[28px] text-2xl text-[#156634] uppercase">
              {locale === "en" ? "Order Information" : "Thông tin đơn hàng"}
            </h2>
          </div>
          <div></div>
        </div>
      </div>
      <h2 className="block md:hidden text-center font-bold md:text-[28px] text-2xl text-[#156634] uppercase">
        {locale === "en" ? "Order Information" : "Thông tin đơn hàng"}
      </h2>
      {data.length === 0 ? (
        <div className="container mx-auto">
          <div className="p-10">
            <img src="https://d3e4m6b6rxmux9.cloudfront.net/preview_b0649685a8.png?updated_at=2023-07-04T09:54:15.960Z" className='rounded-lg w-[250px] mx-auto' />
            <h1 className="text-xl text-gray-600 text-center font-bold">
              {locale === "en" ? "No items in the shopping cart" : "Không có sản phẩm trong giỏ hàng"}
            </h1>
            <div className="mt-8 flex justify-center">
              <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                <button className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-md hover:bg-green-700 font-bold">{locale == "en" ? "Continue shopping" : "Tiếp tục mua hàng"}</button>
              </LinkComponent>
            </div>
          </div>
        </div>
      ) : (
        <div className="px-4 md:px-16 pb-12">
          <div className="mt-4 flex flex-col xl:flex-row jusitfy-center place-items-stretch w-full xl:space-x-4 space-y-4 md:space-y-4 xl:space-y-0">
            <div className="flex flex-col justify-start items-start w-full">
              <div className="w-full">
                <div className="my-4 md:my-8">
                  <div className="w-full p-4 md:p-5 mx-auto bg-white rounded-xl">
                    <div className='flex items-center justify-between mb-2'>
                      <p className="text-xl text-[#156634] font-bold mb-2">{locale === "en" ? " Recipient" : "Đơn hàng"}</p>
                      <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusColors[data.status as 'draft' | 'canceled' | 'done']}`}>
                        {handleStatus(data.status, locale)}
                      </span>
                    </div>
                    <div className="flex justify-between w-full">
                      <p className="text-base">{locale === "en" ? "Total price" : "Mã đơn hàng"}</p>
                      <p className="text-base font-semibold">{data.code}</p>
                    </div>
                    <div className="flex justify-between w-full">
                      <p className="text-base">{locale === "en" ? "Total price" : "Ngày đặt"}</p>
                      <p className="text-base font-semibold">{formatDateHistory(data.createdAt, "DD/MM/YYYY")}</p>
                    </div>
                    <div className='border-gray-200 border-b border my-4'></div>
                    <div className="flex justify-between w-full">
                      <p className="text-base">{locale === "en" ? "Total price" : "Họ và tên"}</p>
                      <p className="text-base font-semibold">{userData?.patient?.full_name}</p>

                    </div>
                    <div className="flex justify-between w-full">
                      <p className="text-base">{locale === "en" ? "Total price" : "Số điện thoại"}</p>
                      <p className="text-base font-semibold">{userData?.phone}</p>
                    </div>
                  </div>
                </div>
                <div className="overflow-x-auto rounded-xl  bg-white p-5">
                  <p className="text-xl text-[#156634] font-bold mb-2">{locale === "en" ? "Products" : "Sản phẩm"}</p>
                  <div className="hidden md:flex pt-4 font-bold text-base border-t-2 text-[#8E8E8E]">
                    <div className="w-[30%] text-base">{locale === "en" ? "Product" : "Sản phẩm"}</div>
                    <div className="w-[20%] text-center text-base">{locale === "en" ? "Price" : "Giá tiền"}</div>
                    <div className="w-[30%] text-center text-base">{locale === "en" ? "Quantity" : "Số lượng"}</div>
                    <div className="w-[20%] text-center text-base">{locale === "en" ? "Total" : "Thành tiền"}</div>
                  </div>
                  {cartLines.map((line: any, index: number) =>
                    <>
                      <div className="hidden md:flex py-5 items-center" key={index}>
                        <div className="w-[30%] flex items-center">
                          <p className="text-base font-medium ">
                            {locale === "en" ? (line.product ? line.product.en_label : line.service.en_label) : (line.product ? line.product.label : line.service.label)}</p>
                          {/* {line.service?.original_price &&
                            <span className="bg-[#FBE7E9] rounded-md p-1 ml-4 text-xs font-bold text-[#AF0E18]">-50%</span>
                          } */}
                        </div>
                        <div className="w-[20%] text-center text-[#156634] text-base font-medium flex flex-col">
                          <span className="text-[#156634]">
                            {numberWithCommas((line.product ? line.product.price : line.service.price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                          </span>
                          {line.service?.original_price &&
                            <span className="line-through text-[#8E8E8E]">
                              {numberWithCommas((line.service?.original_price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                            </span>
                          }
                        </div>
                        <div className="w-[30%] flex justify-center buttons_added items-center">
                          <p>{line.quantity}</p>
                        </div>
                        <div className="w-[20%] justify-center flex text-[#156634] font-medium">
                          {numberWithCommas((line.product ? line.product.price : line.service.price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                        </div>
                      </div>
                      <div className="md:hidden py-5" key={index}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <p className="text-base font-medium">
                              {locale === "en" ? (line.product ? line.product.en_label : line.service.en_label) : (line.product ? line.product.label : line.service.label)}
                            </p>
                            {line.service?.original_price &&
                              <span className="bg-[#FBE7E9] rounded-md p-1 ml-4 text-xs font-bold text-[#AF0E18]">-50%</span>
                            }
                          </div>
                        </div>
                        <div className="flex items-center justify-between space-y-2">
                          <div className="text-center mt-[6px] text-[#156634] text-base font-medium flex flex-col">
                            <span className="text-[#156634]">
                              {numberWithCommas((line.product ? line.product.price : line.service.price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                            </span>
                            {line.service?.original_price &&
                              <span className="line-through text-[#8E8E8E]">
                                {numberWithCommas((line.service?.original_price) * line.quantity)} {locale === "en" ? " VND" : "VND"}
                              </span>
                            }
                          </div>
                          <div className="flex justify-center buttons_added items-center">
                            <p>{line.quantity}</p>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
            <div className="gap-8 flex flex-col">
              <div className="bg-white rounded-xl w-full xl:w-96 flex justify-between items-center md:items-start px-5 py-6 flex-col">
                <div className="flex flex-col xl:flex-col justify-start items-stretch h-full w-full md:space-x-6 lg:space-x-8 xl:space-x-0">
                  <div className="flex justify-center items-center w-full space-y-4 flex-col">
                    <div className="flex justify-between items-center w-full border-gray-200 border-b pb-3">
                      <p className="text-xl text-[#156634] font-bold">{locale === "en" ? " Payment" : "Thanh toán"}</p>
                    </div>
                    <div className="flex justify-between w-full">
                      <p className="text-base">{locale === "en" ? "Total price" : "Tổng tiền"}</p>
                      <p className="text-base font-semibold">
                        {numberWithCommas(originalTotal)}  {locale === "en" ? " VND" : "VND"}
                      </p>
                    </div>
                    <div className="flex justify-between items-center w-full border-gray-200 border-b pb-4">
                      <p className="text-base">{locale === "en" ? "Discount" : "Giảm giá"}</p>
                      <p className="text-base text-[#E98E39] font-semibold">
                        - {numberWithCommas(price)}  {locale === "en" ? " VND" : "VND"}
                      </p>
                    </div>
                    <div className="flex justify-between items-center w-full">
                      <p className="text-xl font-semibold">{locale === "en" ? "Provisional" : "Tạm tính"}</p>
                      <p className="text-xl font-semibold text-[#156634]">
                        {numberWithCommas(totalPrice)} {locale === "en" ? " VND" : "VND"}
                      </p>
                    </div>
                    <div className="flex justify-between items-center w-full">
                      <p className="text-base text-[#156634] font-bold">{locale === "en" ? "Payment method" : "Phương thức thanh toán"}</p>
                    </div>
                    {
                      data.paymentMethod === "vnpay" ? (
                        <div className="flex items-center w-full gap-4">
                          <div className="flex items-center">
                            <img src="https://d3e4m6b6rxmux9.cloudfront.net/vnpay_10b151a67e.png" className="object-cover border border-[#CACACA] rounded h-12" />
                          </div>
                          <label htmlFor="vnpay" className="text-start cursor-pointer text-[#1F1F1F] text-base">
                            {locale === "en" ? "Pay with VNPAY" : "Thanh toán bằng VNPAY"}
                          </label>
                        </div>
                      ) : (
                        <div className="flex items-center w-full gap-4">
                          <div className="flex items-center">

                            <img src="https://d3e4m6b6rxmux9.cloudfront.net/momo_cd67e6e6d8.png" className="object-cover border border-[#CACACA] rounded h-12" />
                          </div>
                          <label htmlFor="momo" className="text-start cursor-pointer text-[#1F1F1F] text-base">
                            {locale === "en" ? "Pay with MOMO e-wallet" : "Thanh toán bằng ví điện tử MOMO"}
                          </label>
                        </div>
                      )
                    }
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="lg:block hidden"><Contact /></div>
    </>
  )
}

export default OrderDetail

export function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

function getDisplayedStatus(x: string) {
  switch (x) {
    case "canceled":
      return "Chưa thanh toán";
    case "done":
      return "Đã thanh toán";
    case "draft":
      return "Chưa thanh toán";
    case "ordered":
      return "Đã thanh toán";
  }
}

