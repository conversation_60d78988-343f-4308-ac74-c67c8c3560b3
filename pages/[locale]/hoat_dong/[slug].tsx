import type {
    InferGetStaticPropsType,
} from 'next';
import Contact from "../../../components/Contact/Contact";
import MetaComponent from "../../../components/components/MetaComponent";
import { useRouter } from 'next/router'
import React from "react";
import parse from 'html-react-parser';
import { getStaticPathsActivities, getStaticPropsActivity } from '../../../lib/getStatic';
import ShareButtons from '../../../components/Share';
import { convertString } from '../../../utils/convertString';
export { getStaticPathsActivities as getStaticPaths, getStaticPropsActivity as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsActivity>) => {
    console.log("Props", props)
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const imageUrl = `https://d3e4m6b6rxmux9.cloudfront.net/${props.cover.split('/').pop()}`
    const shareUrl = `https://echomedi.com/${locale}/hoat_dong/${props.slug}`
    return (
        <>
            <MetaComponent
                title={props.title}
                description={props.description}
                keywords={props.description}
                ogImage={imageUrl}
                url={shareUrl}
                type="website"
            />
            <main className="pb-16 lg:pb-24 bg-white noselect px-4 pt-12">
                <div className="justify-between mx-auto max-w-screen-2xl">
                    <article className="flex flex-col mx-auto w-full max-w-2xl format format-sm sm:format-base lg:format-lg format-blue col-span-2">
                        <h1 className='text-center text-xl special mb-0'>{convertString(props.title)}</h1>
                        {parse(props.content)}
                    </article>
                    <p className='text-center mt-4'>{locale === "en" ? "Please share with us" : "Hãy chia sẻ cùng chúng tôi"}</p>
                    <ShareButtons shareUrl={shareUrl} />
                </div>
            </main>
            <Contact />
        </>
    );
};

export default Blog;
