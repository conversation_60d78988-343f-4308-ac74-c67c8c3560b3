import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import LinkComponent from '../../components/Link';
import { makeStaticProps } from '../../lib/getStatic';
import useToken from '../../hooks/useToken';
import useUserData from '../../hooks/useUserData';
import MenuSlider from '../../components/LayoutUser/LayoutUser';
import { menuItems } from './personal_information';
import React from 'react';
import { formatDateHistory } from '../../utils/dateTime';
import { numberWithCommas } from './order_success';

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }
interface Order {
  code: string;
  contactAddress: string | null;
  contactEmail: string | null;
  contactPhoneNumber: string | null;
  contactReceiver: string | null;
  createdAt: string;
  dynamicLink: string | null;
  id: number;
  num_of_prod: number;
  orderedDate: string | null;
  paymentMethod: string;
  products: any | null;
  promotion: any | null;
  publishedAt: string;
  reference_id: string;
  shippingFee: any | null;
  status: string;
  subTotal: any | null;
  tax: any | null;
  total: string;
  updatedAt: string;
  vnp_payload: any | null;
  vnp_payment_url_params: any | null;
}

const OrderHistory = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const { token } = useToken();
  const { userData } = useUserData(token);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(true);
    axios.get('https://api.echomedi.com' + '/api/orders/getOrderHistory', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
      .then(function (response) {
        const filteredOrders = response.data.orders?.filter((o: any) => o.code)
        const sortedOrders = filteredOrders?.sort((a: any, b: any) => b.id - a.id);
        setOrders(sortedOrders);
        setLoading(false);
      })
      .catch(function (error) {
        setLoading(false);
      });
  }, [token]);

  const statusColors = {
    draft: 'bg-[#FEFBE8] text-[#C6A811]',
    canceled: 'bg-[#EBFEFF] text-[#0098C9]',
    done: 'bg-[#F0FDF4] text-[#156634]',
  };
  const handleMethodsPayment = (methods: string) => {
    switch (methods) {
      case "momo":
        return "Momo"
      case "vnpay":
        return "Vnpay"
      default:
        return "";
    }
  };

  const handleStatus = (status: string, locale: string) => {
    switch (status) {
      case "draft":
        return locale === "en"
          ? "Cancelled"
          : "Đã hủy";
      case "canceled":
        return locale === "en"
          ? "Unpaid"
          : "Chưa thanh toán";
      case "done":
        return locale === "en"
          ? "Success"
          : "Thành công";
      default:
        return "";
    }
  };
  return (
    <>
      <Head>
        <title>Order History - ECHO MEDI</title>
        <meta
          name="description"
          content="Review your past orders at ECHO MEDI. Check the status, reorder products, or view details of previous purchases."
        />
        <meta name="keywords" content="ECHO MEDI, order history, past orders, purchase details, reorder, medical products" />
        <meta property="og:title" content="Order History - ECHO MEDI" />
        <meta
          property="og:description"
          content="Access your ECHO MEDI order history and manage your previous purchases easily."
        />
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className="bg-[#FAFBFD] 2xl:container 2xl:mx-auto lg:px-20 my-4 md:px-6 px-2">
        <section>
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="inline-flex items-center -space-x-1 md:space-x-1">
              <li className="inline-flex items-center">
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Home" : "Trang chủ"}
                  </span>
                </LinkComponent>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                  </svg>
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] mt-1 md:mt-0 text-[8px] md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Order History" : "Lịch sử mua hàng"}
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </section>
        <div className="flex md:flex-row flex-col gap-8 mt-4">
          <div className="w-64 md:h-2/3 bg-white shadow-md rounded-2xl hidden md:block">
            <div className="p-4 flex flex-col items-center justify-center">
              <svg width="111" height="111" viewBox="0 0 111 111" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M110.083 55.4993C110.083 85.6449 85.6449 110.083 55.4993 110.083C25.3538 110.083 0.916016 85.6449 0.916016 55.4993C0.916016 25.3538 25.3538 0.916016 55.4993 0.916016C85.6449 0.916016 110.083 25.3538 110.083 55.4993ZM71.8743 39.1243C71.8743 48.168 64.543 55.4993 55.4993 55.4993C46.4557 55.4993 39.1243 48.168 39.1243 39.1243C39.1243 30.0807 46.4557 22.7493 55.4993 22.7493C64.543 22.7493 71.8743 30.0807 71.8743 39.1243ZM55.4993 101.895C65.2372 101.895 74.2744 98.8951 81.7371 93.7688C85.0332 91.5047 86.4418 87.1917 84.5255 83.682C80.5528 76.4061 72.3669 71.8743 55.499 71.8743C38.6314 71.8743 30.4454 76.406 26.4727 83.6816C24.5563 87.1913 25.9649 91.5043 29.2609 93.7685C36.7237 98.895 45.7611 101.895 55.4993 101.895Z" fill="#14813D" />
              </svg>
              <p className='mt-4'>{userData?.patient?.full_name}</p>
              <p>{userData?.phone}</p>
            </div>
            <MenuSlider menuItems={menuItems} defaultActiveId="purchases" />
          </div>
          <div className="flex-1">
            <div>
              <h1 className="text-xl font-bold text-[#156634]">{locale === "en" ? "Order History" : "Lịch sử mua hàng"} ({orders.length})</h1>
              <div className='border-b-[1px] mb-4 mt-2'></div>
              {loading ? (
                <div className='h-[30vh] px-4 md:px-8 w-full max-w-7xl mx-auto'>
                  <p className="mt-16 h-4 bg-gray-200 rounded-full animate-pulse max-w-xs"></p>
                  <ul className="mt-4 space-y-3">
                    <li className="w-full h-4 bg-gray-200 rounded-full dark:bg-neutral-700"></li>
                    <li className="w-full h-4 bg-gray-200 rounded-full dark:bg-neutral-700"></li>
                    <li className="w-full h-4 bg-gray-200 rounded-full dark:bg-neutral-700"></li>
                    <li className="w-full h-4 bg-gray-200 rounded-full dark:bg-neutral-700"></li>
                  </ul>
                </div>
              ) : (
                orders.length > 0 ? (
                  <>
                    {orders.map((line: any) => (
                      <div className="bg-white rounded-lg p-6 mb-4">
                        <div className="flex justify-between items-start mb-4">
                          <h3 className="text-lg font-semibold">{line.code}</h3>
                          <section className='flex items-center gap-2'>
                            <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusColors[line.status as 'draft' | 'canceled' | 'done']}`}>
                              {handleStatus(line.status, locale)}
                            </span>
                            <LinkComponent skipLocaleHandling={undefined} locale={undefined} href={`/order_success/?code=${line.code}`}>
                              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1.5 10.5L10.5 1.5M10.5 1.5L3.75 1.5M10.5 1.5L10.5 8.25" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                              </svg>
                            </LinkComponent>
                          </section>
                        </div>
                        <div className='border-b-[1px] my-4'></div>
                        <div className="mt-4 text-right flex items-center justify-between">
                          <p className="text-base font-medium">
                            {locale === "en" ? "Total" : "Thành tiền"}
                          </p>
                          <p className="text-[#156634] text-base font-medium">
                            {numberWithCommas(line.total)} VND
                          </p>
                        </div>
                        <div className='border-b-[1px] my-4'></div>
                        <div className="mt-4 flex items-center justify-between">
                          <div className='flex items-center gap-4'>
                            <p className="text-[#8E8E8E] text-sm italic">
                              {locale === "en" ? "Created Date" : "Ngày tạo"}: {formatDateHistory(line.createdAt, "DD/MM/YYYY")}
                            </p>
                            <p className='text-[#8E8E8E] text-sm'>
                              {locale === "en" ? "Pay with" : "Thanh toán bằng ví"} {handleMethodsPayment(line.paymentMethod)}
                            </p>
                          </div>
                          <a href="tel:1900638408" className="text-[#156634] text-sm italic underline">
                            {locale === "en" ? "Contact" : "Liên hệ hỗ trợ"}: 1900 638 408
                          </a>
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="container mx-auto">
                    <div className="p-10">
                      <svg className="mx-auto" xmlns="http://www.w3.org/2000/svg" width="168" height="164" viewBox="0 0 168 164" fill="none">
                        <g filter="url(#filter0_d_14133_736)">
                          <path d="M3.99988 81.0083C3.99988 36.7097 39.9078 1 84.0081 1C128.042 1 164 36.6932 164 81.0083C164 99.8046 157.525 117.098 146.657 130.741C131.676 149.653 108.784 161 84.0081 161C59.0675 161 36.3071 149.57 21.3427 130.741C10.4745 117.098 3.99988 99.8046 3.99988 81.0083Z" fill="white" />
                        </g>
                        <path d="M145.544 77.4619H146.044V76.9619V48.9851C146.044 43.424 141.543 38.9227 135.982 38.9227H67.9223C64.839 38.9227 61.9759 37.3578 60.3174 34.7606L60.3159 34.7583L56.8477 29.3908L56.8472 29.3901C54.9884 26.5237 51.8086 24.7856 48.3848 24.7856H26.4195C20.8584 24.7856 16.3571 29.287 16.3571 34.848V76.9619V77.4619H16.8571H145.544Z" fill="white" stroke="black" />
                        <path d="M63.9999 26.2856C63.9999 25.7334 64.4476 25.2856 64.9999 25.2856H141.428C143.638 25.2856 145.428 27.0765 145.428 29.2856V33.8571H67.9999C65.7907 33.8571 63.9999 32.0662 63.9999 29.8571V26.2856Z" fill="black" />
                        <ellipse cx="1.42857" cy="1.42857" rx="1.42857" ry="1.42857" transform="matrix(-1 0 0 1 46.8571 31)" fill="black" />
                        <ellipse cx="1.42857" cy="1.42857" rx="1.42857" ry="1.42857" transform="matrix(-1 0 0 1 38.2859 31)" fill="black" />
                        <ellipse cx="1.42857" cy="1.42857" rx="1.42857" ry="1.42857" transform="matrix(-1 0 0 1 29.7141 31)" fill="black" />
                        <path d="M148.321 126.907L148.321 126.906L160.559 76.3043C162.7 67.5161 156.036 59.0715 147.01 59.0715H14.5902C5.56258 59.0715 -1.08326 67.5168 1.04059 76.3034L1.04064 76.3036L13.2949 126.906C14.9181 133.621 20.9323 138.354 27.8354 138.354H133.764C140.685 138.354 146.681 133.621 148.321 126.907Z" fill="white" stroke="black" />
                        <path d="M86.3858 109.572C85.2055 109.572 84.2268 108.593 84.2268 107.384C84.2268 102.547 76.9147 102.547 76.9147 107.384C76.9147 108.593 75.9359 109.572 74.7269 109.572C73.5466 109.572 72.5678 108.593 72.5678 107.384C72.5678 96.7899 88.5737 96.8186 88.5737 107.384C88.5737 108.593 87.5949 109.572 86.3858 109.572Z" fill="black" />
                        <path d="M104.954 91.0616H95.9144C94.7053 91.0616 93.7265 90.0829 93.7265 88.8738C93.7265 87.6935 94.7053 86.7147 95.9144 86.7147H104.954C106.163 86.7147 107.141 87.6935 107.141 88.8738C107.141 90.0829 106.163 91.0616 104.954 91.0616Z" fill="black" />
                        <path d="M65.227 91.0613H56.1877C54.9787 91.0613 53.9999 90.0825 53.9999 88.8734C53.9999 87.6931 54.9787 86.7144 56.1877 86.7144H65.227C66.4073 86.7144 67.3861 87.6931 67.3861 88.8734C67.3861 90.0825 66.4073 91.0613 65.227 91.0613Z" fill="black" />
                        <circle cx="142.572" cy="121" r="24.7857" fill="white" stroke="black" />
                        <path d="M152.214 130.643L149.535 127.964M150.071 119.928C150.071 115.195 146.234 111.357 141.5 111.357C136.766 111.357 132.928 115.195 132.928 119.928C132.928 124.662 136.766 128.5 141.5 128.5C143.858 128.5 145.993 127.548 147.543 126.007C149.104 124.455 150.071 122.305 150.071 119.928Z" stroke="black" stroke-width="1.6" stroke-linecap="round" />
                        <defs>
                          <filter id="filter0_d_14133_736" x="1.99988" y="0" width="164" height="164" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                            <feOffset dy="1" />
                            <feGaussianBlur stdDeviation="1" />
                            <feComposite in2="hardAlpha" operator="out" />
                            <feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0" />
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_14133_736" />
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_14133_736" result="shape" />
                          </filter>
                        </defs>
                      </svg>
                      <h1 className="text-xl text-gray-600 text-center font-bold">
                        {locale === "en" ? "No information" : "Không có thông tin"}
                      </h1>
                      <div className="w-full flex justify-center md:justify-end items-end mt-12">
                        <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                          <span className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-full hover:bg-green-700 font-bold">{locale == "en" ? "Back to home" : "Quay lại trang chủ"}</span>
                        </LinkComponent>
                      </div>
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>
      <Contact />
    </>
  );
}

export default OrderHistory