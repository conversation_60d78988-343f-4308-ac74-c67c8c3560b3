import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import Input from '../../components/components/Input';
import axios from "../../services/axios";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }


const PersonalInformation = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const token = localStorage.getItem('token');
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassowrd, setConfirmNewPassword] = useState("");

  useEffect(() => {
    if (token) {
      axios.get('https://api.echomedi.com' + '/api/user/getMe', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
        .then(function (response) {
          toast.success('Thành công');
        })
        .catch(function (error) {
          if (error.response.status == 401) {
            toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
            localStorage.removeItem("token");
            window.location.href = '/login';
          }
        });
    }
  }, [token]);

  return <>
    <Head>
      <title>ECHO MEDI</title>
      <meta
        name="ECHO MEDI"
        content="ECHO MEDI"
      />
      <meta name="keywords" content="ECHO MEDI"></meta>
      <link rel="icon" href="/favicon1.png" />
    </Head>

    <div className="space-y-2 px-6 mt-4 max-w-[568px] m-auto py-6">
      <p className='text-2xl font-bold mb-4 text-center'>Đổi mật khẩu</p>
      <form className="w-full max-w-lg col-span-1 px-5">
        <div className="flex flex-wrap -mx-3 mb-6">
          <div className="w-full px-3">
            <Input label="Mật khẩu hiện tại" placeholder=""
          type="password"
          value={currentPassword}
          
          onChange={(e: any) => setCurrentPassword(e.target.value)} id={undefined} name={undefined} errors={undefined} required={undefined} suffix={undefined} prefix={undefined} onFocus={undefined} disabled={undefined} />
          </div>
        </div>
        <div className="flex flex-wrap -mx-3 mb-6">
          <div className="w-full px-3">
          <Input label="Mật khẩu mới" placeholder=""
          value={newPassword}
          type="password"
          onChange={(e: any) => setNewPassword(e.target.value)} id={undefined} name={undefined} errors={undefined} required={undefined} suffix={undefined} prefix={undefined} onFocus={undefined} disabled={undefined} />
          </div>
        </div>
        <div className="flex flex-wrap -mx-3 mb-6">
          <div className="w-full px-3">
          <Input label="Nhập lại mật khẩu mới" placeholder=""
          value={confirmNewPassowrd}
          type="password"
          onChange={(e: any) => setConfirmNewPassword(e.target.value)} id={undefined} name={undefined} errors={undefined} required={undefined} suffix={undefined} prefix={undefined} onFocus={undefined} disabled={undefined} />
          </div>
        </div>
        <div className='grid grid-cols-2 mt-10'>
          <button 
          onClick={() => {
            axios.post("/auth/change-password", {
              currentPassword,
              password: newPassword,
              passwordConfirmation: confirmNewPassowrd,
            })
              .catch(error => {
                toast.error(error.response?.data?.error?.message);
                if (error.response.status == 401) {
                  toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
                  localStorage.removeItem("token");
                  window.location.href = '/login';
                }
              })
              .then(response => {
                if (response?.status == 200) {
                  toast.success('Đổi mật khẩu thành công');
                }
              })
              .finally(() => {
              });
          }}
          className="bg-green-800 hover:bg-green-900 text-white font-bold py-2 px-4 rounded mr-2">
            {locale === "en" ? "Save" : "Lưu"}
          </button>
          <button className="w-full bg-transparent hover:bg-gray-200 text-green-700 font-semibold hover:text-black py-2 px-4 border border-green-700 hover:border-gray-300 rounded ml-2">
            {locale === "en" ? "Cancel" : "Huỷ"}
          </button>
        </div>
      </form>
    </div>
    <Contact />
  </>
}

export default PersonalInformation
