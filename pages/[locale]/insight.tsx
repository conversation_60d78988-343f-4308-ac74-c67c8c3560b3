import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import axios from 'axios';
import React, { useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import SmallHero from "../../components/Hero/SmallHero";
import { makeStaticProps } from '../../lib/getStatic';
import LinkComponent from "../../components/Link";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

const tranlsate = (term: string, locale: string) => {
  if (locale === "en") {
    switch (term) {
      case "contact_info":
        return "Contact Information";
      case "become_a_member":
        return "Become A Member";
      case "gifting":
        return "Gifting";
    }
  } else {
    switch (term) {
      case "contact_info":
        return "Thông Tin Liên Hệ";
      case "become_a_member":
        return "Trở Thành Thành Viên";
      case "gifting":
        return "Quà tặng";
    }
  }
}

const Home: NextPage = () => {
  const [email, setEmail] = useState("");
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';

  const sendEmailSubscription = () => {
    axios.post('https://api.echomedi.com' + '/api/packages/subscribeInfo', {
      "email": email,
    })
      .then(function (response) {
        toast.success('Thành công');
      })
      .catch(function (error) {
        if (error.response.status == 401) {
          toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
          localStorage.removeItem("token");
          window.location.href = '/login';
        }
      });
  }

  return (
    <>
      <Head>
        <title>ECHO MEDI</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>

      <SmallHero heading={locale === "en" ? "Insight" : "Tổng quan"} message={""} sub_message={[]} image_url={"https://d3e4m6b6rxmux9.cloudfront.net/pexels_antoni_shkraba_5214952_2_836c5784e8.jpg?updated_at=2023-01-07T04:15:21.739Z"} />
      <div className="max-w-[1240px] mx-auto p-4 text-center">
        <div className="grid grid-rows-none md:grid-cols-3 p-4 gap-4 pt-12">
          <div className="w-full h-full col-span-2 md:col-span-1 row-span-2">
            <p className='text-center text-2xl font-medium mb-4'>{locale === "en" ? "ECHO MEDI News" : "Tin tức ECHO MEDI"}</p>
            <img src="https://d3e4m6b6rxmux9.cloudfront.net/pexels_rodnae_productions_8401909_1024x683_94ceb57669.jpg?updated_at=2023-01-07T04:18:02.545Z" />
          </div>
          <div className="w-full h-full col-span-2 md:col-span-1 row-span-2">
            <p className='text-center text-2xl font-medium mb-4'>{locale === "en" ? "Video" : "Video"}</p>
            <img src="https://d3e4m6b6rxmux9.cloudfront.net/pexels_pixabay_219932_1024x685_010403ae94.jpg?updated_at=2023-01-07T04:18:23.316Z" />
          </div>
          <LinkComponent skipLocaleHandling={undefined} locale={""} href={"/knowledge"}>
            <div className="w-full h-full col-span-2 md:col-span-1 row-span-2">
              <p className='text-center text-2xl font-medium mb-4'>{locale === "en" ? "Knowledge" : "Kiến thức y khoa"}</p>
              <img src="https://d3e4m6b6rxmux9.cloudfront.net/pexels_lisa_1662145_1024x683_f693c344fd.jpg?updated_at=2023-01-07T04:18:46.400Z" />
            </div>
          </LinkComponent>
        </div>
        <div className="max-w-[500px] h-full col-span-2 md:col-span-1 row-span-2 pt-10 m-auto">
          <div className="flex justify-center">
            <div className="mb-3 w-full">
              <p>{locale === "en" ? "Newsletter" : "Đăng Ký Nhận Thông Tin"}</p>
              <input
                type="text"
                className="
                    form-control
                    mt-3
                    mb-3
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                id="exampleFormControlInput1"
                placeholder={locale === "en" ? "Your Email" : "Email của bạn"}
                onChange={(e) => { setEmail(e.target.value) }}
              />
              <button onClick={sendEmailSubscription}>
                <div className="mb-4 inline-block px-6 py-2.5 text-white font-semibold text-sm leading-tight uppercase rounded-full shadow-md hover:bg-emgreen hover:shadow-lg focus:bg-emgreen focus:shadow-lg focus:outline-none focus:ring-0 active:bg-emgreen active:shadow-lg transition duration-150 ease-in-out bg-emgreen text-white rounded-full">
                  {locale == "en" ? "Subscribe" : "Đăng ký"}
                </div>
              </button>
            </div></div></div>
      </div>
      <Contact />
      <Toaster
        position="bottom-center"
      />
    </>
  );
};

export default Home;
