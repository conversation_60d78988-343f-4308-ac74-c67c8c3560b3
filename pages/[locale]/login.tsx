import type { NextPage } from "next";
import Head from "next/head";
import { useRouter } from 'next/router';
import axios from 'axios';
import React, { useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import Contact from "../../components/Contact/Contact";
import Modal from "../../components/components/Modal";
import LinkComponent from "../../components/Link";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})

export { getStaticPaths, getStaticProps }
interface LoginResponse { jwt: string; }
interface LoginData { email: string; password: string; }

const Home: NextPage = () => {
  const [loginData, setLoginData] = useState<LoginData>({ email: "", password: "" });
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const locale = (router.query.locale as string) || 'vi';
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLoginData({ ...loginData, [e.target.name]: e.target.value });
  };

  const validateInputs = (): boolean => {
    const errorMessage =
      locale === "en"
        ? "Invalid information"
        : "Thông tin không hợp lệ";
    if (loginData.email == "" || loginData.password == "") {
      toast.error(errorMessage);
      return false;
    }
    return true;
  };
  const performLogin = async ({ email, password }: LoginData): Promise<LoginResponse> => {
    try {
      const response = await axios.post<LoginResponse>('https://api.echomedi.com/api/user/auth', { identifier: email, password });
      return response.data;
    } catch (error: any) {
      const message = locale == "vi" ? "Đăng nhập không thành công. Vui lòng kiểm tra lại tên đăng nhập, mật khẩu" : "Login fail. Invalid credentials"
      throw new Error(message);
    }
  };
  const handleLogin = async () => {
    if (!validateInputs()) return;
    const toastId = toast.loading(locale === "en" ? 'Loading...' : 'Đang tải...');
    try {
      const response = await performLogin(loginData);
      toast.success(locale === "en" ? 'Login successful' : 'Đăng nhập thành công');
      localStorage.setItem('token', response.jwt);
      location.href = "/";
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      toast.dismiss(toastId);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleLogin();
  };
  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Đăng nhập" : "Login"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section className="bg-gray-50">
        <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
          <div className="w-full bg-white rounded-lg shadow sm:max-w-md xl:p-0">
            <div className="p-6 space-y-4 sm:p-8">
              <h1 className="text-xl font-bold leading-tight text-gray-900 md:text-2xl text-center">{locale === "en" ? "Login" : "Đăng nhập"}</h1>
              <form className="space-y-4" onSubmit={handleSubmit}>
                <div>
                  <label htmlFor="email" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Email or phone number" : "Email hoặc số điện thoại"}</label>
                  <input id="email" name="email" type="text" value={loginData.email} onChange={handleChange} className="bg-gray-50 h-10 border border-gray-300 text-gray-900 rounded-lg block w-full p-2.5" aria-required="true" />
                </div>
                <div className="relative">
                  <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Password" : "Mật khẩu"}</label>
                  <input id="password" name="password" type={showPassword ? 'text' : 'password'} value={loginData.password} onChange={handleChange} className="bg-gray-50 h-10 border border-gray-300 text-gray-900 rounded-lg block w-full p-2.5" aria-required="true" />
                  <div
                    className="absolute right-0 top-1/2 mt-2 mr-3 flex items-center cursor-pointer"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <>
                        <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                        </svg>
                      </>
                    ) : (
                      <>
                        <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                        </svg>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-start">
                    <div className="flex items-center gap-2">
                      <input id="remember" aria-describedby="remember" type="checkbox" className="w-5 h-5 border border-gray-300 rounded" />
                      <label htmlFor="remember" className="text-sm">{locale === "en" ? "Remember me" : "Ghi nhớ đăng nhập"}</label>

                    </div>
                  </div>
                  <div className="flex items-start">
                    <LinkComponent href={"/forgotpassword"} locale={locale} skipLocaleHandling={false}>
                      <p className="font-medium text-[#156634] hover:underline text-sm">{locale === "en" ? "Forgot password" : "Quên mật khẩu"}</p>
                    </LinkComponent>
                  </div>
                </div>
                <button type="submit" className="w-full bg-[#156634] text-white rounded-lg px-5 py-2.5">{locale === "en" ? "Login" : "Đăng nhập"}</button>
                <div className="flex items-center justify-center">
                  <LinkComponent href={"/signup"} locale={locale} skipLocaleHandling={false}>
                    <p className="text-gray-500 text-sm">{locale === "en" ? "Don’t have an account yet?" : "Bạn chưa có tài khoản?"} <span className="font-medium text-[#156634] text-sm underline">{locale === "en" ? "Sign up" : "Đăng ký"}</span></p>
                  </LinkComponent>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
      <Contact />
      <Toaster
        position="bottom-center"
      />
    </>
  );
};

export default Home;
