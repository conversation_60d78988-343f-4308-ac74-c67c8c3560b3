import axios from 'axios';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from 'next/head';
import { makeStaticProps } from '../../lib/getStatic';
import LinkComponent from '../../components/Link';
import React from 'react';
import MenuSlider from '../../components/LayoutUser/LayoutUser';
import useUserData from '../../hooks/useUserData';
import useToken from '../../hooks/useToken';
import { REGION_DATA } from '../../constants/Regions'
const getStaticProps = makeStaticProps(['common', 'footer']);
const getStaticPaths = () => ({
  fallback: false,
  paths: [
    {
      params: {
        locale: 'en',
        slug: 'test',
        label: 'test2',
      },
    },
    {
      params: {
        locale: 'vi',
        slug: 'test',
        label: 'test2',
      },
    },
  ],
});
export { getStaticPaths, getStaticProps };

export const menuItems = [
  { id: 'personal', label: 'Thông tin cá nhân', en_label: 'Personal Information', linkURL: 'personal_information' },
  { id: 'purchases', label: 'Lịch sử mua hàng', en_label: 'Order History', linkURL: 'order_history' },
  { id: 'appointments', label: 'Lịch sử đặt hẹn', en_label: 'Booking History', linkURL: 'booking_history' },
  { id: 'test_results', label: 'Kết quả xét nghiệm', en_label: 'Test results', linkURL: 'test_results' },
  { id: 'change-password', label: 'Thay đổi mật khẩu', en_label: 'Change Password', linkURL: 'change-password' },
];
interface Address {
  province: {
    id: string
    name: string
  }
  district: {
    id: string
    name: string
  }
  ward: {
    id: string
    name: string
  }
  address: string
}

interface Patient {
  phone?: string
  email?: string
  full_name?: string
  birthday?: string
  gender?: string
  address?: Address
}

interface Region {
  id: string
  name: string
  type: string
  level2s: Level2[]
}

interface Level2 {
  id: string
  name: string
  type: string
  level3s: Level3[]
}

interface Level3 {
  id: string
  name: string
  type: string
}

const PersonalInformation = () => {
  const router = useRouter();
  const { token } = useToken();
  const { userData, setUserData } = useUserData(token);
  const locale = router.query.locale as string || 'vi';
  const [loading, setLoading] = useState(false)
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<Patient>>({});

  const [selectedProvince, setSelectedProvince] = useState<Region | null>(null)
  const [selectedDistrict, setSelectedDistrict] = useState<Level2 | null>(null)
  const [selectedWard, setSelectedWard] = useState<Level3 | null>(null)
  const [streetAddress, setStreetAddress] = useState("")

  const [availableDistricts, setAvailableDistricts] = useState<Level2[]>([])
  const [availableWards, setAvailableWards] = useState<Level3[]>([])

  useEffect(() => {
    if (selectedProvince) {
      setAvailableDistricts(selectedProvince.level2s)
      if (!isEditing) {
        // If not in edit mode, try to find the previously selected district
        const district = selectedProvince.level2s.find(
          d => d.id === userData?.patient?.address?.district?.id
        ) || null
        setSelectedDistrict(district)
      } else if (!selectedDistrict || !selectedProvince.level2s.find(d => d.id === selectedDistrict.id)) {
        // Reset district selection if current selection not available in new province
        setSelectedDistrict(null)
        setSelectedWard(null)
        setAvailableWards([])
      }
    } else {
      setAvailableDistricts([])
    }
  }, [selectedProvince, userData?.patient?.address?.district?.id, isEditing, selectedDistrict])

  // Update available wards when district changes
  useEffect(() => {
    if (selectedDistrict) {
      setAvailableWards(selectedDistrict.level3s)
      if (!isEditing) {
        // If not in edit mode, try to find the previously selected ward
        const ward = selectedDistrict.level3s.find(
          w => w.id === userData?.patient?.address?.ward?.id
        ) || null
        setSelectedWard(ward)
      } else if (!selectedWard || !selectedDistrict.level3s.find(w => w.id === selectedWard.id)) {
        // Reset ward selection if current selection not available in new district
        setSelectedWard(null)
      }
    } else {
      setAvailableWards([])
    }
  }, [selectedDistrict, userData?.patient?.address?.ward?.id, isEditing, selectedWard])

  // Update formData address when location selections change during editing
  useEffect(() => {
    if (isEditing && selectedProvince && selectedDistrict && selectedWard) {
      setFormData(prev => ({
        ...prev,
        address: {
          province: {
            id: selectedProvince.id,
            name: selectedProvince.name,
          },
          district: {
            id: selectedDistrict.id,
            name: selectedDistrict.name,
          },
          ward: {
            id: selectedWard.id,
            name: selectedWard.name,
          },
          address: streetAddress,
        },
      }))
    }
  }, [selectedProvince, selectedDistrict, selectedWard, streetAddress, isEditing])

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle province selection change
  const handleProvinceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const provinceId = e.target.value
    const province = REGION_DATA.find(p => p.id === provinceId) || null
    setSelectedProvince(province)
  }

  // Handle district selection change
  const handleDistrictChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const districtId = e.target.value
    const district = availableDistricts.find(d => d.id === districtId) || null
    setSelectedDistrict(district)
  }

  // Handle ward selection change
  const handleWardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const wardId = e.target.value
    const ward = availableWards.find(w => w.id === wardId) || null
    setSelectedWard(ward)
  }

  // Handle street address change
  const handleStreetAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStreetAddress(e.target.value)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    try {
      setLoading(true)

      // Remove empty fields
      const cleanedFormData = Object.fromEntries(
        Object.entries(formData).filter(([_, value]) => value !== undefined && value !== null && value !== ""),
      )

      // Create updates array from cleaned form data
      const updates = Object.entries(cleanedFormData).map(([field, value]) => ({
        field,
        value: field === "birthday" && typeof value === "string" ? new Date(value).toISOString() : value,
      }))

      console.log("Updates to be sent:", updates)

      // Send all updates in parallel
      await Promise.all(
        updates.map(async ({ field, value }) => {
          await axios.post(
            "https://api.echomedi.com/api/patient/updatePatient",
            { field, value },
            { headers: { Authorization: `Bearer ${token}` } },
          )
        }),
      )

      // Update userData with the new form data to reflect changes immediately
      if (userData && userData.patient) {
        // Create a deep copy of userData to avoid mutation issues
        const updatedUserData = {
          ...userData,
          patient: {
            ...userData.patient,
            ...formData,
            // Ensure address is properly updated
            address: formData.address,
          },
        }

        setUserData(updatedUserData)
      }
      toast.success(locale === "en" ? 'Information updated successfully' : 'Cập nhật thông tin thành công');
      setIsEditing(false)
    } catch (error) {
      toast.error(locale === "en" ? 'Failed to update information' : 'Cập nhật thông tin không thành công');
    } finally {
      setLoading(false)
    }
  }

  // Initialize form data from user data
  useEffect(() => {
    if (userData && userData.patient) {
      setFormData({
        full_name: userData.patient.full_name || "",
        phone: userData.patient.phone || "",
        email: userData.patient.email || "",
        birthday: userData.patient.birthday || "",
        gender: userData.patient.gender || "male",
        address: userData.patient.address,
      })

      if (userData.patient.address) {
        // Find and set the selected province
        const province = REGION_DATA.find(p => p.id === userData.patient.address?.province?.id) || null
        setSelectedProvince(province)

        // Street address is set here
        setStreetAddress(userData.patient.address.address || "")
      }
    }
  }, [userData])

  const getFormattedAddress = () => {
    if (!userData?.patient?.address) return ""

    const parts = [
      userData.patient.address.address,
      userData.patient.address.ward?.name,
      userData.patient.address.district?.name,
      userData.patient.address.province?.name
    ].filter(Boolean)

    return parts.join(", ")
  }

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return ""
    try {
      return new Date(dateString).toISOString().split("T")[0]
    } catch (e) {
      return ""
    }
  }

  return (
    <>
      <Head>
        <title>Personal Information - ECHO MEDI</title>
        <meta
          name="description"
          content="Manage and update your personal information securely at ECHO MEDI. Keep your profile details, contact information, and medical history up to date."
        />
        <meta name="keywords" content="ECHO MEDI, personal information, profile update, contact details, medical history" />
        <meta property="og:title" content="Personal Information - ECHO MEDI" />
        <meta
          property="og:description"
          content="Update and manage your personal details securely at ECHO MEDI."
        />
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className="bg-[#FAFBFD] 2xl:container 2xl:mx-auto lg:px-20 my-4 md:px-6 px-2">
        <section>
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="inline-flex items-center -space-x-1 md:space-x-1">
              <li className="inline-flex items-center">
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Home" : "Trang chủ"}
                  </span>
                </LinkComponent>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                  </svg>
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] mt-1 md:mt-0 md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Personal information" : "Thông tin cá nhân"}
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </section>
        {userData ? (
          <div className="flex md:flex-row flex-col gap-8 my-4">
            <div className="md:w-64 md:h-2/3 bg-white shadow-md rounded-2xl hidden md:block">
              <div className="p-4 flex flex-col items-center justify-center">
                <svg width="111" height="111" viewBox="0 0 111 111" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M110.083 55.4993C110.083 85.6449 85.6449 110.083 55.4993 110.083C25.3538 110.083 0.916016 85.6449 0.916016 55.4993C0.916016 25.3538 25.3538 0.916016 55.4993 0.916016C85.6449 0.916016 110.083 25.3538 110.083 55.4993ZM71.8743 39.1243C71.8743 48.168 64.543 55.4993 55.4993 55.4993C46.4557 55.4993 39.1243 48.168 39.1243 39.1243C39.1243 30.0807 46.4557 22.7493 55.4993 22.7493C64.543 22.7493 71.8743 30.0807 71.8743 39.1243ZM55.4993 101.895C65.2372 101.895 74.2744 98.8951 81.7371 93.7688C85.0332 91.5047 86.4418 87.1917 84.5255 83.682C80.5528 76.4061 72.3669 71.8743 55.499 71.8743C38.6314 71.8743 30.4454 76.406 26.4727 83.6816C24.5563 87.1913 25.9649 91.5043 29.2609 93.7685C36.7237 98.895 45.7611 101.895 55.4993 101.895Z" fill="#14813D" />
                </svg>
                <p className='mt-4'>{userData?.patient?.full_name}</p>
                <p>{userData?.phone}</p>
              </div>
              <MenuSlider menuItems={menuItems} defaultActiveId="personal" />
            </div>
            <div className="flex-1">
              <div className="bg-white rounded-lg md:p-6 pb-8">
                <h1 className="text-xl font-bold text-[#156634] md:mb-6 p-4">{locale === "en" ? "Personal information" : "Thông tin cá nhân"}</h1>
                <form className="w-full col-span-1 px-5" onSubmit={handleSubmit}>
                  <div className="flex flex-wrap -mx-3 mb-6">
                    <div className="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                      <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="fullname">
                        {locale === "en" ? "Full name" : "Tên đầy đủ"}
                      </label>
                      <input
                        name="full_name"
                        value={formData.full_name || ""}
                        onChange={handleChange}
                        className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                        id="fullname"
                        type="text"
                        placeholder="Nguyen Van A"
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                      <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="phonenumber">
                        {locale === "en" ? "Phone number" : "Số điện thoại"}
                      </label>
                      <input
                        name="phone"
                        value={formData.phone || ""}
                        onChange={handleChange}
                        className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                        id="phonenumber"
                        type="text"
                        placeholder="0123456789"
                        disabled
                      />
                    </div>
                  </div>
                  <div className="flex flex-wrap -mx-3 mb-6">
                    <div className="w-full px-3">
                      <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="email">
                        Email
                      </label>
                      <input
                        name="email"
                        value={formData.email || ""}
                        onChange={handleChange}
                        className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        disabled
                      />
                    </div>
                  </div>

                  {!isEditing ? (
                    <div className="flex flex-wrap -mx-3 mb-6">
                      <div className="w-full px-3">
                        <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="address">
                          {locale === "en" ? "Address" : "Địa chỉ"}
                        </label>
                        <input
                          name="address"
                          value={getFormattedAddress()}
                          className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                          id="address"
                          type="text"
                          placeholder=""
                          disabled={!isEditing}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-wrap -mx-3 mb-6">
                      <div className="w-1/3 px-3 mb-6">
                        <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="fullname">
                          {locale === "en" ? "City/Province" : "Tỉnh/Thành phố"}
                        </label>
                        <select
                          id="province"
                          value={selectedProvince?.id || ""}
                          onChange={handleProvinceChange}
                          className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                        >
                          {REGION_DATA.map((province) => (
                            <option key={province.id} value={province.id}>
                              {province.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="w-1/3 px-3 mb-6">
                        <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="fullname">
                          {locale === "en" ? "District" : "Quận/Huyện"}
                        </label>
                        <select
                          id="district"
                          value={selectedDistrict?.id || ""}
                          onChange={handleDistrictChange}
                          className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                          disabled={!selectedProvince}
                        >
                          {availableDistricts.map((district) => (
                            <option key={district.id} value={district.id}>
                              {district.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="w-1/3 px-3 mb-6">
                        <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="fullname">
                          {locale === "en" ? "Ward/Commune" : "Phường/Xã"}
                        </label>
                        <select
                          id="ward"
                          value={selectedWard?.id || ""}
                          onChange={handleWardChange}
                          className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                          disabled={!selectedDistrict}
                        >
                          {availableWards.map((ward) => (
                            <option key={ward.id} value={ward.id}>
                              {ward.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="w-full px-3 mb-6">
                        <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="fullname">
                          {locale === "en" ? "Detailed Address" : "Địa chỉ chi tiết"}
                        </label>
                        <input
                          id="streetAddress"
                          value={streetAddress}
                          onChange={handleStreetAddressChange}
                          className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                          type="text"
                          placeholder=""
                        />
                      </div>
                    </div>
                  )}

                  <div className="flex flex-wrap -mx-3 mb-2">
                    <div className="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                      <label className="block uppercase tracking-wide  text-xs font-bold mb-2" htmlFor="birthday">
                        {locale === "en" ? "Birthday" : "Ngày sinh"}
                      </label>
                      <input
                        name="birthday"
                        value={formatDate(formData.birthday)}
                        onChange={handleChange}
                        className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                        id="birthday"
                        type="date"
                        disabled={!isEditing}
                        style={{ color: !isEditing ? '#aaa' : '#000' }}
                      />
                    </div>
                    <div className="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                      <label className="block uppercase tracking-wide text-xs font-bold mb-2" htmlFor="gender">
                        {locale === "en" ? "Gender" : "Giới tính"}
                      </label>
                      <select
                        name="gender"
                        value={formData.gender || "male"}
                        onChange={handleChange}
                        className={`appearance-none block w-full bg-white border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 ${isEditing ? '' : 'disabled'}`}
                        id="gender"
                        disabled={!isEditing}
                        style={{ color: !isEditing ? '#aaa' : '#000' }}
                      >
                        <option value="male">{locale === "en" ? "Male" : "Nam"}</option>
                        <option value="female">{locale === "en" ? "Female" : "Nữ"}</option>
                        <option value="other">{locale === "en" ? "Other" : "Khác"}</option>
                      </select>
                    </div>
                  </div>
                  <div className='mt-10 flex items-center justify-center'>
                    <button
                      type="button"
                      onClick={() => setIsEditing(!isEditing)}
                      className="text-[#156634] font-bold py-2 px-10 rounded-full mr-2 border border-[#156634]"
                    >
                      {isEditing ? (locale === "en" ? "Cancel" : "Hủy") : (locale === "en" ? "Edit Information" : "Chỉnh sửa thông tin")}
                    </button>
                    {isEditing && (
                      <button
                        type="submit"
                        className="text-white font-bold py-2 px-10 rounded-full bg-[#156634]"
                      >
                        {locale === "en" ? "Save information" : "Lưu thông tin"}
                      </button>
                    )}
                  </div>
                </form>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center mt-4">
            <p className='md:text-base text-sm text-center'>{locale === "en" ? "No personal information available" : "Chưa có thông tin cá nhân"}</p>
          </div>
        )}
      </div>
      <Contact />
    </>
  );
};

export default PersonalInformation;