import { NextPage } from 'next';
import Image from 'next/image'
import { useRouter } from 'next/router';
import React, { useState } from 'react'
import { shimmer, toBase64 } from '../../lib/ui';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper';
import { Toaster } from "react-hot-toast";
import Contact from "../../components/Contact/Contact";
import { dataImage, dataImageCompany } from '../../utils/dataBusiness';
import useIsMobile from '../../utils/detectMob';
import { makeStaticProps } from '../../lib/getStatic';
import BookingBusiness from '../../components/BookingBusiness';
import { CorporatePagesLayout } from '../../components/CorporatePages/CorporateLayout';
import Head from 'next/head';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }
const CorporatePages: NextPage = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const isMobile = useIsMobile();
    const [showModal, setShowModal] = useState(false);
    const [message, setMessage] = useState("");
    const handleShowModal = () => {
        setShowModal(true);
        setMessage("Khách hàng Đăng ký tư vấn doanh nghiệp")
    };
    const [selectedImageId, setSelectedImageId] = useState<number>(1);
    const handleDivClick = (id: number) => {
        setSelectedImageId(id);
    };
    const dataImages = dataImage(locale);
    const selectedImage = dataImages.find(image => image.id === selectedImageId) || dataImages[0];
    return (
        <>
            <Head>
                <title>{locale === "vi" ? "ECHOMEDI - Khách hàng doanh nghiệp" : "ECHOMEDI - Corporate Clients"}</title>
                <meta name="description" content="Tìm hiểu cách Echo Medi hợp tác với doanh nghiệp, cung cấp giải pháp y tế hiện đại, dịch vụ chuyên nghiệp và nâng cao chất lượng chăm sóc sức khỏe cho khách hàng của bạn." />
                <meta name="keywords" content="Echo Medi, giải pháp doanh nghiệp, dịch vụ y tế, chăm sóc sức khỏe, đối tác doanh nghiệp" />
                <meta property="og:title" content="Echo Medi - Đối Tác Doanh Nghiệp Tin Cậy" />
                <meta property="og:description" content="Khám phá các giải pháp y tế từ Echo Medi giúp tối ưu hóa hoạt động doanh nghiệp và chăm sóc sức khỏe nhân viên." />
                <meta property="og:image" content="/banner/Old-Mobile-VIE.webp" />
                <meta property="og:type" content="website" />
                <meta property="twitter:card" content="summary_large_image" />
                <meta property="twitter:title" content="Giải Pháp Y Tế Cho Doanh Nghiệp - Echo Medi" />
                <meta property="twitter:description" content="Echo Medi cung cấp các giải pháp y tế toàn diện, cải thiện sức khỏe và hiệu suất công việc cho doanh nghiệp." />
                <meta property="twitter:image" content="/banner/Old-Mobile-VIE.webp" />
            </Head>
            <section>
                <div className="mx-auto">
                    <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                        <div className="w-full relative">
                            <Image
                                src="/banner/banner_new.webp"
                                alt="Banner"
                                width={1920}
                                height={300}
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                                className="object-center"
                                layout="responsive"
                            />
                        </div>
                        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 2xl:w-1/2 absolute md:px-16 px-4">
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block">
                                {locale === "vi" ? "KHÁCH HÀNG\n DOANH NGHIỆP" : "CORPORATE\n CLIENTS"}
                            </h2>
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                                {locale === "vi" ? "KHÁCH HÀNG\n DOANH NGHIỆP" : "CORPORATE\n CLIENTS"}
                            </h2>
                            <p className="text-base text-justify hidden md:block">
                                {locale === "en" ? "Corporate health programs are a cost-effective investment that bring numerous benefits to employees' physical and mental well-being. These programs help reduce medical treatment costs and time, while boosting the overall value and performance of the business." : "Chương trình sức khỏe doanh nghiệp được đầu tư với chi phí rất thấp nhưng mang rất nhiều tác động tích cực lên sức khỏe thể chất lẫn tinh thần người lao động, tiết kiệm chi phí và thời gian điều trị bệnh, đồng thời gia tăng giá trị và hiệu suất tổng thể của doanh nghiệp."}
                            </p>
                        </div>
                    </div>
                    <p className="text-sm my-6 text-justify px-4 block md:hidden">
                        {locale === "en" ? "Corporate health programs are a cost-effective investment that bring numerous benefits to employees' physical and mental well-being. These programs help reduce medical treatment costs and time, while boosting the overall value and performance of the business." : "Chương trình sức khỏe doanh nghiệp được đầu tư với chi phí rất thấp nhưng mang rất nhiều tác động tích cực lên sức khỏe thể chất lẫn tinh thần người lao động, tiết kiệm chi phí và thời gian điều trị bệnh, đồng thời gia tăng giá trị và hiệu suất tổng thể của doanh nghiệp."}
                    </p>
                </div>
            </section>
            <CorporatePagesLayout />
            <section className="relative py-16">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="w-full flex-col justify-start items-center inline-flex">
                        <div className="block md:hidden">
                            <h2 className="font-bold md:text-2xl text-lg text-[#156634] uppercase pb-4 text-center">{locale === "vi" ? "ỨNG DỤNG CÔNG NGHỆ" : "utilizes technology to monitor"}</h2>
                        </div>
                        <div className='flex items-center justify-between flex-col md:flex-row gap-4 md:gap-8'>
                            <img src={selectedImage.img} alt="ECHO MEDI ỨNG DỤNG CÔNG NGHỆ CHĂM SÓC SỨC KHỎE TẠI DOANH NGHIỆP" />
                            <div>
                                <div className="hidden md:block space-y-2">
                                    <h2 className="font-bold md:text-2xl text-lg text-[#156634] uppercase pb-4 text-left">{locale === "vi" ? "ỨNG DỤNG CÔNG NGHỆ" : "utilizes technology to monitor"}</h2>
                                </div>
                                <div className="flex-col justify-center items-center space-y-2">
                                    <div
                                        onClick={() => handleDivClick(1)}
                                        className={`w-full h-full p-3.5 rounded-xl transition-all duration-700 ease-in-out flex-col justify-start items-start gap-2.5 inline-flex ${selectedImageId === 1 ? 'md:border-[1px] border-[#156634] bg-white' : 'hover:border hover:border-[#156634]'
                                            }`}
                                    >
                                        <p className={`text-sm md:text-base text-justify ${selectedImageId === 1 ? 'font-bold' : ''}`}>
                                            {locale === "vi" ? "1. Các ứng dụng và nền tảng trực tuyến giúp nâng cao khả năng tiếp cận dịch vụ chăm sóc sức khỏe" : "1. Online apps and platforms improve healthcare accessibility."}
                                        </p>
                                    </div>
                                    <div
                                        onClick={() => handleDivClick(2)}
                                        className={`w-full h-full p-3.5 rounded-xl transition-all duration-700 ease-in-out flex-col justify-start items-start gap-2.5 inline-flex ${selectedImageId === 2 ? 'md:border-[1px] border-[#156634] bg-white' : 'hover:border hover:border-[#156634]'
                                            }`}
                                    >
                                        <p className={`text-sm md:text-base text-justify ${selectedImageId === 2 ? 'font-bold' : ''}`}>
                                            {locale === "vi" ? "2. Đồng hồ thông minh giúp theo dõi các chỉ số cơ thể, giảm rủi ro sức khỏe, liên kết trực tiếp với hệ thống lưu trữ hồ sơ sức khỏe" : "2. Utilize smartwatches to monitor vital health indicators, mitigate health risks, and integrate with health record storage systems."}
                                        </p>
                                    </div>
                                    <div
                                        onClick={() => handleDivClick(3)}
                                        className={`w-full h-full p-3.5 rounded-xl transition-all duration-700 ease-in-out flex-col justify-start items-start gap-2.5 inline-flex ${selectedImageId === 3 ? 'md:border-[1px] border-[#156634] bg-white' : 'hover:border hover:border-[#156634]'
                                            }`}
                                    >
                                        <p className={`text-sm md:text-base text-justify ${selectedImageId === 3 ? 'font-bold' : ''}`}>
                                            {locale === "vi" ? "3. Hệ thống quản lý bệnh án điện tử thu thập và lưu trữ hồ sơ sức khỏe trọn đời" : "3. The electronic medical records management system collects and stores comprehensive lifelong health data."}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="relative">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <section className='hidden md:block'>
                        <div className="flex-col justify-start items-center gap-2.5 flex">
                            <p className="text-center font-bold md:text-2xl text-lg text-[#156634] uppercase pb-8">{locale === "vi" ? "NHỮNG GIẢI PHÁP SỨC KHỎE" : "HEALTHCARE SOLUTIONS"}</p>
                        </div>
                    </section>
                    <div className="w-full justify-start items-center md:gap-16 grid lg:grid-cols-2 grid-cols-1 bg-[#156634] rounded-3xl">
                        <section className='slide-corporate px-4 md:px-0 md:order-1 order-2'>
                            <Swiper
                                slidesPerView={1}
                                className="bg-[#156634] rounded-3xl mySwiper"
                                pagination={{
                                    clickable: true,
                                }}
                                modules={[Pagination]}
                            >
                                {dataImageCompany.map((data) => (
                                    <SwiperSlide key={data.id} className='rounded-3xl'>
                                        <Image loading='lazy' width={100} height={48} alt="Image Doanh Nghiệp" src={data.img} />
                                    </SwiperSlide>
                                ))}
                            </Swiper>
                        </section>
                        <section className='block md:hidden mt-8 mb-6'>
                            <p className="text-center font-bold md:text-2xl text-lg text-white uppercase">{locale === "vi" ? "NHỮNG GIẢI PHÁP SỨC KHỎE" : "HEALTHCARE SOLUTIONS"}</p>
                        </section>
                        <div className="w-full flex-col justify-start lg:items-start items-center gap-8 inline-flex md:py-8 pb-8 md:order-2 order-1">
                            <div className="flex-col justify-center items-center">
                                <div className="w-full h-full flex-col justify-start items-start py-4 md:py-6 border-b border-solid border-gray-200">
                                    <p className="text-base font-medium text-white ml-4"><span className='bg-white p-1 md:p-2 rounded-full text-[#156634] mr-2'>01</span> {locale === "vi" ? "Gói thành viên vàng và bạch kim" : "Gold and platium membership"}</p>
                                </div>
                                <div className="w-full h-full flex-col justify-start items-start inline-flex py-4 md:py-6 border-b border-solid border-gray-200">
                                    <p className="text-base font-medium text-white ml-4"><span className='bg-white p-1 md:p-2 rounded-full text-[#156634] mr-2'>02</span> {locale === "vi" ? "Gói cố vấn sức khỏe cho doanh nghiệp" : "Corporate healthcare provider package"}</p>
                                </div>
                                <div className="w-full h-full flex-col justify-start items-start inline-flex py-4 md:py-6">
                                    <p className="text-base font-medium text-white ml-4"><span className='bg-white p-1 md:p-2 rounded-full text-[#156634] mr-2'>03</span> {locale === "vi" ? "Chương trình tọa đàm sức khỏe" : "Health Talks"}</p>
                                </div>
                            </div>
                            <section className='hidden md:block'>
                                <button onClick={() => handleShowModal()} className="font-bold flex px-6 py-2 text-black text-xs leading-tight rounded-full bg-white">
                                    <span className="text-center text-[#156634] text-sm font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</span>
                                </button>
                            </section>
                        </div>
                        <div className="block md:hidden p-4 mx-auto md:order-none order-3">
                            <button onClick={() => handleShowModal()} className="font-bold flex px-6 py-2 text-black text-xs leading-tight rounded-full bg-white">
                                <span className="text-center text-[#156634] text-sm font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
            <section className="py-8 relative">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <div className="mx-auto">
                        {isMobile ? (
                            <div className="relative">
                                <img
                                    src="https://api.echomedi.com/uploads/contact_company_83d7196677.png"
                                    alt="Background Image"
                                    className="w-full h-auto object-cover"
                                />
                                <div className="absolute pt-12 inset-0 flex flex-col items-center space-y-6 p-5 bg-white bg-opacity-75 shadow-lg rounded-2xl">
                                    <h2 className="text-center font-bold text-base text-[#156634]">
                                        {locale === "en" ? "ECHO MEDI focuses on comprehensive healthcare, addressing both physical and mental well-being, with medical treatment being a key component." : "Đầu tư vào chăm sóc sức khỏe trong công ty giúp tạo ra một lực lượng lao động khỏe mạnh, hạnh phúc và làm việc hiệu quả, dẫn đến thành công lâu dài!"}
                                    </h2>
                                    <Image
                                        alt="Image Doanh Nghiêp"
                                        width={1000}
                                        height={400}
                                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1000, 400))}`}
                                        className='object-cover'
                                        src={"https://api.echomedi.com/uploads/contact_company_01_a2176cb15b.png"}
                                    />
                                    <button onClick={() => handleShowModal()} className="font-bold px-6 py-2 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]">
                                        <span className="text-center text-white text-sm font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</span>
                                    </button>
                                </div>
                            </div>

                        ) : (
                            <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                                <div className="w-full relative">
                                    <Image
                                        alt="Image Doanh Nghiêp"
                                        width={1000}
                                        height={400}
                                        placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1000, 400))}`}
                                        className='object-cover w-full'
                                        src={"https://d3e4m6b6rxmux9.cloudfront.net/banner_doanh_nghiep_ec670ebece.png"}
                                    />
                                </div>
                                <div className="max-w-3xl absolute md:px-16 px-4">
                                    <h2 className="text-justify font-bold text-sm md:text-2xl text-[#156634] pb-2">
                                        {locale === "vi" ? "Đầu tư vào chăm sóc sức khỏe trong công ty giúp tạo ra một lực lượng lao động khỏe mạnh, hạnh phúc và làm việc hiệu quả, dẫn đến thành công lâu dài!" : "ECHO MEDI focuses on comprehensive healthcare, addressing both physical and mental well-being, with medical treatment being a key component."}
                                    </h2>
                                    <button onClick={() => handleShowModal()} className="mt-6 font-bold flex px-6 py-2 text-black text-xs leading-tight rounded-full bg-[#156634] hover:bg-[#14813d]">
                                        <span className="text-center text-white text-sm font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</span>
                                    </button>
                                </div>
                            </div>
                        )}

                    </div>
                </div>
            </section>
            {showModal && (
                <BookingBusiness
                    onClose={() => setShowModal(false)}
                    locale={locale}
                />
            )}
            <Contact />
            <Toaster position="bottom-center" />
        </>
    )
}
export default CorporatePages;