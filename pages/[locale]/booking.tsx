import type { NextPage } from "next";
import Head from "next/head";
import { useRouter } from "next/router";
import { useMemo, useState } from "react";
import { Toaster } from "react-hot-toast";
import dayjs from "dayjs";
import Contact from "../../components/Contact/Contact";
import { makeStaticProps } from "../../lib/getStatic";
import Booking2 from "../../components/Booking/Booking"

const getStaticProps = makeStaticProps(["common", "footer"]);
const getStaticPaths = () => ({
  fallback: false,
  paths: [
    {
      params: {
        locale: "en",
        slug: "test",
        label: "test2",
      },
    },
    {
      params: {
        locale: "vi",
        slug: "test",
        label: "test2",
      },
    },
  ],
});
export { getStaticPaths, getStaticProps };

const Booking: NextPage = () => {
  const router = useRouter();
  const locale = (router.query.locale as string) || "en";

  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Đặt lịch khám" : "Booking"}</title>
        <meta name="ECHO MEDI" content="ECHO MEDI" />
        <meta name="description" content={locale == "vi" ? "Đặt lịch khám cùng ECHO MEDI" : "Booking visit ECHO MEDI now"} />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <Booking2 />
      <Contact />
      <Toaster position="bottom-center" />
    </>
  );
};

export default Booking;
