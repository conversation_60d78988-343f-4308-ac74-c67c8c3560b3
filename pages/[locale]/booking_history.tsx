import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import LinkComponent from '../../components/Link';
import { makeStaticProps } from '../../lib/getStatic';
import { formatDate, formatDateHistory } from "../../utils/dateTime"
import dayjs from "dayjs";
import useToken from '../../hooks/useToken';
import useUserData from '../../hooks/useUserData';
import MenuSlider from '../../components/LayoutUser/LayoutUser';
import { menuItems } from './personal_information';
import React from 'react';

require("dayjs/locale/vi");
dayjs.locale("vi");

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }

const translate = (locale: String, t: String) => {
    if (locale == "vi") {
        switch (t) {
            case "scheduled":
                return "Đặt lịch"
                break;
            case "confirmed":
                return "Đã xác nhận"
                break;
            case "finished":
                return "Hoàn thành"
                break;
            case "cancelled":
                return "Huỷ"
                break;
            case "postpone":
                return "Hoãn lịch"
                break;
            case "waiting":
                return "Đã đến"
                break;
        }
    } else {
        switch (t) {
            case "scheduled":
                return "Scheduled"
                break;
            case "confirmed":
                return "Confirmed"
                break;
            case "finished":
                return "Finished"
                break;
            case "cancelled":
                return "Cancelled"
                break;
            case "postpone":
                return "Postpone"
                break;
            case "waiting":
                return "Waiting"
                break;
        }
    }
}

interface Booking {
    bookingDate: string;
    branch: string;
    callToRemind: boolean;
    cc_note: string | null;
    code: string | null;
    contactAddress: string;
    contactEmail: string;
    contactFullName: string;
    contactPhoneNumber: string;
    contactReceiver: string | null;
    createdAt: string;
    createdByAdmin: string | null;
    dontShowOnCalendar: boolean;
    id: number;
    latitude: number | null;
    longitude: number | null;
    membership: string | null;
    note: string;
    paid: boolean | null;
    publishedAt: string;
    scheduleTreatmentTimes: any | null;
    status: string;
    timeSession: any | null;
    treatmentTime: string | null;
    type: string;
    updatedAt: string;
}


const BookingHistory = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const [results, setResults] = useState<Booking[]>([]);
    const { token } = useToken();
    const { userData } = useUserData(token);
    useEffect(() => {
        if (token) {
            axios.post('https://api.echomedi.com/api/bookings/getAppointmentsV2', {},
                {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(function (response) {
                    setResults(response.data);
                    toast.success('Thành công');
                })
                .catch(function (error) {
                });
        }
    }, [token]);

    const handleAddress = (address: string, locale: string) => {
        switch (address) {
            case "q7":
                return locale === "en"
                    ? "1026 Nguyen Van Linh, Tan Phong Ward, District 7"
                    : "1026 Nguyễn Văn Linh, P. Tân Phong, Quận 7";
            case "q2":
                return locale === "en"
                    ? "46 Nguyen Thi Dinh, An Phu Ward, District 2"
                    : "46 Nguyễn Thị Định, P. An Phú, Quận 2";
            case "binhduong":
                return locale === "en"
                    ? "Canary Plaza, 5 Binh Duong Highway, Thuan Giao, Thuan An City, Binh Duong"
                    : "Canary Plaza, số 5 Đại lộ Bình Dương, Thuận Giao, Tp. Thuận An, Bình Dương";
            default:
                return "";
        }
    };
    const handleBranch = (address: string, locale: string) => {
        switch (address) {
            case "q7":
                return locale === "en"
                    ? "Clinic District 7"
                    : "Chi nhánh Quận 7";
            case "q2":
                return locale === "en"
                    ? "Clinic District 2"
                    : "Chi nhánh Quận 2";
            case "binhduong":
                return locale === "en"
                    ? "Clinic Binh Duong"
                    : "Chi nhánh Bình Dương";
            default:
                return "";
        }
    };
    const handleStatus = (status: string, locale: string) => {
        switch (status) {
            case "pending":
                return locale === "en"
                    ? "Wait for confirmation"
                    : "Chờ xác nhận";
            case "confirmed":
                return locale === "en"
                    ? "Confirmed"
                    : "Đã xác nhận";
            case "completed":
                return locale === "en"
                    ? "Done"
                    : "Hoàn thành";
            case "finished":
                return locale === "en"
                    ? "Done"
                    : "Hoàn thành";
            default:
                return "";
        }
    };

    const statusColors = {
        pending: 'bg-[#FEFBE8] text-[#C6A811]',
        confirmed: 'bg-[#EBFEFF] text-[#0098C9]',
        completed: 'bg-[#F0FDF4] text-[#156634]',
        finished: 'bg-[#F0FDF4] text-[#156634]',
    };
    return (
        <>
            <Head>
                <title>Booking History - ECHO MEDI</title>
                <meta
                    name="description"
                    content="View and manage your past bookings at ECHO MEDI. Check details, reschedule, or cancel appointments."
                />
                <meta name="keywords" content="ECHO MEDI, booking history, medical appointments, reschedule, cancel bookings" />
                <meta property="og:title" content="Booking History - ECHO MEDI" />
                <meta
                    property="og:description"
                    content="Access your ECHO MEDI booking history and manage your appointments with ease."
                />
                <link rel="icon" href="/favicon1.png" />
            </Head>
            <div className="bg-[#FAFBFD] 2xl:container 2xl:mx-auto lg:px-20 my-4 md:px-6 px-2">
                <section>
                    <nav className="flex" aria-label="Breadcrumb">
                        <ol className="inline-flex items-center -space-x-1 md:space-x-1">
                            <li className="inline-flex items-center">
                                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                                    <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                                        {locale === "en" ? "Home" : "Trang chủ"}
                                    </span>
                                </LinkComponent>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                                    </svg>
                                    <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] mt-1 md:mt-0 text-[8px] md:text-xs font-normal whitespace-nowrap">
                                        {locale === "en" ? "Booking History" : "Lịch sử đặt hẹn"}
                                    </span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                </section>
                <div className="flex md:flex-row flex-col gap-8 mt-4">
                    <div className="w-64 md:h-2/3 bg-white shadow-md rounded-2xl hidden md:block">
                        <div className="p-4 flex flex-col items-center justify-center">
                            <svg width="111" height="111" viewBox="0 0 111 111" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M110.083 55.4993C110.083 85.6449 85.6449 110.083 55.4993 110.083C25.3538 110.083 0.916016 85.6449 0.916016 55.4993C0.916016 25.3538 25.3538 0.916016 55.4993 0.916016C85.6449 0.916016 110.083 25.3538 110.083 55.4993ZM71.8743 39.1243C71.8743 48.168 64.543 55.4993 55.4993 55.4993C46.4557 55.4993 39.1243 48.168 39.1243 39.1243C39.1243 30.0807 46.4557 22.7493 55.4993 22.7493C64.543 22.7493 71.8743 30.0807 71.8743 39.1243ZM55.4993 101.895C65.2372 101.895 74.2744 98.8951 81.7371 93.7688C85.0332 91.5047 86.4418 87.1917 84.5255 83.682C80.5528 76.4061 72.3669 71.8743 55.499 71.8743C38.6314 71.8743 30.4454 76.406 26.4727 83.6816C24.5563 87.1913 25.9649 91.5043 29.2609 93.7685C36.7237 98.895 45.7611 101.895 55.4993 101.895Z" fill="#14813D" />
                            </svg>
                            <p className='mt-4'>{userData?.patient?.full_name}</p>
                            <p>{userData?.phone}</p>
                        </div>
                        <MenuSlider menuItems={menuItems} defaultActiveId="appointments" />
                    </div>
                    <div className="flex-1">
                        <div>
                            <h1 className="text-xl font-bold text-[#156634]">{locale === "en" ? "Booking History" : "Lịch sử đặt hẹn"} ({results.length})</h1>
                            <div className='border-b-[1px] mb-4 mt-2'></div>
                            {results.length > 0 ? (
                                <>
                                    {results.map((line: any) => (
                                        <div className="bg-white rounded-lg shadow-md p-6 mb-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <h3 className="text-base font-medium">{extractTextAfterKeyword(line.note)}</h3>
                                                <section className='flex items-center gap-2 justify-end'>
                                                    <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusColors[line.status as 'pending' | 'confirmed' | 'completed' | 'finished']}`}>
                                                        {line.status === 'pending' && 'Chờ xác nhận'}
                                                        {line.status === 'confirmed' && 'Đã xác nhận'}
                                                        {(line.status === 'completed' || line.status === 'finished') && 'Hoàn thành'}
                                                    </span>
                                                    {/* <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M10.0007 14.1667C10.9211 14.1667 11.6673 14.9129 11.6673 15.8333C11.6673 16.7538 10.9211 17.5 10.0007 17.5C9.08018 17.5 8.33398 16.7538 8.33398 15.8333C8.33398 14.9129 9.08018 14.1667 10.0007 14.1667Z" fill="#1C274C" />
                                                        <path d="M10.0007 8.33333C10.9211 8.33333 11.6673 9.07953 11.6673 10C11.6673 10.9205 10.9211 11.6667 10.0007 11.6667C9.08018 11.6667 8.33398 10.9205 8.33398 10C8.33398 9.07953 9.08018 8.33333 10.0007 8.33333Z" fill="#1C274C" />
                                                        <path d="M10.0007 2.5C10.9211 2.5 11.6673 3.24619 11.6673 4.16667C11.6673 5.08714 10.9211 5.83333 10.0007 5.83333C9.08018 5.83333 8.33398 5.08714 8.33398 4.16667C8.33398 3.24619 9.08018 2.5 10.0007 2.5Z" fill="#1C274C" />
                                                    </svg> */}
                                                </section>
                                            </div>
                                            <div className='border-b-[1px] my-2'></div>
                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="flex items-center gap-1">
                                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 8C15.5 12.1421 12.1421 15.5 8 15.5C3.85786 15.5 0.5 12.1421 0.5 8C0.5 3.85786 3.85786 0.5 8 0.5C12.1421 0.5 15.5 3.85786 15.5 8ZM10.25 5.75C10.25 6.99264 9.24264 8 8 8C6.75736 8 5.75 6.99264 5.75 5.75C5.75 4.50736 6.75736 3.5 8 3.5C9.24264 3.5 10.25 4.50736 10.25 5.75ZM7.99999 14.375C9.33803 14.375 10.5798 13.9628 11.6052 13.2584C12.0581 12.9473 12.2516 12.3547 11.9883 11.8724C11.4425 10.8727 10.3177 10.25 7.99995 10.25C5.68227 10.25 4.55748 10.8727 4.01161 11.8724C3.74828 12.3546 3.94183 12.9472 4.39472 13.2584C5.42014 13.9628 6.66192 14.375 7.99999 14.375Z" fill="#1F1F1F" />
                                                    </svg>

                                                    <span>{line.contactFullName}</span>
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M4.40163 2.94987L4.86134 3.77361C5.27621 4.51699 5.10967 5.49218 4.45626 6.1456C4.45625 6.1456 4.45625 6.1456 4.45625 6.14561C4.45614 6.14572 3.66378 6.93829 5.1007 8.37521C6.53696 9.81148 7.32949 9.02047 7.33031 9.01966C7.33033 9.01964 7.33032 9.01965 7.33034 9.01962C7.98376 8.36623 8.95893 8.1997 9.7023 8.61457L10.526 9.07428C11.6486 9.70075 11.7811 11.275 10.7945 12.2616C10.2016 12.8545 9.47528 13.3158 8.6724 13.3463C7.3208 13.3975 5.02545 13.0554 2.72296 10.753C0.420465 8.45046 0.078402 6.15511 0.129641 4.80351C0.160078 4.00063 0.621402 3.27433 1.21428 2.68145C2.20095 1.69478 3.77516 1.82735 4.40163 2.94987Z" fill="#1F1F1F" />
                                                        <path d="M6.68445 0.516443C6.73134 0.226813 7.00514 0.0303392 7.29477 0.077229C7.3127 0.0806607 7.37038 0.0914418 7.40061 0.0981729C7.46105 0.111633 7.54536 0.132358 7.65048 0.162976C7.8607 0.224207 8.15438 0.325076 8.50685 0.486673C9.21255 0.810207 10.1517 1.37617 11.1262 2.35069C12.1007 3.32522 12.6667 4.26434 12.9902 4.97003C13.1518 5.32251 13.2527 5.61619 13.3139 5.82641C13.3445 5.93153 13.3653 6.01584 13.3787 6.07628C13.3854 6.1065 13.3904 6.13077 13.3938 6.1487L13.3979 6.1708C13.4448 6.46043 13.2501 6.74554 12.9604 6.79243C12.6716 6.83919 12.3996 6.64366 12.3515 6.35539C12.35 6.34766 12.346 6.32686 12.3416 6.30725C12.3329 6.26802 12.3178 6.20581 12.2938 6.12353C12.2459 5.95896 12.1626 5.71435 12.0244 5.41283C11.7483 4.81053 11.2517 3.97881 10.3749 3.102C9.49807 2.22518 8.66636 1.72864 8.06406 1.45251C7.76254 1.31427 7.51792 1.23102 7.35335 1.18308C7.27108 1.15912 7.16767 1.13538 7.12844 1.12665C6.84016 1.07861 6.6377 0.805246 6.68445 0.516443Z" fill="#1F1F1F" />
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.84471 2.95981C6.92532 2.6777 7.21936 2.51435 7.50147 2.59495L7.35552 3.10576C7.50147 2.59495 7.50147 2.59495 7.50147 2.59495L7.50249 2.59524L7.50357 2.59556L7.50594 2.59625L7.51143 2.59788L7.52544 2.60225C7.53612 2.60567 7.54946 2.61012 7.56536 2.61576C7.59718 2.62705 7.63922 2.6431 7.69079 2.6652C7.79399 2.70943 7.93499 2.77773 8.10819 2.88023C8.45489 3.08542 8.92819 3.42626 9.48421 3.98228C10.0402 4.5383 10.3811 5.0116 10.5863 5.3583C10.6888 5.5315 10.7571 5.6725 10.8013 5.77569C10.8234 5.82727 10.8394 5.86931 10.8507 5.90112C10.8564 5.91703 10.8608 5.93036 10.8642 5.94104L10.8686 5.95506L10.8702 5.96055L10.8709 5.96291L10.8712 5.964C10.8712 5.964 10.8715 5.96502 10.3607 6.11096L10.8715 5.96502C10.9521 6.24713 10.7888 6.54117 10.5067 6.62177C10.227 6.70169 9.93551 6.54178 9.85202 6.26407L9.8494 6.25643C9.84562 6.24579 9.83781 6.22484 9.82469 6.19423C9.79848 6.13308 9.75096 6.03304 9.6719 5.89946C9.51396 5.6326 9.22871 5.22938 8.73291 4.73358C8.23711 4.23778 7.83389 3.95253 7.56703 3.79459C7.43345 3.71553 7.33341 3.668 7.27225 3.64179C7.24165 3.62868 7.2207 3.62086 7.21005 3.61709L7.20242 3.61447C6.92471 3.53097 6.7648 3.23953 6.84471 2.95981Z" fill="#1F1F1F" />
                                                    </svg>
                                                    <span>{line.contactPhoneNumber}</span>
                                                </div>
                                                <div>
                                                    <div className="flex items-center col-span-2 gap-1">
                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M2.16667 17.3327C1.70833 17.3327 1.31597 17.1695 0.989583 16.8431C0.663194 16.5167 0.5 16.1244 0.5 15.666V3.99935C0.5 3.54102 0.663194 3.14865 0.989583 2.82227C1.31597 2.49588 1.70833 2.33268 2.16667 2.33268H3V1.49935C3 1.26324 3.07986 1.06532 3.23958 0.905599C3.39931 0.745877 3.59722 0.666016 3.83333 0.666016C4.06944 0.666016 4.26736 0.745877 4.42708 0.905599C4.58681 1.06532 4.66667 1.26324 4.66667 1.49935V2.33268H11.3333V1.49935C11.3333 1.26324 11.4132 1.06532 11.5729 0.905599C11.7326 0.745877 11.9306 0.666016 12.1667 0.666016C12.4028 0.666016 12.6007 0.745877 12.7604 0.905599C12.9201 1.06532 13 1.26324 13 1.49935V2.33268H13.8333C14.2917 2.33268 14.684 2.49588 15.0104 2.82227C15.3368 3.14865 15.5 3.54102 15.5 3.99935V15.666C15.5 16.1244 15.3368 16.5167 15.0104 16.8431C14.684 17.1695 14.2917 17.3327 13.8333 17.3327H2.16667ZM2.16667 15.666H13.8333V7.33268H2.16667V15.666ZM8 10.666C7.76389 10.666 7.56597 10.5862 7.40625 10.4264C7.24653 10.2667 7.16667 10.0688 7.16667 9.83268C7.16667 9.59657 7.24653 9.39866 7.40625 9.23893C7.56597 9.07921 7.76389 8.99935 8 8.99935C8.23611 8.99935 8.43403 9.07921 8.59375 9.23893C8.75347 9.39866 8.83333 9.59657 8.83333 9.83268C8.83333 10.0688 8.75347 10.2667 8.59375 10.4264C8.43403 10.5862 8.23611 10.666 8 10.666ZM4.66667 10.666C4.43056 10.666 4.23264 10.5862 4.07292 10.4264C3.91319 10.2667 3.83333 10.0688 3.83333 9.83268C3.83333 9.59657 3.91319 9.39866 4.07292 9.23893C4.23264 9.07921 4.43056 8.99935 4.66667 8.99935C4.90278 8.99935 5.10069 9.07921 5.26042 9.23893C5.42014 9.39866 5.5 9.59657 5.5 9.83268C5.5 10.0688 5.42014 10.2667 5.26042 10.4264C5.10069 10.5862 4.90278 10.666 4.66667 10.666ZM11.3333 10.666C11.0972 10.666 10.8993 10.5862 10.7396 10.4264C10.5799 10.2667 10.5 10.0688 10.5 9.83268C10.5 9.59657 10.5799 9.39866 10.7396 9.23893C10.8993 9.07921 11.0972 8.99935 11.3333 8.99935C11.5694 8.99935 11.7674 9.07921 11.9271 9.23893C12.0868 9.39866 12.1667 9.59657 12.1667 9.83268C12.1667 10.0688 12.0868 10.2667 11.9271 10.4264C11.7674 10.5862 11.5694 10.666 11.3333 10.666ZM8 13.9994C7.76389 13.9994 7.56597 13.9195 7.40625 13.7598C7.24653 13.6 7.16667 13.4021 7.16667 13.166C7.16667 12.9299 7.24653 12.732 7.40625 12.5723C7.56597 12.4125 7.76389 12.3327 8 12.3327C8.23611 12.3327 8.43403 12.4125 8.59375 12.5723C8.75347 12.732 8.83333 12.9299 8.83333 13.166C8.83333 13.4021 8.75347 13.6 8.59375 13.7598C8.43403 13.9195 8.23611 13.9994 8 13.9994ZM4.66667 13.9994C4.43056 13.9994 4.23264 13.9195 4.07292 13.7598C3.91319 13.6 3.83333 13.4021 3.83333 13.166C3.83333 12.9299 3.91319 12.732 4.07292 12.5723C4.23264 12.4125 4.43056 12.3327 4.66667 12.3327C4.90278 12.3327 5.10069 12.4125 5.26042 12.5723C5.42014 12.732 5.5 12.9299 5.5 13.166C5.5 13.4021 5.42014 13.6 5.26042 13.7598C5.10069 13.9195 4.90278 13.9994 4.66667 13.9994ZM11.3333 13.9994C11.0972 13.9994 10.8993 13.9195 10.7396 13.7598C10.5799 13.6 10.5 13.4021 10.5 13.166C10.5 12.9299 10.5799 12.732 10.7396 12.5723C10.8993 12.4125 11.0972 12.3327 11.3333 12.3327C11.5694 12.3327 11.7674 12.4125 11.9271 12.5723C12.0868 12.732 12.1667 12.9299 12.1667 13.166C12.1667 13.4021 12.0868 13.6 11.9271 13.7598C11.7674 13.9195 11.5694 13.9994 11.3333 13.9994Z" fill="#1F1F1F" />
                                                        </svg>
                                                        <span className='text-base font-medium'>{formatDateHistory(line.bookingDate, "DD/MM/YYYY")}</span>
                                                    </div>
                                                    <div className="flex items-center col-span-2 mt-1">
                                                        <span className="ml-5 text-base">{formatDate(line.bookingDate, "H:mm")}</span>
                                                    </div>
                                                </div>
                                                <div >
                                                    <div className="flex items-center col-span-2 gap-1">
                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M6 12.8938C5.85 12.8938 5.7 12.8688 5.55 12.8188C5.4 12.7688 5.2625 12.6938 5.1375 12.5938C3.6625 11.4188 2.5625 10.2719 1.8375 9.15313C1.1125 8.03438 0.75 6.9375 0.75 5.8625C0.75 4.3 1.2375 3.01563 2.2125 2.00938C3.1875 1.00313 4.45 0.5 6 0.5C7.55 0.5 8.8125 1.00313 9.7875 2.00938C10.7625 3.01563 11.25 4.3 11.25 5.8625C11.25 6.9375 10.8875 8.03438 10.1625 9.15313C9.4375 10.2719 8.3375 11.4188 6.8625 12.5938C6.7375 12.6938 6.6 12.7688 6.45 12.8188C6.3 12.8688 6.15 12.8938 6 12.8938ZM6 7.25C6.4125 7.25 6.76562 7.10313 7.05938 6.80938C7.35313 6.51562 7.5 6.1625 7.5 5.75C7.5 5.3375 7.35313 4.98438 7.05938 4.69063C6.76562 4.39688 6.4125 4.25 6 4.25C5.5875 4.25 5.23438 4.39688 4.94063 4.69063C4.64688 4.98438 4.5 5.3375 4.5 5.75C4.5 6.1625 4.64688 6.51562 4.94063 6.80938C5.23438 7.10313 5.5875 7.25 6 7.25ZM1.5 15.5C1.2875 15.5 1.10938 15.4281 0.965625 15.2844C0.821875 15.1406 0.75 14.9625 0.75 14.75C0.75 14.5375 0.821875 14.3594 0.965625 14.2156C1.10938 14.0719 1.2875 14 1.5 14H10.5C10.7125 14 10.8906 14.0719 11.0344 14.2156C11.1781 14.3594 11.25 14.5375 11.25 14.75C11.25 14.9625 11.1781 15.1406 11.0344 15.2844C10.8906 15.4281 10.7125 15.5 10.5 15.5H1.5Z" fill="#1F1F1F" />
                                                        </svg>
                                                        <span className='text-base font-medium'>{handleBranch(line.branch, locale)}</span>
                                                    </div>
                                                    <div className="flex items-center col-span-2 mt-1">
                                                        <span className="ml-5 text-base">{handleAddress(line.branch, locale)}</span>
                                                    </div>
                                                </div>

                                            </div>
                                            <div className='border-b-[1px] my-4'></div>
                                            <div className="mt-4 flex items-center justify-between">
                                                <p className="text-[#8E8E8E] text-sm italic">
                                                    {locale === "en" ? "Created Date" : "Ngày tạo"}: {formatDateHistory(line.createdAt, "DD/MM/YYYY")}
                                                </p>
                                                <a href="tel:1900638408" className="text-[#156634] text-sm italic">
                                                    {locale === "en" ? "Contact" : "Liên hệ hỗ trợ"}: 1900 638 408
                                                </a>
                                            </div>
                                        </div>
                                    ))}
                                </>
                            ) : (
                                <div className="container mx-auto">
                                    <div className="p-10">
                                        <svg className="mx-auto" xmlns="http://www.w3.org/2000/svg" width="168" height="164" viewBox="0 0 168 164" fill="none">
                                            <g filter="url(#filter0_d_14133_736)">
                                                <path d="M3.99988 81.0083C3.99988 36.7097 39.9078 1 84.0081 1C128.042 1 164 36.6932 164 81.0083C164 99.8046 157.525 117.098 146.657 130.741C131.676 149.653 108.784 161 84.0081 161C59.0675 161 36.3071 149.57 21.3427 130.741C10.4745 117.098 3.99988 99.8046 3.99988 81.0083Z" fill="white" />
                                            </g>
                                            <path d="M145.544 77.4619H146.044V76.9619V48.9851C146.044 43.424 141.543 38.9227 135.982 38.9227H67.9223C64.839 38.9227 61.9759 37.3578 60.3174 34.7606L60.3159 34.7583L56.8477 29.3908L56.8472 29.3901C54.9884 26.5237 51.8086 24.7856 48.3848 24.7856H26.4195C20.8584 24.7856 16.3571 29.287 16.3571 34.848V76.9619V77.4619H16.8571H145.544Z" fill="white" stroke="black" />
                                            <path d="M63.9999 26.2856C63.9999 25.7334 64.4476 25.2856 64.9999 25.2856H141.428C143.638 25.2856 145.428 27.0765 145.428 29.2856V33.8571H67.9999C65.7907 33.8571 63.9999 32.0662 63.9999 29.8571V26.2856Z" fill="black" />
                                            <ellipse cx="1.42857" cy="1.42857" rx="1.42857" ry="1.42857" transform="matrix(-1 0 0 1 46.8571 31)" fill="black" />
                                            <ellipse cx="1.42857" cy="1.42857" rx="1.42857" ry="1.42857" transform="matrix(-1 0 0 1 38.2859 31)" fill="black" />
                                            <ellipse cx="1.42857" cy="1.42857" rx="1.42857" ry="1.42857" transform="matrix(-1 0 0 1 29.7141 31)" fill="black" />
                                            <path d="M148.321 126.907L148.321 126.906L160.559 76.3043C162.7 67.5161 156.036 59.0715 147.01 59.0715H14.5902C5.56258 59.0715 -1.08326 67.5168 1.04059 76.3034L1.04064 76.3036L13.2949 126.906C14.9181 133.621 20.9323 138.354 27.8354 138.354H133.764C140.685 138.354 146.681 133.621 148.321 126.907Z" fill="white" stroke="black" />
                                            <path d="M86.3858 109.572C85.2055 109.572 84.2268 108.593 84.2268 107.384C84.2268 102.547 76.9147 102.547 76.9147 107.384C76.9147 108.593 75.9359 109.572 74.7269 109.572C73.5466 109.572 72.5678 108.593 72.5678 107.384C72.5678 96.7899 88.5737 96.8186 88.5737 107.384C88.5737 108.593 87.5949 109.572 86.3858 109.572Z" fill="black" />
                                            <path d="M104.954 91.0616H95.9144C94.7053 91.0616 93.7265 90.0829 93.7265 88.8738C93.7265 87.6935 94.7053 86.7147 95.9144 86.7147H104.954C106.163 86.7147 107.141 87.6935 107.141 88.8738C107.141 90.0829 106.163 91.0616 104.954 91.0616Z" fill="black" />
                                            <path d="M65.227 91.0613H56.1877C54.9787 91.0613 53.9999 90.0825 53.9999 88.8734C53.9999 87.6931 54.9787 86.7144 56.1877 86.7144H65.227C66.4073 86.7144 67.3861 87.6931 67.3861 88.8734C67.3861 90.0825 66.4073 91.0613 65.227 91.0613Z" fill="black" />
                                            <circle cx="142.572" cy="121" r="24.7857" fill="white" stroke="black" />
                                            <path d="M152.214 130.643L149.535 127.964M150.071 119.928C150.071 115.195 146.234 111.357 141.5 111.357C136.766 111.357 132.928 115.195 132.928 119.928C132.928 124.662 136.766 128.5 141.5 128.5C143.858 128.5 145.993 127.548 147.543 126.007C149.104 124.455 150.071 122.305 150.071 119.928Z" stroke="black" stroke-width="1.6" stroke-linecap="round" />
                                            <defs>
                                                <filter id="filter0_d_14133_736" x="1.99988" y="0" width="164" height="164" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                                    <feOffset dy="1" />
                                                    <feGaussianBlur stdDeviation="1" />
                                                    <feComposite in2="hardAlpha" operator="out" />
                                                    <feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0" />
                                                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_14133_736" />
                                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_14133_736" result="shape" />
                                                </filter>
                                            </defs>
                                        </svg>
                                        <h1 className="text-xl text-gray-600 text-center font-bold">
                                            {locale === "en" ? "No information" : "Không có thông tin"}
                                        </h1>
                                        <div className="w-full flex justify-center md:justify-end items-end mt-12">
                                            <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                                                <span className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-full hover:bg-green-700 font-bold">{locale == "en" ? "Back to home" : "Quay lại trang chủ"}</span>
                                            </LinkComponent>
                                        </div>
                                    </div>
                                </div>
                            )
                            }
                        </div>
                    </div>
                </div>
            </div>
            <Contact />
        </>
    );
};

export default BookingHistory

function extractTextAfterKeyword(text: string) {
    if (!text) return "";
    const keyword = "Khách hàng đặt lịch khám ở phòng khám về gói"
    const keywordIndex = text.indexOf(keyword);
    if (keywordIndex !== -1) {
        return text.slice(keywordIndex + keyword.length).trim();
    }
    return "";
}