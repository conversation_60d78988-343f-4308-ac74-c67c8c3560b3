import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import { ORDER_STATUS } from '../../constants/Order'
import LinkComponent from '../../components/Link';
import React from 'react';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }


const OrderSuccess = () => {
  const router = useRouter()
  const { code } = router.query;
  const [data, setData] = useState<any>({ status: "" });
  const locale = router.query.locale as string || 'vi';
  const [cartLines, setCartLines] = useState([]);
  const [completed, setCompleted] = useState(false);
  useEffect(() => {
    if (code) {
      axios.get('https://api.echomedi.com' + '/api/orders/getOrderDetailByCode/' + code)
        .then(function (response) {
          if (response.data.order.status === ORDER_STATUS.COMPLETED) {
            setCartLines(response.data.order.cart.cart_lines);
            toast.success('Thành công');
            setData(response.data.order);
            setCompleted(true)
          }
          else if (response.data.order.status === ORDER_STATUS.CANCELED) {
            setCompleted(false);
          }
          else {
            setCartLines(response.data.order.products.lines);
            toast.success('Thành công');
            setData(response.data.order);
            setCompleted(true)
          }
        })
        .catch(function (error) {
          if (error.response?.status == 401) {
            toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
            localStorage.removeItem("token");
            window.location.href = '/login';
          }
        });
    }
  }, [code]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);
  return <>
    <Head>
      <title>Order Success - ECHO MEDI</title>
      <meta
        name="description"
        content="Review your past orders at ECHO MEDI. Check the status, reorder products, or view details of previous purchases."
      />
      <meta name="keywords" content="ECHO MEDI, order success, past orders, purchase details, reorder, medical products" />
      <meta property="og:title" content="Order Success - ECHO MEDI" />
      <meta
        property="og:description"
        content="Access your ECHO MEDI order success and manage your previous purchases easily."
      />
      <link rel="icon" href="/favicon1.png" />
    </Head>
    <div className="container mx-auto my-12 ">
      <div className="p-10 md:px-16 px-4">
        <div className="w-full">
          {completed ? (
            <>
              <section className='bg-[#FFFFFF] p-10'>
                <div className="flex items-center justify-center relative mt-4">
                  {loading ? (
                    <div className="w-16 h-16 border-4 border-t-4 border-[#156634] border-t-transparent rounded-full absolute animate-spin"></div>
                  ) : (
                    <svg viewBox="0 0 24 24" className="text-[#156634] w-16 h-16 mx-auto absolute">
                      <path fill="currentColor" d="M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"></path>
                    </svg>
                  )}
                </div>
                <h1 className='text-center font-bold text-xl mt-12'>{locale == "vi" ? "Quý khách đã mua hàng thành công" : "You have successfully purchased the product."}
                </h1>
                <p className='text-base text-center max-w-3xl mx-auto'>
                  {locale == "vi" ? "Quý khách đã mua thành công" : "You have successfully purchased"}  <span className="font-bold">
                    {cartLines.map((line: any, index: number) =>
                      locale === "en" ? line.service.en_label : line.service.label
                    ).join(", ")}
                  </span>.
                  {locale == "vi" ? "Bộ phận chăm sóc khách hàng sẽ liên hệ xác nhận và hỗ trợ thêm thông tin. Để dễ theo dõi chi tiết, bạn có thể tải app của ECHO MEDI. Cảm ơn bạn đã tin tưởng và lựa chọn dịch vụ của chúng tôi!" : "The customer service department will contact you to confirm and provide further support and information. To easily track the details, you can download the ECHO MEDI app. Thank you for trusting and choosing our service!"}
                </p>
                <div>
                </div>
              </section>
              <div className="w-full flex justify-center md:justify-end items-end mt-12">
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-full hover:bg-green-700 font-bold">{locale == "en" ? "Back to home" : "Quay lại trang chủ"}</span>
                </LinkComponent>
              </div>
            </>
          ) : (
            <>
              <section className='bg-[#FFFFFF] p-10'>
                <div className="flex items-center justify-center relative mt-4">
                  {loading ? (
                    <div className="w-16 h-16 border-4 border-t-4 border-[#156634] border-t-transparent rounded-full absolute animate-spin"></div>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 48 48">
                      <path fill="#f44336" d="M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z"></path><path fill="#fff" d="M29.656,15.516l2.828,2.828l-14.14,14.14l-2.828-2.828L29.656,15.516z"></path><path fill="#fff" d="M32.484,29.656l-2.828,2.828l-14.14-14.14l2.828-2.828L32.484,29.656z"></path>
                    </svg>
                  )}
                </div>

                <h1 className='text-center font-bold text-xl mt-12'>{locale == "vi" ? "Giao dịch không thành công" : "Transaction failed"}</h1>
              </section>
              <div className="w-full flex justify-center md:justify-end items-end mt-12">
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-full hover:bg-green-700 font-bold">{locale == "en" ? "Back to home" : "Quay lại trang chủ"}</span>
                </LinkComponent>
              </div>
            </>
          )}
        </div>
      </div>

    </div>
    <Contact />
  </>
}

export default OrderSuccess

function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function getDisplayedStatus(x: string) {
  switch (x) {
    case "canceled":
      return "Chưa thanh toán";
    case "done":
      return "Đã thanh toán";
    case "draft":
      return "Chưa thanh toán";
    case "ordered":
      return "Đã thanh toán";
  }
}

