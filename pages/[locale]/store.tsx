import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import React from 'react';
import { Toaster } from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import LinkComponent from "../../components/Link";
import Image from "next/image";
import { shimmer, toBase64 } from "../../lib/ui";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';

    return (
        <>
            <Head>
                <title>ECHO MEDI - {locale == "vi" ? "Hệ thống y tế toàn diện cho bạn và gia đình" : "Comprehensive healthcare system for you and your family"}</title>
                <meta
                    name="ECHO MEDI"
                    content="ECHO MEDI"
                />
                <meta name="keywords" content="ECHO MEDI"></meta>
                <link rel="icon" href="/favicon1.png" />
            </Head>
            <div className="mx-auto">
                <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                    <div className="w-full relative">
                        <Image
                            src="/banner/banner_new.webp"
                            alt="Banner"
                            width={1920}
                            height={300}
                            placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                            className="object-center"
                            layout="responsive"
                        />
                    </div>
                    <div className="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 2xl:w-2/3 absolute md:px-16 px-4">
                        <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block">
                            {locale === "vi" ? "NHÀ THUỐC" : "PHARMACY"}
                        </h2>
                        <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                            {locale === "vi" ? "NHÀ THUỐC" : "PHARMACY"}
                        </h2>
                        <p className="text-base mb-8 text-justify hidden md:block">
                            {locale == "en" ? "ECHO MEDI offers products and services tailored to support each individual’s unique health journey. Through face-to-face counseling, our pharmacists help ensure medication adherence, close gaps in care, and recommend cost-effective drug therapies. Our standalone pharmacy provides customers with convenience and value, offering a wide range of health, wellness, personal care, and beauty products, along with an extensive selection of wellness items." : "ECHO MEDI cung cấp các sản phẩm và dịch vụ phù hợp với nhu cầu sức khỏe riêng biệt của mỗi người. Thông qua tư vấn trực tiếp, các dược sĩ của chúng tôi sẽ đảm bảo thuốc được sử dụng an toàn, hợp lý và hiệu quả, rút ngắn quá trình điều trị và đưa ra nhiều sự lựa chọn thuốc hiệu quả, hợp túi tiền. Nhà thuốc ECHO MEDI không chỉ mang đến cho bạn sự tiện lợi thông qua giá trị gói sản phẩm chăm sóc sức khỏe mà còn cung cấp đa dạng sản phẩm làm đẹp, thực phẩm chức năng và chăm sóc sức khoẻ toàn diện."}
                        </p>
                    </div>
                </div>
            </div>
            <div className="2xl:container 2xl:mx-auto">
                <p className="text-sm my-4 text-justify px-4 block md:hidden">
                    {locale == "en" ? "ECHO MEDI offers products and services tailored to support each individual’s unique health journey. Through face-to-face counseling, our pharmacists help ensure medication adherence, close gaps in care, and recommend cost-effective drug therapies. Our standalone pharmacy provides customers with convenience and value, offering a wide range of health, wellness, personal care, and beauty products, along with an extensive selection of wellness items." : "ECHO MEDI cung cấp các sản phẩm và dịch vụ phù hợp với nhu cầu sức khỏe riêng biệt của mỗi người. Thông qua tư vấn trực tiếp, các dược sĩ của chúng tôi sẽ đảm bảo thuốc được sử dụng an toàn, hợp lý và hiệu quả, rút ngắn quá trình điều trị và đưa ra nhiều sự lựa chọn thuốc hiệu quả, hợp túi tiền. Nhà thuốc ECHO MEDI không chỉ mang đến cho bạn sự tiện lợi thông qua giá trị gói sản phẩm chăm sóc sức khỏe mà còn cung cấp đa dạng sản phẩm làm đẹp, thực phẩm chức năng và chăm sóc sức khoẻ toàn diện."}
                </p>
                <section className="max-w-screen-2xl mx-auto">
                    <div className="md:px-16 px-4">
                        <h2 className="text-start md:text-[26px] text-2xl text-[#156634] uppercase font-bold mt-10">{locale == "en" ? "Monthly Packages" : "Gói Chăm Sóc Hàng Tháng"}</h2>
                        <section className="hidden md:block">
                            <div className="grid grid-rows-none md:grid-cols-3 gap-8 mt-8">
                                {healthPackages.map((packageItem, index) => (
                                    <LinkComponent
                                        key={index}
                                        skipLocaleHandling={undefined}
                                        locale={locale}
                                        href={packageItem.href}
                                    >
                                        <div className="rounded-xl overflow-hidden shadow-lg relative h-[300px] w-full">
                                            <img
                                                className="h-full w-full absolute object-cover"
                                                src={packageItem.image}
                                                alt={packageItem.alt}
                                            />
                                            <div
                                                style={{
                                                    backgroundImage: "linear-gradient(180deg, #FFFFFF00 0%, #426045 100%)",
                                                }}
                                                className="px-6 py-4 relative z-10 h-[300px] flex justify-end flex-col"
                                            >
                                                <div className="text-base mb-2 text-white">
                                                    <p>
                                                        {locale === "en" ? packageItem.title.en : packageItem.title.vi}{" "}
                                                        {packageItem.ageRange}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </LinkComponent>
                                ))}
                            </div>
                        </section>
                        <section className="block md:hidden">
                            <div className="swiper slide mt-6">
                                <Swiper
                                    slidesPerView={1.5}
                                    spaceBetween={10}
                                    loop={true}
                                    autoplay={{
                                        delay: 10000,
                                        disableOnInteraction: false,
                                    }}
                                    centeredSlides={true}
                                    pagination={{
                                        clickable: true,
                                    }}
                                    modules={[Autoplay, Pagination, Navigation]}
                                    className="mySwiper"
                                >
                                    {healthPackages.map((packageItem, index) => (
                                        <SwiperSlide key={index}>
                                            <LinkComponent
                                                skipLocaleHandling={undefined}
                                                locale={locale}
                                                href={packageItem.href}
                                            >
                                                <div className="group cursor-pointer w-full rounded-2xl p-3 bg-white">
                                                    <div className="flex items-center mb-2 h-[200px] w-full">
                                                        <img
                                                            src={packageItem.image}
                                                            alt={packageItem.alt}
                                                            className="rounded-lg object-cover w-full h-full"
                                                        />
                                                    </div>
                                                    <div className="block">
                                                        <h4 className="text-black font-medium leading-8">
                                                            {locale === "en" ? packageItem.title.en : packageItem.title.vi}{" "}
                                                            {packageItem.ageRange}
                                                        </h4>
                                                    </div>
                                                </div>
                                            </LinkComponent>
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                        </section>
                        <h2 className="text-start md:text-[26px] text-2xl text-[#156634] uppercase font-bold mt-10">{locale == "en" ? "Health Concern" : "Dựa Trên Vấn Đề Sức Khỏe"}</h2>
                        <section className="hidden md:block">
                            <div className="grid grid-rows-none md:grid-cols-4 pt-4 gap-4">
                                {products.map((product, index) => (
                                    <LinkComponent key={index} skipLocaleHandling={undefined} locale={locale} href={product.href}>
                                        <div className="group cursor-pointer w-full rounded-2xl p-3 bg-white">
                                            <div className="flex items-center mb-6">
                                                <img
                                                    src={product.imageSrc}
                                                    alt={locale === "en" ? product.titleEn : product.titleVi}
                                                    className="rounded-lg w-full object-cover h-[200px]"
                                                />
                                            </div>
                                            <div className="block">
                                                <h4 className="text-black font-medium leading-8">
                                                    {locale === "en" ? product.titleEn : product.titleVi}
                                                </h4>
                                            </div>
                                        </div>
                                    </LinkComponent>
                                ))}
                            </div>
                        </section>
                        <section className="block md:hidden pt-4">
                            <div className="swiper slide flex justify-center gap-y-4 lg:gap-y-0 flex-wrap md:flex-wrap lg:flex-nowrap lg:flex-row lg:justify-between">
                                <Swiper
                                    slidesPerView={1.5}
                                    spaceBetween={10}
                                    loop={true}
                                    autoplay={{
                                        delay: 10000,
                                        disableOnInteraction: false,
                                    }}
                                    centeredSlides={true}
                                    pagination={{
                                        clickable: true,
                                    }}
                                    modules={[Autoplay, Pagination, Navigation]}
                                    className="mySwiper"
                                >
                                    {products.map((product, index) => (
                                        <SwiperSlide>
                                            <LinkComponent key={index} skipLocaleHandling={undefined} locale={locale} href={product.href}>
                                                <div className="group cursor-pointer w-full rounded-2xl p-3 bg-white">
                                                    <div className="flex items-center mb-4">
                                                        <img
                                                            src={product.imageSrc}
                                                            alt={locale === "en" ? product.titleEn : product.titleVi}
                                                            className="rounded-lg w-full object-cover !h-[220px]"
                                                        />
                                                    </div>
                                                    <div className="block">
                                                        <h4 className="text-black font-medium leading-8">
                                                            {locale === "en" ? product.titleEn : product.titleVi}
                                                        </h4>
                                                    </div>
                                                </div>

                                            </LinkComponent>
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                        </section>
                    </div>
                </section>
            </div>
            <Contact />
            <Toaster
                position="bottom-center"
            />
        </>
    );
};

export default Home;

const products = [
    {
        href: "/products/goi-ho-tro-giac-ngu/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/pexels_anna_nekrashevich_6604845_1_50a33e0e4b.jpg?updated_at=2023-01-07T04:30:11.607Z",
        titleEn: "Sleep",
        titleVi: "Ngủ Ngon",
    },
    {
        href: "/products/goi-ho-tro-cai-thuoc-la/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/pexels_victor_dompablo_5987483_1_6581fe8c56.jpg?updated_at=2023-01-07T04:30:46.194Z",
        titleEn: "Smoking Cessation",
        titleVi: "Cai Thuốc lá",
    },
    {
        href: "/products/goi-ho-tro-giam-can/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/pexels_andres_ayrton_6550832_1_374e48ace6.jpg?updated_at=2023-01-07T04:31:05.116Z",
        titleEn: "Weight Loss",
        titleVi: "Giảm Cân",
    },
    {
        href: "/products/goi-cham-soc-da-va-ngan-ngua-lao-hoa/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Cham_Soc_Da_scaled_pvzpoq8eqktfhlxiwekhtmzaagt62r080r633b2fko_c174abcfbe.jpg?updated_at=2023-01-07T04:31:41.546Z",
        titleEn: "Skin Care & Anti-aging",
        titleVi: "Chăm sóc da",
    },
    {
        href: "/products/goi-cham-soc-va-phuc-hoi-toc-mong/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Cham_Soc_Toc_scaled_pvzpopakjqs55zyw1w5v957tp2xsv1whomilm13tqw_403804b315.jpg?updated_at=2023-01-07T04:31:59.982Z",
        titleEn: "Hair & Nails Treatment",
        titleVi: "Chăm Sóc Tóc & Móng",
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-phu-nu-mang-thai/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/10_dau_hieu_co_thai_de_nhan_biet_nhat_f4b2e81bd3.jpg?updated_at=2023-05-22T07:06:29.452Z",
        titleEn: "Pregnancy Care",
        titleVi: "Mang thai",
    },
    {
        href: "/products/goi-suc-khoe-sinh-ly-nam/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Untitled_design_2_1_2cdb30587f.jpg?updated_at=2023-01-07T04:32:41.436Z",
        titleEn: "Men Sexual Health",
        titleVi: "Sinh lý nam",
    },
    {
        href: "/products/goi-suc-khoe-sinh-ly-nu/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Untitled_design_3_1_c76664bbdc.jpg?updated_at=2023-01-07T04:33:02.501Z",
        titleEn: "Women Sexual Health",
        titleVi: "Sinh lý nữ",
    },
    {
        href: "/products/goi-ho-tro-suc-khoe-tim-mach/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Untitled_design_4_1_ff8bb621b2.jpg?updated_at=2023-01-07T04:33:20.962Z",
        titleEn: "Heart & Blood Circulation",
        titleVi: "Tim mạch",
    },
    {
        href: "/products/goi-ho-tro-tieu-hoa/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Tieu_Hoa_scaled_pvzpor68xeupt7w5qwz4e4qqvuojag3ycvtkkl11eg_952c000f7b.jpg?updated_at=2023-01-07T04:32:16.881Z",
        titleEn: "Digestive System",
        titleVi: "Tiêu hoá"
    },
    {
        href: "/products/goi-phong-ngua-benh-xuong-khop/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Untitled_design_5_1_3239dc73a0.jpg?updated_at=2023-01-07T04:33:59.383Z",
        titleEn: "Bone & Joint Health",
        titleVi: "Xương khớp"
    },
    {
        href: "/products/goi-tang-suc-de-khang-va-mien-dich/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Untitled_design_6_1_4dc08fd0db.jpg?updated_at=2023-01-07T04:34:15.831Z",
        titleEn: "Immune System",
        titleVi: "Đề kháng và miễn dịch"
    },
    {
        href: "/products/goi-cai-thien-tri-nao/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Than_Kinh_Nao_scaled_pvzpoq8eqktfhlxiwekhtmzaagt62r080r633b2fko_357f43bfb5.jpg?updated_at=2023-01-07T04:34:38.081Z",
        titleEn: "Brain Health",
        titleVi: "Thần Kinh - Não"
    },
    {
        href: "/products/goi-giai-doc-tang-cuong-chuc-nang-gan",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/20201210_075414_292977_20170405103421_gan_kh_max_800x800_343e15df03.jpg?updated_at=2023-05-22T07:04:38.533Z",
        titleEn: "Liver",
        titleVi: "Gan"
    },
    {
        href: "/products/goi-cham-soc-phu-khoa/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/20190415_125212_673311_kham_phu_khoa_1_max_1800x1800_1f4b730ca2.png?updated_at=2023-05-22T07:08:03.996Z",
        titleEn: "Gynecological",
        titleVi: "Phụ Khoa"
    },
    {
        href: "/products/goi-ho-tro-benh-dau-da-day/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/20190927_200926_832533_viem_loet_da_day_max_800x800_23e33b24b5.jpg?updated_at=2023-05-22T07:11:18.092Z",
        titleEn: "Stomach",
        titleVi: "Dạ Dày"
    },
    {
        href: "/products/goi-ho-tro-tang-cuong-chuc-nang-phoi/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/nam_dau_hieu_khi_hut_thuoc_canh_bao_ung_thu_phoi_xuat_hien_3_crop_1619307858600_a3fdb1d777.webp?updated_at=2023-05-22T07:12:55.192Z",
        titleEn: "Lung",
        titleVi: "Phổi"
    },
    {
        href: "/products/goi-ho-tro-benh-tieu-duong/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/benh_tieu_duong_9054d8d79e.jpg?updated_at=2023-05-22T07:15:02.848Z",
        titleEn: "Diabetes",
        titleVi: "Tiểu Đường"
    },
    {
        href: "/products/goi-ho-tro-benh-gout/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/tu_a_den_z_nhung_dieu_can_biet_ve_benh_gout_1_1523690268161256377735_a14d4f8f12.webp?updated_at=2023-05-22T07:17:08.620Z",
        titleEn: "Gout",
        titleVi: "Gout"
    },
    {
        href: "/products/goi-ho-tro-benh-gan-nhiem-mo/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/tham_lang_nhu_gan_nhiem_mo_nguyen_nhan_trieu_chung_va_cach_dieu_tri_6924a14362624fe59daf3abe512f4da2_3060f9e84f.jpg?updated_at=2023-05-22T07:18:44.598Z",
        titleEn: "Fatty Liver",
        titleVi: "Gan Nhiễm Mỡ"
    },
    {
        href: "/products/goi-ho-tro-giam-cang-thang-stress",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/stress_2_800x450_df8cfdf3e5.jpg?updated_at=2023-05-22T07:20:09.609Z",
        titleEn: "Stress",
        titleVi: "Căng Thẳng"
    },
    {
        href: "/products/goi-ho-tro-benh-phi-dai-tuyen-tien-liet/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/ung_thu_tuyen_tien_liet_3a63511efd.jpg?updated_at=2023-05-22T07:22:04.827Z",
        titleEn: "Prostate Gland",
        titleVi: "Tuyến Tiền Liệt"
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-doi-mat/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/cham_soc_mat_31126388f4.webp?updated_at=2023-05-22T07:23:32.874Z",
        titleEn: "Eye",
        titleVi: "Mắt"
    },
    {
        href: "/products/goi-ho-tro-benh-dai-trang/",
        imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/2_1498103256892_15295570745901465220946_60_0_734_1200_crop_1529557080614304404680_8_0da526ef1e.jpg?updated_at=2023-05-22T07:24:52.467Z",
        titleEn: "Colon",
        titleVi: "Đại Tràng"
    }

];

const healthPackages = [
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-tre-em-tuoi-6-12/",
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Tre_Em_e0fe04e60e.png",
        alt: "Child",
        title: { en: "Child", vi: "Trẻ em" },
        ageRange: "(6 - 12)"
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-thanh-thieu-nien-tuoi-13-19/",
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_Thieu_Nien_ce1f63e70b.png",
        alt: "Teenager",
        title: { en: "Teenager", vi: "Thanh thiếu niên" },
        ageRange: "(13 - 19)"
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-nguoi-truong-thanh-tuoi-18-45/",
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Nguoi_Truong_Thanh_6340bc1000.png",
        alt: "Adult",
        title: { en: "Adult", vi: "Người trưởng thành" },
        ageRange: "(18 - 45)"
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-nu-gioi-do-tuoi-trung-nien-tuoi-45/",
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Nu_Trung_Nien_26afa510de.png",
        alt: "Middle-aged Woman",
        title: { en: "Middle-aged Woman", vi: "Nữ trung niên" },
        ageRange: "(45+)"
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-nam-gioi-do-tuoi-trung-nien-tuoi-45/",
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Nam_Trung_Nien_8210d4345f.png",
        alt: "Middle-aged Man",
        title: { en: "Middle-aged Man", vi: "Nam trung niên" },
        ageRange: "(45+)"
    },
    {
        href: "/products/goi-cham-soc-suc-khoe-cho-nguoi-lon-tuoi-tuoi-60/",
        image: "https://d3e4m6b6rxmux9.cloudfront.net/Nguoi_Lon_Tuoi_64e22b31a6.png",
        alt: "Elderly",
        title: { en: "Elderly", vi: "Người lớn tuổi" },
        ageRange: "(60+)"
    }
];
