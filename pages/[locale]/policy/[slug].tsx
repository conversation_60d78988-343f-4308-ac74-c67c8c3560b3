import type {
    InferGetStaticPropsType,
  } from 'next';
  import Head from "next/head";
  import Contact from "../../../components/Contact/Contact";
  import { useRouter } from 'next/router'
  import React from "react";
  import parse from 'html-react-parser';
  
  import { getStaticPathsServicesPolicy, getStaticPropsService } from '../../../lib/getStatic';
  export { getStaticPathsServicesPolicy as getStaticPaths, getStaticPropsService as getStaticProps };
  
  const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsService>) => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';

    return (
      <>
        <Head>
          <title>{locale === "en" ? props.en_label : props.label}</title>
          <meta
            name="description"
            content={locale == "en" ? (props.en_label ?? "") : (props.label ?? "")}
          />
          <meta name="keywords" content="ECHO MEDI"></meta>
          <link rel="icon" href="/favicon1.png" />
        </Head>
        <main className="pt-4 pb-16 lg:pt-16 lg:pb-24 bg-white container m-auto">
          <div className='lg:pl-[200px] lg:pr-[200px] w-full pl-2 pr-2 justify-between'>
            <h2><a className="text-gray-700 font-bold uppercase">{locale == "en" ? (props.en_label ?? "") : (props.label ?? "")}</a></h2>
            <hr className="h-px my-1 mb-8 bg-gray-200 border-0 dark:bg-gray-700"/>
            <div className='leading-8'>{parse(locale == "en" ? (props.en_detail ?? "") : (props.detail ?? ""))}</div>
          </div>
        </main>
        <Contact />
      </>
    );
  };
  
  export default Blog;
  