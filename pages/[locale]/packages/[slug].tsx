import type {
  InferGetStaticPropsType,
} from 'next';
import React from 'react';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import Accordion from '../../../components/Accordion';
import { useState, useEffect } from 'react';
import Image from 'next/image'
import { toBase64, shimmer, addToCart } from "../../../lib/ui";
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { getStaticPathsPackages, getStaticPropsPackage } from '../../../lib/getStatic';
import {
  getStaticPathsServices,
  getStaticPropsService,
} from "../../../lib/getStatic";
import LinkComponent from '../../../components/Link';
import parse from 'html-react-parser';
export { getStaticPathsPackages as getStaticPaths, getStaticPropsPackage as getStaticProps };
const api_endpoint = "https://api.echomedi.com";

const tranlsate = (s: string, locale: string | undefined) => {
  switch (s) {
    case "buy_now":
      if (locale === "en")
        return "Buy Now";
      else
        return "Mua ngay";
    case "learn_more":
      if (locale === "en")
        return "Learn More";
      else
        return "Tìm hiểu thêm";
  }
  return "";
}
const detectMob = () => {
  const toMatch = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i
  ];

  return toMatch.some((toMatchItem) => {
    return navigator.userAgent.match(toMatchItem);
  });
}

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsPackage>) => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [active, setActive] = useState(-1);
  const [category, setCategory] = useState('tintuc');
  const [cnt, setCnt] = useState(detectMob() ? 2 : 4);
  const [showLoadAllButton, setShowLoadAllButton] = useState(true);
  const [showHideButton, setShowHideButton] = useState(false);

  const handleLoadAll = () => {
    setCnt(-1);
    setShowLoadAllButton(false); 
    setShowHideButton(true);
  };

  const handleHide = () => {
    setCnt(detectMob() ? 2 : 4); 
    setShowLoadAllButton(true); 
    setShowHideButton(false);
  };
  useEffect(() => {
    window.scrollTo({
      top: document.getElementById("scroll")?.offsetTop,
      behavior: "smooth",
    });
  }, [router]);
  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta name="description" content={locale == "en" ? (props.en_desc ?? "") : (props.desc ?? "")} />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className='relative sm:h-[450px] h-[150px]'>
        <Image
          alt='12'
          width={1000}
          height={300}
          placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1000, 500))}`}
          style={{
            width: "100%",
            objectFit: "cover",
          }}
          className='absolute top-0 sm:h-[450px] h-[150px]'
          src={"https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_112_f0f2122800.svg"}
        />
        <div className='lg:mx-10 mx-4 p-4 m-auto'>
          <h1 className='sm:text-5xl text-lg absolute bottom-1 lg:bottom-6 font-bold uppercase text-[#426044]'>{locale === "vi" ? "VỀ CHÚNG TÔI" : 'ABOUT US'}</h1>
        </div>
      </div>
      <div className="2xl:container 2xl:mx-auto lg:pb-12 lg:px-20 md:py-12 md:px-6 pb-9 px-4 pt-2 noselect">
        <div className="flex flex-col lg:flex-row justify-between gap-8">
          <div className="w-full lg:w-6/12 flex flex-col justify-center text-justify">
            <p className="font-normal  leading-6 text-gray-600 mb-3 text-sm">
            {locale === "en" ? "Healthcare is a comprehensive approach to maintaining physical and mental wellness, rather than just treating illnesses (Sick Care), which is the current medical practice. ECHO MEDI is at the forefront of introducing the \"Family Doctor\" model, which is prevalent in advanced countries, to provide comprehensive healthcare programs in Vietnam. Our team comprises experts in the field, including doctors, pharmacists, nurses, nutritionists, and psychologists who have undergone rigorous training from top institutions, both domestically and internationally. These institutions encompass Barca Innovation Hub Universitas, Ho Chi Minh City University of Medicine and Pharmacy, Pham Ngoc Thach Medical University, and Ton Duc Thang University..." : "Chăm sóc sức khỏe (Healthcare) là mô hình chăm sóc toàn diện về thể chất và tinh thần thay vì chỉ tập trung vào điều trị khi có vấn đề về bệnh lý (Sick Care) như thực trạng y tế hiện nay. ECHO MEDI tiên phong tại Việt Nam trong việc nâng cấp mô hình “Bác sĩ gia đình” của các nước tiên tiến trên thế giới nhằm cung cấp những chương trình chăm sóc sức khỏe toàn diện nhất. Chúng tôi là tập hợp một đội ngũ các bác sĩ, dược sĩ, điều dưỡng, chuyên gia dinh dưỡng và chuyên viên tâm lý được đào tạo trong và ngoài nước như Barca Innovation Hub Universitas, đại học Y dược Tp.HCM, đại học Y khoa Phạm Ngọc Thạch, đại học Tôn Đức Thắng, v.v. để cùng chăm sóc, theo dõi và nâng cao sức khỏe của mọi khách hàng."}
            </p>
            <p className="font-normal  leading-6 text-gray-600 mb-3 text-sm">
            {locale === "en" ? "We take a pathogenic and proactive approach, focusing on understanding the causes, consequences, diagnosis, and treatment of diseases and injuries. Our primary objective is to prevent illness and help our members maintain optimal health. We encourage healthy attitudes and lifestyles that improve their quality of life and overall well-being." : "ECHO MEDI mong muốn trở thành một nhân tố tích cực trong hành trình chăm sóc sức khỏe của người Việt Nam."}
            </p>
            <p className="font-normal  leading-6 text-gray-600 mb-3 text-sm">
            {locale === "en" ? "ECHO MEDI's healthcare system is built on four essential pillars: preventive care, primary care, chronic disease management and comprehensive wellness." : "Để thực hiện được mục tiêu trên, hệ thống y tế ECHO MEDI xây dựng các dịch vụ chăm sóc sức khỏe dựa trên bốn nền tảng: chăm sóc phòng ngừa, điều trị ban đầu, quản lý bệnh mạn tính, chăm sóc sức khỏe toàn diện."}
            </p>
            <p className="font-normal  leading-6 text-gray-600 mb-3 text-sm">
            {locale === "en" ? "At our establishment, an on-site pharmacy provides a wide range of medications, beauty products, supplements, and healthcare products, all with transparent origins. Our experienced pharmacists take into consideration the unique health status of each client to conduct personalized consultations to ensure that medications are used safely, reasonably, and effectively." : "Nhà thuốc ECHO MEDI đảm bảo cung ứng đầy đủ thuốc điều trị, đa dạng sản phẩm làm đẹp, thực phẩm chức năng và chăm sóc sức khỏe toàn diện với nguồn gốc rõ ràng. Các dược sĩ của chúng tôi luôn tư vấn trực tiếp và hướng dẫn khách hàng dùng thuốc an toàn, hợp lý và hiệu quả, đúng với tình trạng sức khỏe của mỗi cá nhân."}
            </p>
            <p className="font-normal  leading-6 text-gray-600 text-sm">
            {locale === "en" ? "Take control of your health with ECHO MEDI. Our medical experts will accompany you anywhere and provide personalized care." : "Đến ECHO MEDI ngay hôm nay để quản lý sức khỏe của bạn hiệu quả hơn. Các chuyên gia y tế của ECHO MEDI sẽ đồng hành với bạn bất cứ đâu để chăm sóc sức khỏe của bạn."}
            </p>
            <p className="font-normal  leading-6 text-gray-600 text-sm">
              {locale === "en" ? "" : ""}
            </p>
          </div>
          <div className="w-full lg:w-6/12 ">
            <div className="grid grid-cols-2 gap-4 mt-8">
              <img className="w-full rounded-lg" src="https://d3e4m6b6rxmux9.cloudfront.net/xmz2_Xib_20fa85d988.jpg?updated_at=2023-05-29T08:40:48.020Z" alt="office content 1" />
              <img className="mt-4 w-full lg:mt-10 rounded-lg" src="https://d3e4m6b6rxmux9.cloudfront.net/m_D9_Q_Ir_O_bc202d813c.jpg?updated_at=2023-05-29T08:40:47.909Z" alt="office content 2" />
            </div>

          </div>
        </div>
        <div id="scroll"></div>
      </div>
      <h2 className='text-center font-bold lg:text-3xl text-xl pb-2'>{locale === "en" ? "DỊCH VỤ TẠI ECHO MEDI" : "DỊCH VỤ TẠI ECHO MEDI"}</h2>
      <div className="hidden md:block bg-[#ECF5ED] py-10" >
        <div className="px-10 grid grid-cols-5 gap-10 container m-auto ">
          <button onClick={e => setCategory('tintuc')}>
            <div>
              <LinkComponent
                href={"/packages/cham-soc-phong-ngua"}
                locale={locale}
                skipLocaleHandling={false}
              >
                <Image
                  alt="image"
                  width={240}
                  height={240}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(240, 240))}`}
                  className={`border-[12px] rounded-full ${category == "tintuc" ? "border-[#416044]" : "border-white"}`} src="https://d3e4m6b6rxmux9.cloudfront.net/Ellipse_54_55b6497f42.png" />
                <p className="text-center mt-4 font-bold text-sm">{locale == "vi" ? "Chăm sóc phòng ngừa" : "Preventive Care"}</p>
              </LinkComponent>
            </div>
          </button>
          <button onClick={e => setCategory('tintuc_primary_care')}>
            <div>
              <LinkComponent
                href={"/packages/dieu-tri-ban-dau"}
                locale={locale}
                skipLocaleHandling={false}
              >
                <Image
                  alt="image"
                  width={240}
                  height={240}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(240, 240))}`}
                  className={` border-[12px] rounded-full ${category == "tintuc_primary_care" ? "border-[#416044]" : "border-white"}`} src="https://d3e4m6b6rxmux9.cloudfront.net/Ellipse_54_1_ec96951122.png" />
                <p className="text-center mt-4 font-bold text-sm">{locale == "vi" ? "Điều trị ban đầu" : "Primary Care"}</p>
              </LinkComponent>
            </div>
          </button>
          <button onClick={e => setCategory('tintuc_chronic_diseases')}>
            <div>
              <LinkComponent
                href={"/packages/quan-ly-benh-man-tinh"}
                locale={locale}
                skipLocaleHandling={false}
              >
                <Image
                  alt="image"
                  width={240}
                  height={240}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(240, 240))}`}
                  className={` border-[12px] rounded-full ${category == "tintuc_chronic_diseases" ? "border-[#416044]" : "border-white"}`} src="https://d3e4m6b6rxmux9.cloudfront.net/Ellipse_54_2_0d9d10b5dd.png" />
                <p className="text-center mt-4 font-bold text-sm">{locale == "vi" ? "Quản lý bệnh mạn tính" : "Chronic Diseases"}</p>
              </LinkComponent>
            </div>
          </button>
          <button onClick={e => setCategory('tintuc_wellness')}>
            <div>
              <LinkComponent
                href={"/packages/suc-khoe-toan-dien"}
                locale={locale}
                skipLocaleHandling={false}
              >
                <Image
                  alt="image"
                  width={240}
                  height={240}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(240, 240))}`}
                  className={` border-[12px] rounded-full ${category == "tintuc_wellness" ? "border-[#416044]" : "border-white"}`} src="https://d3e4m6b6rxmux9.cloudfront.net/Ellipse_54_3_1dad1416c3.png" />
                <p className="text-center mt-4 font-bold text-sm">{locale == "vi" ? "Sức khỏe toàn diện" : "Wellness"}</p>
              </LinkComponent>
            </div>
          </button>
          <button onClick={e => setCategory('tintuc_pharmacy')}>
            <div>
              <LinkComponent
                // href={"/packages/cham-soc-tai-nha/"}
                href={"/packages/suc-khoe-toan-dien"}
                locale={locale}
                skipLocaleHandling={false}
              >
                <Image
                  alt="image"
                  width={240}
                  height={240}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(240, 240))}`}
                  className={` border-[12px] rounded-full ${category == "tintuc_pharmacy" ? "border-[#416044]" : "border-white"}`} src="https://d3e4m6b6rxmux9.cloudfront.net/Ellipse_54_4_4b40f0e960.png" />
                <p className="text-center mt-4 font-bold text-sm">{locale == "vi" ? "Dịch vụ tại nhà" : "Services At Home"}</p>
              </LinkComponent>
            </div>
          </button>
        </div>
      </div>
      <div className="block md:hidden p-2 shadow-xl m-2">
        <div className="grid grid-cols-2 gap-2 container m-auto ">
          <button className={`${category == "tintuc" ? "bg-[#DFF9D6]" : "bg-[#E8E8E8]"} rounded p-2`} onClick={e => setCategory('tintuc')}>
            <LinkComponent
              href={"/packages/cham-soc-phong-ngua"}
              locale={locale}
              skipLocaleHandling={false}
            >
              <p className="text-center m-auto text-xs">{locale == "vi" ? "Chăm sóc phòng ngừa" : "Preventive care"}</p>
            </LinkComponent>
          </button>
          <button className={`${category == "tintuc_primary_care" ? "bg-[#DFF9D6]" : "bg-[#E8E8E8]"} rounded p-2`} onClick={e => setCategory('tintuc_primary_care')}>
            <LinkComponent
              href={"/packages/dieu-tri-ban-dau"}
              locale={locale}
              skipLocaleHandling={false}
            >
              <p className="text-center m-auto text-xs">{locale == "vi" ? "Điều trị ban đầu" : "Primary care"}</p>
            </LinkComponent>
          </button>
          <button className={`${category == "tintuc_chronic_diseases" ? "bg-[#DFF9D6]" : "bg-[#E8E8E8]"} rounded p-2`} onClick={e => setCategory('tintuc_chronic_diseases')}>
            <LinkComponent
              href={"/packages/quan-ly-benh-man-tinh"}
              locale={locale}
              skipLocaleHandling={false}
            >
              <p className="text-center m-auto text-xs">{locale == "vi" ? "Quản lý bệnh mạn tính" : "Chronic diseases"}</p>
            </LinkComponent>
          </button>
          <button className={`${category == "tintuc_wellness" ? "bg-[#DFF9D6]" : "bg-[#E8E8E8]"} rounded p-2`} onClick={e => setCategory('tintuc_wellness')}>
            <LinkComponent
              href={"/packages/suc-khoe-toan-dien"}
              locale={locale}
              skipLocaleHandling={false}
            >
              <p className="text-center m-auto text-xs">{locale == "vi" ? "Sức khỏe toàn diện" : "Wellness"}</p>
            </LinkComponent>
          </button>
          <button className={`${category == "tintuc_pharmacy" ? "bg-[#DFF9D6]" : "bg-[#E8E8E8]"} rounded p-2`} onClick={e => setCategory('tintuc_pharmacy')}>
            <LinkComponent
              // href={"/service_home/cham-soc-tai-nha/"}
              href={"/packages/suc-khoe-toan-dien"}
              locale={locale}
              skipLocaleHandling={false}
            >
              <p className="text-center m-auto text-xs">{locale == "vi" ? "Dịch vụ tại nhà" : "Services At Home"}</p>
            </LinkComponent>
          </button>
        </div>
      </div>
        {
          props.en_label === "Home Visits" && (
            <Tabs>
            <TabList className="flex justify-center shadow-lg w-3/4 mx-auto items-center mt-10 p-2 box-border border rounded-xl bg-gray-50">
              <Tab className="lg:px-[60px] text-center py-2 text-sm font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
              <LinkComponent
                  // href={"/packages/cham-soc-tai-nha/"}
                  href={"/packages/suc-khoe-toan-dien"}
                  locale={locale}
                  skipLocaleHandling={false}
                >
                <p>Chăm Sóc Tại Nhà</p>
                </LinkComponent>
              </Tab>
              <Tab className="lg:px-[60px] py-2 text-center text-sm font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
              <LinkComponent
                  href={"/services/kham-benh-tu-xa-khach-hang-tai-viet-nam/"}
                  locale={locale}
                  skipLocaleHandling={false}
                >
                <p>Khám Bệnh Từ Xa</p>
                <p> (Khách Hàng Tại Việt Nam)</p>
                </LinkComponent>
              </Tab>
              <Tab className="lg:px-[60px] py-2 text-center text-sm font-bold text-black hover:text-gray-800 hover:bg-gray-200 cursor-pointer">
              <LinkComponent
                  href={"/services/kham-benh-tu-xa-viet-kieu-o-nuoc-ngoai/"}
                  locale={locale}
                  skipLocaleHandling={false}
                >
                <p>Khám Bệnh Từ Xa</p>
                <p> (Việt Kiều Nước Ngoài)</p>
                </LinkComponent>
              </Tab>
            </TabList>
          </Tabs>
          )
        }



      <p className='text-center text-[#426044] font-bold lg:text-3xl uppercase text-lg lg:mt-4 mb-2 px-2'>{locale === "en" ? props.en_label : props.label}</p>
      {props.en_desc && <div className="max-w-[1048px] mx-auto text-center px-4">
        {parse(locale == "en" ? (props.en_desc ?? "") : (props.desc ?? ""))}
      </div>}
      <div className="max-w-[1048px] mx-auto text-left noselect py-4">
        {props.sub_packages?.map((sp: any, id: any) =>
          <div className="max-w-[1240px] mx-auto p-4 text-left">
            <h2 className='py-3 text-[#426044] font-bold text-lg'>{locale === "en" ? sp.en_label : sp.label}</h2>
            <div className="grid grid-rows-none md:grid-cols-4 gap-4">
              {sp.services.slice(0, cnt == -1 ? sp.services.length : cnt).map((sv: any) =>
                <div className='rounded-2xl border border-gray-200 bg-white p-2 group transition-all duration-500 transform hover:scale-105'>
                  <div className='flex flex-col items-center'>
                    <Image
                      placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(300, 300))}`}
                      alt={locale === "en" ? sv.label_web_en : sv.label_web}
                      layout='intrinsic'
                      width={100}
                      height={100}
                      loading='lazy'
                      className='w-2/3 object-contain mb-3'
                      src={"https://d3e4m6b6rxmux9.cloudfront.net" + sv.genetica_image?.url.replace('/uploads', '')} />
                    <p className='mb-1 text-center font-bold text-base sm:whitespace-break-spaces whitespace-normal' >{locale === "en" ? sv.en_label : sv.label}</p>
                  </div>
                  <div className='px-4'>
                    <p className='text-sm text-justify leading-5' >{locale === "en" ? sv.en_desc : sv.desc}</p>
                  </div>
                </div>
              )}
            </div>
            {showLoadAllButton && cnt !== sp.services.length && cnt !== -1 &&
              <div className="flex justify-center mt-4">
                <button
                  type="button"
                  onClick={handleLoadAll}
                  className="rounded-2xl border border-gray-200 bg-white px-4 py-2 group transition-all duration-500 transform hover:scale-105"
                            >
                  {locale === "vi" ? "Xem Tất Cả" : "Load all"}
                </button>
              </div>
            }
            {showHideButton &&
              <div className="flex justify-center mt-4">
                <button
                  type="button"
                  onClick={handleHide}
                  className="rounded-2xl border border-gray-200 bg-white px-4 py-2 group transition-all duration-500 transform hover:scale-105"
                  > 
                  {locale === "vi" ? "Ẩn bớt" : "Hide"}
                </button>
              </div>
            }
          </div>
        )}
      </div>
      <Contact />
    </>
  );
};


export default Blog;