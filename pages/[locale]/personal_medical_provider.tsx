import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import { makeStaticProps } from '../../lib/getStatic';
import React from "react";
import { addToCart } from "../../lib/ui";
import HeaderMemBership from "../../components/Membership/HeaderMemBership";
import Image from "next/image";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';

  return (
    <>
      <Head>
        <title>ECHO MEDI - Thành viên <PERSON>ác <PERSON>ĩ Gia Đình</title>
        <meta name="description" content="ECHO MEDI - Trở thành thành viên Bác Sĩ Gia Đình và tận hưởng những ưu đãi đặc biệt cùng dịch vụ chăm sóc sức khỏe chất lượng cao." />
        <meta name="keywords" content="ECHO MEDI, thành viên bác sĩ gia đình, chăm sóc sức khỏe, ưu đãi, dịch vụ y tế, bác sĩ gia đình" />
        <meta property="og:title" content="ECHO MEDI - Thành viên Bác Sĩ Gia Đình" />
        <meta property="og:description" content="Tận hưởng các lợi ích độc quyền và dịch vụ chất lượng hàng đầu khi trở thành thành viên Bác Sĩ Gia Đình tại ECHO MEDI." />
        <meta property="og:image" content="/favicon.png" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <HeaderMemBership locale={locale} />
      <div className="noselect 2xl:mx-auto lg:pb-16 lg:px-20 md:py-2 px-2 pb-9 m-auto">
        <h3 className="text-2xl md:text-3xl text-center text-[#156634] font-bold mt-4">{locale === "en" ? "Family Doctor" : "Bác Sĩ Gia Đình"}</h3>
        <div className="flex flex-col justify-between">
          <div className="w-full flex flex-row text-justify">
            <p className="font-normal leading-6 mb-3 text-base text-justify md:text-center my-2">
              {locale === "en" ? "In today’s fast-paced world, people are looking for healthcare solutions that are both efficient and reliable. ECHO MEDI introduces an innovative service designed to meet this need: the Family Doctor package. This package offers telehealth consultations through advanced technology, ensuring that customers receive high-quality healthcare that is both convenient and secure." : "Đời sống ngày càng phát triển, nhu cầu về chất lượng và độ tiện dụng của dịch vụ ngày càng tăng cao. ECHO MEDI tạo ra gói Bác sĩ gia đình, cung cấp cho khách hàng dịch vụ tư vấn sức khỏe từ xa bằng cách ứng dụng công nghệ tiên tiến. Khách hàng không còn cần phải đến phòng khám để khám bệnh mà có thể tiếp cận đội ngũ y tế mọi lúc mọi nơi."}
            </p>
          </div>
          <div className="mt-3">
            <div className="overflow-x-auto">
              <table className="table w-full text-sm md:text-base">
                {/* head */}
                <thead>
                  <tr>
                    <th className="text-center align-middle w-[30%] bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634] md:text-xl text-sm">{locale === "en" ? "Services" : "Dịch Vụ"}</th>
                    <th className="text-center align-middle w-[25%] bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634] md:text-xl text-sm">{locale === "en" ? "Non-member" : "Khách Hàng \n Thông Thường"}</th>
                    <th className="text-center py-6 md:py-0 text-white align-middle w-[45%] relative bg-header-family h-16 shadow-2xl border-none rounded-lg md:text-xl text-sm">
                      {locale === "en" ? "Offers" : "Ưu Đãi Áp Dụng"}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {/* row 1 */}
                  <tr>
                    <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "In clinic health evaluation" : "Khám tại phòng khám"}</td>
                    <td className="text-center align-middle bg-white border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "350.000 VND/visit" : "350.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                      <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "1 visit/year" : "01 lần khám"}
                      </section>
                    </td>
                  </tr>
                  {/* row 2 */}
                  <tr className="hover">
                    <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Telehealth consultation" : "Tư vấn sức khỏe từ xa"}</td>
                    <td className="text-center align-middle  bg-white border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "250.000 VND/call" : "250.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                      <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "12 calls/year" : "12 lần/năm"}
                      </section>
                    </td>
                  </tr>
                  {/* row 3 */}
                  <tr>
                    <td className=" bg-white border-[#E6E9F5] align-middle">{locale === "en" ? "Medical services*" : "Dịch vụ khám và xét nghiệm*"}</td>
                    <td className="text-center align-middle  whitespace-break-spaces bg-white border-[#E6E9F5]">{locale === "en" ? "Listed price*" : "Theo bảng giá niêm yết*"}</td>
                    <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                      <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="20" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        
                        <span>{locale === "en" ? "10% discount*" : "Giảm 10% \ngiá niêm yết*"}</span>
                      </section>
                      {/* <span className="text-center">{locale === "en" ? "(không áp dụng với thuốc và thực phẩm chức năng)*" : "(không áp dụng với thuốc và thực phẩm chức năng)*"}</span> */}
                    </td>
                  </tr>
                  {/* <tr>
                    <td colSpan={2} className="align-middle bg-[#FAFAFA] h-16 border-[#E6E9F5]">{locale === "en" ? "Price membership" : "Giá gói Thành Viên"}</td>
                    <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <span className="line-through">{locale === "en" ? "3.000.000 VND/year" : "3.000.000 VND/năm"}</span>
                      </section>
                    </td>
                  </tr> */}
                  {/* <tr>
                    <td colSpan={2} className="font-bold align-middle border-[#E6E9F5] bg-white text-[#156634]">{locale === "en" ? "Special offer" : "Giá ưu đãi"}</td>
                    <td className="text-center align-middle border-[#E6E9F5] relative bg-header-family md:h-16">
                      <section className="flex h-16 items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <span className="font-bold pt-4"> {locale === "en" ? "1.000.000 VND/year" : "1.000.000 VND/năm"}</span>
                      </section>
                    </td>
                  </tr> */}
                  {/* <tr>
                    <td colSpan={2} className="font-bold align-middle bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634]">{locale === "en" ? "Special offer" : "Giá ưu đãi"}</td>
                    <td className="py-6 text-center md:py-0 align-middle relative bg-header-family md:h-16 border-none rounded-lg text-white md:text-lg">
                      {locale === "en" ? "1.000.000 VND/year" : "1.000.000 VND/năm"}
                    </td>
                  </tr> */}
                  <tr>
                    <td colSpan={2} className="border-none">
                    </td>
                    <td className="border-none">
                      <section className="hidden md:block">
                        <div className='mt-4 flex items-center justify-around'>
                          <a href="tel:1900638408" className="border border-[#156634] rounded-3xl py-1.5 md:px-12 text-[#156634] font-medium" data-original-title="Liên hệ với chúng tôi">
                            {locale === "en" ? "Contact" : "Tư vấn gói"}
                          </a>
                          {/* <button onClick={() => { addToCart(839, locale) }} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 md:px-12 text-sm font-medium text-white">
                            {locale === "en" ? "Add To Cart" : "Mua ngay"}
                          </button> */}
                        </div>
                      </section>
                    </td>
                  </tr>
                </tbody>
              </table>
              <section className="block md:hidden">
                <div className="flex items-center justify-between">
                  <div>
                    <a href="tel:1900638408" className="border border-[#156634] rounded-3xl py-1 px-12 text-[#156634] font-medium" data-original-title="Liên hệ với chúng tôi">
                      {locale === "en" ? "Contact" : "Tư vấn gói"}
                    </a>
                  </div>
                  <div>
                    <button onClick={() => { addToCart(839, locale) }} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-12 text-sm font-medium text-white">
                      {locale === "en" ? "Add To Cart" : "Mua ngay"}
                    </button>
                  </div>
                </div>
              </section>
              {locale == "vi" ?
                <div className="text-sm">
                  <p className="mt-4">*Giảm trên giá dịch vụ đã công bố với Sở Y tế, không bao gồm các xét nghiệm thực hiện tại cơ sở y tế liên kết.</p>
                  <p className="font-bold text-left my-2">Điều khoản chung:</p>
                  <li className="my-1"> Các ưu đãi dịch vụ trong danh mục trên có giá trị sử dụng trong vòng 1 năm kể từ ngày thanh toán chi phí dịch vụ.</li>
                  <li className="my-1"> Các dịch vụ trong danh mục trên áp dụng cho khách hàng sử dụng gói dịch vụ “Bác sĩ gia đình (Family Doctor)”.</li>
                  <li> Sử dụng đúng họ tên và số điện thoại đã đăng ký khi sử dụng dịch vụ tại phòng khám hoặc đăng ký thành viên thông qua app ECHO MEDI.</li>
                </div> :
                <div className="text-sm">
                  <p className="mt-4">*Discounts apply to service prices registered with the Ministry of Health, excluding tests conducted at partnered hospitals.</p>
                  <p className="font-bold text-left my-2">General terms:</p>
                  <li className="my-1">	The services listed above are valid for one year from the date of payment.</li>
                  <li className="my-1">	These services are applicable to customers enrolled in the "Family Doctor" service package.</li>
                  <li> When using the services at the clinic or registering for membership through the ECHO MEDI app, please provide your full name and registered phone number accurately.</li>
                </div>}
            </div>
          </div>
        </div>
      </div>
      <Contact />
    </>
  );
};

export default Home;
