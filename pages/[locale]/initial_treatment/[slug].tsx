import type {
  InferGetStaticPropsType,
} from 'next';
import React, { useEffect, useState } from 'react';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import Image from 'next/image'
import { addToCart, shimmer, toBase64 } from '../../../lib/ui';
import { getStaticPathsPackagesPrimaryCare, getStaticPropsPackage } from '../../../lib/getStatic';
import { CardPackageItem, CardPackageSlideItem } from '../../../components/CardPackage/CardPackageItem';
import ModalBooking from '../../../components/BookingService';
export { getStaticPathsPackagesPrimaryCare as getStaticPaths, getStaticPropsPackage as getStaticProps };



const detectMob = () => {
  const toMatch = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i
  ];

  return toMatch.some((toMatchItem) => {
    return navigator.userAgent.match(toMatchItem);
  });
}

interface Treatment {
  id: string;
  label: string;
  label_en: string;
}

const treatments: Treatment[] = [
  { id: 'initial_treatment_1', label: 'Khám Tổng Quát', label_en: "General Healthcare" },
  { id: 'initial_treatment_2', label: 'Nam Giới', label_en: "Men" },
  { id: 'initial_treatment_3', label: 'Nữ Giới', label_en: "Women" },
  { id: 'initial_treatment_4', label: 'Hậu Covid-19', label_en: "Post Covid-19" },
  { id: 'initial_treatment_5', label: 'Khám Tổng Quát', label_en: "General Healthcare" },
  { id: 'initial_treatment_6', label: 'Nam Giới', label_en: "Men" },
  { id: 'initial_treatment_7', label: 'Nữ Giới', label_en: "Women" },
  { id: 'initial_treatment_8', label: 'Covid-19', label_en: "Post Covid-19" },
];

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsPackage>) => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showModal, setShowModal] = useState(false);
  const [message, setMessage] = useState("");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng đặt lịch khám ở phòng khám về gói" + title)
  };

  const [activeStates, setActiveStates] = useState('')
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    const isMobile = detectMob();
    setActiveStates(isMobile ? 'initial_treatment_5' : 'initial_treatment_1');
  }, []);
  const handleSetActive = (key: string) => {
    setActiveStates(prevState => (prevState === key ? '' : key));
    const element = document.getElementById(key);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };
  const checkActiveSection = () => {
    const sections = document.querySelectorAll('section[id]');
    let currentId = '';

    sections.forEach((section) => {
      const rect = section.getBoundingClientRect();
      if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
        currentId = section.id;
      }
    });

    setActiveStates(currentId);
  };

  useEffect(() => {
    window.addEventListener('scroll', checkActiveSection);
    checkActiveSection();
    return () => {
      window.removeEventListener('scroll', checkActiveSection);
    };
  }, []);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);
  const handleClickDefault = (key: string) => (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    handleSetActive(key);
    const anchorElement = e.currentTarget;
    if (anchorElement) {
      anchorElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      });
    }
  };

  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta name="description" content={locale == "en" ? (props.en_desc ?? "") : (props.desc ?? "")} />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section>
        <div className="mx-auto">
          <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
            <div className="w-full relative">
              <Image
                src="/banner/banner_new.webp"
                alt="Banner"
                width={1920}
                height={500}
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                className="object-center"
                layout="responsive"
              />
            </div>
            <div className="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 2xl:w-2/3 absolute md:px-16 px-4">
              <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block">
                {locale === "vi" ? "Điều Trị\n Ban Đầu" : "Primary Care"}
              </h2>
              <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                {locale === "vi" ? "Điều Trị\n Ban Đầu" : "Primary Care"}
              </h2>
              <p className="text-base mb-8 text-justify hidden md:block">
                {locale === "en" ? props.en_desc : props.desc}
              </p>
              <section className='hidden md:block'>
                <div className="flex w-full flex-wrap gap-2">
                  {treatments.slice(0, 4).map((treatment) => (
                    <a
                      key={treatment.id}
                      href={`#${treatment.id}`}
                      onClick={handleClickDefault(treatment.id)}
                      className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === treatment.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                        }`}
                    >
                      {locale === "vi" ? treatment.label : treatment.label_en}
                    </a>
                  ))}
                </div>
              </section>
            </div>
          </div>
          <p className="text-sm my-4 text-justify px-4 block md:hidden">
            {locale === "en" ? props.en_desc : props.desc}
          </p>
          <section className='block md:hidden'>
            <div className="flex w-full gap-2 px-4 overflow-x-scroll whitespace-nowrap hide-scrollbar">
              {treatments.slice(4, 8).map((treatment) => (
                <a
                  key={treatment.id}
                  href={`#${treatment.id}`}
                  onClick={handleClickDefault(treatment.id)}
                  className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === treatment.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                    }`}
                >
                  {locale === "vi" ? treatment.label : treatment.label_en}
                </a>
              ))}
            </div>
          </section>
        </div>
      </section>
      <div style={{
        background: "white",
        zIndex: 100,
        position: "fixed",
        top: 45,
        width: "100%",
        textAlign: "left",
      }}
      >
        {isVisible && (
          <>
            <section className='md:px-16 px-4'>
              <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase py-2 mt-4">
                {locale === "vi" ? "Điều Trị\n Ban Đầu" : "Primary Care"}
              </h2>
              <section className='md:hidden block'>
                <div className="flex w-full gap-2 overflow-x-scroll whitespace-nowrap hide-scrollbar pb-3">
                  {treatments.slice(4, 8).map((treatment) => (
                    <a
                      key={treatment.id}
                      href={`#${treatment.id}`}
                      onClick={handleClickDefault(treatment.id)}
                      className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === treatment.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                        }`}
                    >
                      {locale === "vi" ? treatment.label : treatment.label_en}
                    </a>
                  ))}
                </div>
              </section>
              <section className='md:block hidden'>
                <div className="flex w-full flex-wrap gap-2 pb-3">
                  {treatments.slice(0, 4).map((treatment) => (
                    <a
                      key={treatment.id}
                      href={`#${treatment.id}`}
                      onClick={handleClickDefault(treatment.id)}
                      className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === treatment.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                        }`}
                    >
                      {locale === "vi" ? treatment.label : treatment.label_en}
                    </a>
                  ))}

                </div>
              </section>
            </section>
          </>
        )}
      </div>
      <section className='mx-auto max-w-screen-2xl'>
        <div className="md:px-16 px-4">
          <div className="mx-auto text-left noselect my-12">
            {props.sub_packages?.map((sp: any, id: any) => {
              if (id === 0) {
                return (
                  <>
                    <section id="initial_treatment_1" className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id="initial_treatment_5" className='block md:hidden' >
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 1) {
                return (
                  <>
                    <section id="initial_treatment_2" className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <CardPackageItem sv={sv} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                          </>
                        )}
                      </div>
                    </section>
                    <section id="initial_treatment_6" className='block md:hidden' >
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 2) {
                return (
                  <>
                    <section id="initial_treatment_3" className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <CardPackageItem sv={sv} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                          </>
                        )}
                      </div>
                    </section>
                    <section id="initial_treatment_7" className='block md:hidden' >
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 3) {
                return (
                  <>
                    <section id="initial_treatment_4" className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <CardPackageItem sv={sv} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                          </>
                        )}
                      </div>
                    </section>
                    <section id="initial_treatment_8" className='block md:hidden' >
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[550px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
            }
            )}
            {showModal && (
              <ModalBooking
                visible={showModal}
                onClose={() => setShowModal(false)}
                currentBlog={currentBlog}
                locale={locale}
              />
            )}
          </div>
        </div>
      </section>
      <Contact />
    </>
  );
};

function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Blog;
