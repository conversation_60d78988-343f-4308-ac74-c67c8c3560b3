import type {
  InferGetStaticPropsType,
} from 'next';
import React, { useEffect } from 'react';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import { useState } from 'react';
import Image from 'next/image'
import { addToCart, shimmer, toBase64 } from '../../../lib/ui';
import { getStaticPathsPackages, getStaticPropsPackage } from '../../../lib/getStatic';
import LinkComponent from '../../../components/Link';
import { CardPackageItem, CardPackageSlideItem } from '../../../components/CardPackage/CardPackageItem';
import ModalBooking from '../../../components/BookingService';
export { getStaticPathsPackages as getStaticPaths, getStaticPropsPackage as getStaticProps };

const detectMob = () => {
  const toMatch = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i
  ];

  return toMatch.some((toMatchItem) => {
    return navigator.userAgent.match(toMatchItem);
  });
}

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsPackage>) => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showModal, setShowModal] = useState(false);
  const [activeTab, setActiveTab] = useState('');
  const handleTabClick = (tab: any) => {
    setActiveTab(tab);
  };
  useEffect(() => {
    if (router.query.slug === 'tam-ly-tre-em') {
      setActiveTab("tabs-membership-2")
    } else {
      setActiveTab("tabs-membership-1")
    }
  }, [router.query.slug]);
  const [message, setMessage] = useState("");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng đặt lịch khám ở phòng khám về gói" + title)
  };

  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta name="description" content={locale == "en" ? (props.en_desc ?? "") : (props.desc ?? "")} />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section>
        <div className="mx-auto">
          <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
            <div className="w-full relative">
              <Image
                src="/banner/banner_new.webp"
                alt="Banner"
                width={1920}
                height={500}
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                className="object-center"
                layout="responsive"
              />
            </div>
            <div className="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 2xl:w-2/3 absolute md:px-16 px-4">
              <h2 className="text-left font-bold md:text-[28px] text-lg text-[#156634] uppercase pb-2 hidden md:block">
                {locale === "vi" ? "Tham Vấn\n Tâm Lý" : "Mental\n Counselling"}
              </h2>
              <h2 className="text-left font-bold md:text-[28px] text-lg text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                {locale === "vi" ? "Tham Vấn\n Tâm Lý" : "Mental\n Counselling"}
              </h2>
              <p className="text-base mb-8 text-justify hidden md:block">
                {locale === "en" ? props.en_desc : props.desc}
              </p>
            </div>
          </div>
        </div>
      </section>
      <p className="text-sm my-4 text-justify px-4 block md:hidden">
        {locale === "en" ? props.en_desc : props.desc}
      </p>
      <section className='mx-auto max-w-screen-2xl'>
        <div className="md:px-16 px-4">
          <div className="mx-auto text-left noselect pb-8">
            <div className="tabs">
              <div className="mb-15 border-b-2 flex justify-center rounded-[10px] md:flex-nowrap md:items-center lg:gap-7 xl:mb-21 xl:gap-12 overflow-x-scroll whitespace-nowrap hide-scrollbar">
                <LinkComponent href={"/goi_tam_ly/goi-tam-ly-nguoi-lon/"} skipLocaleHandling={false} locale={""}>
                  <div
                    onClick={() => handleTabClick("tabs-membership-1")}
                    className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${activeTab === "tabs-membership-1"
                      ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                      : ""
                      }`}
                  >
                    <div className="md:w-3/5 lg:w-auto ">
                      <button className={`text-lg ${activeTab === "tabs-membership-1" ? "text-[#156634] font-bold" : "text-black "
                        } xl:text-regular`}>
                        {locale === "en" ? "Adult" : "Người lớn"}
                      </button>
                    </div>
                  </div>
                </LinkComponent>
                <LinkComponent href={"/goi_tam_ly/tam-ly-tre-em/"} skipLocaleHandling={false} locale={""}>
                  <div
                    onClick={() => handleTabClick("tabs-membership-2")}
                    className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${activeTab === "tabs-membership-2"
                      ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                      : ""
                      }`}
                  >
                    <div className="md:w-3/5 lg:w-auto">
                      <button className={`text-lg  ${activeTab === "tabs-membership-2" ? "text-[#156634] font-bold" : "text-black "
                        } xl:text-regular`}>
                        {locale === "en" ? "Children and Adolescent" : "Trẻ em và vị thành niên"}
                      </button>
                    </div>
                  </div>
                </LinkComponent>
              </div>
              <div className="mt-3">
                <div
                  id="tabs-membership-1"
                  role="tabpanel"
                  aria-labelledby="tabs-membership-1"
                  className={`tabcontent ${activeTab === 'tabs-membership-1' ? '' : 'hidden'}`}
                >

                  {props.sub_packages?.map((sp: any, id: any) => {
                    if (id === 0) {
                      return (
                        <>
                          <section id='care_package_1' className='hidden md:block'>
                            <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                              {sp.services.map((sv: any) =>
                                <>
                                  <section key={sv.id}>
                                    <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                                  </section>
                                </>
                              )}
                            </div>
                          </section>
                          <section id='care_package_10' className='block md:hidden' >
                            <div className="mx-auto py-6">
                              <section key={sp.id}>
                                <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                              </section>
                            </div>
                          </section>
                        </>
                      )
                    }
                  }
                  )}
                </div>
                <div
                  id="tabs-membership-2"
                  role="tabpanel"
                  aria-labelledby="tabs-membership-2"
                  className={`tabcontent ${activeTab === 'tabs-membership-2' ? '' : 'hidden'}`}
                >
                  {props.sub_packages?.map((sp: any, id: any) => {
                    if (id === 0) {
                      return (
                        <>
                          <section id='care_package_1' className='hidden md:block'>
                            <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                              {sp.services.map((sv: any) =>
                                <>
                                  <section key={sv.id}>
                                    <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                                  </section>
                                </>
                              )}
                            </div>
                          </section>
                          <section id='care_package_10' className='block md:hidden' >
                            <div className="mx-auto py-6">
                              <section key={sp.id}>
                                <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                              </section>
                            </div>
                          </section>
                        </>
                      )
                    }
                  }
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        {showModal && (
          <ModalBooking
            visible={showModal}
            onClose={() => setShowModal(false)}
            currentBlog={currentBlog}
            locale={locale}
          />
        )}
      </section >
      <Contact />
    </>
  );
};

export default Blog;
