import type { InferGetStaticPropsType } from 'next';
import Head from "next/head";
import { shimmer, toBase64 } from "../../../lib/ui";
import Image from "next/image";
import { getStaticPathsPartnership, getStaticPropsPartnership } from '../../../lib/getStatic';
import LinkComponent from '../../../components/Link';
import { useRouter } from 'next/router';
import Contact from "../../../components/Contact/Contact";
import toast, { Toaster } from 'react-hot-toast';
export { getStaticPathsPartnership as getStaticPaths, getStaticPropsPartnership as getStaticProps };

const Partnership = ({ data }: InferGetStaticPropsType<typeof getStaticPropsPartnership>) => {
  const router = useRouter();
  const locale = router.query.locale as string || 'vi';

  const formatPhoneNumber = (phone: string): string => {
    const digitsOnly = phone.replace(/\D/g, '');
    return digitsOnly.startsWith('84') ? '0' + digitsOnly.slice(2) : digitsOnly;
  };

  const copyToClipboard = async (text: string) => {
    const formattedText = formatPhoneNumber(text);
    if (navigator.clipboard && navigator.clipboard.writeText) {
      try {
        await navigator.clipboard.writeText(formattedText);
        toast.success('Đã sao chép vào bộ nhớ tạm!');
      } catch (err) {
        toast.error('Không thể sao chép!');
        console.error('Lỗi sao chép văn bản: ', err);
      }
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = formattedText;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand('copy');
        toast.success('Đã sao chép vào bộ nhớ tạm!');
      } catch (err) {
        toast.error('Không thể sao chép!');
        console.error('Lỗi sao chép văn bản: ', err);
      }
      document.body.removeChild(textarea);
    }
  };

  const downloadImage = (url: string, name: string = 'image') => {
    if (!url) {
      toast.error('URL của ảnh không hợp lệ!');
      return;
    }

    const link = document.createElement('a');
    link.href = url;
    link.download = `${name}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Tải ảnh đã bắt đầu. Để lưu vào thư viện ảnh của bạn, hãy nhấn và giữ ảnh và chọn "Thêm vào Ảnh".');
  };




  if (!data) {
    return <div className="flex items-center justify-center">
      <div role='status' className='max-w-sm animate-pulse'>
        <h3 className='h-3 bg-gray-300 rounded-full w-48 mb-4'></h3>
        <p className='h-2 bg-gray-300 rounded-full max-w-[380px] mb-2.5'></p>
        <p className='h-2 bg-gray-300 rounded-full max-w-[340px] mb-2.5'></p>
        <p className='h-2 bg-gray-300 rounded-full max-w-[320px] mb-2.5'></p>
      </div>
    </div>;
  }

  return <>
    <Head>
      <title>{data.name} - Profile</title>
      <meta name="description" content={`Learn more about ${data.name}, including contact information and more.`} />
      <meta name="keywords" content={`${data.name}, profile, contact`} />
      <meta property="og:title" content={`${data.name} - Profile`} />
      <meta property="og:description" content={`Learn more about ${data.name}, including contact information and more.`} />
      <meta property="og:image" content={data.imgURL_VI} />
      <meta property="og:type" content="profile" />
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:title" content={`${data.name} - Profile`} />
      <meta property="twitter:description" content={`Learn more about ${data.name}, including contact information and more.`} />
      <meta property="twitter:image" content={data.imgURL_VI} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
    </Head>

    <section className='my-0 md:my-6'>
      <section className="my-2">
        <div className="w-full max-w-sm mx-auto px-4 md:px-8 flex items-center justify-between">
          <nav className="flex" aria-label="Breadcrumb">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="w-4 h-4 rotate-180" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 15L11.0858 11.4142C11.7525 10.7475 12.0858 10.4142 12.0858 10C12.0858 9.58579 11.7525 9.25245 11.0858 8.58579L7.5 5" stroke="#E5E7EB" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="hover:underline hover:text-[#156634] text-[12px]">{locale === "en" ? "Home" : "Trang chủ"}</span>
                </LinkComponent>
              </div>
            </div>
          </nav>
          <nav className="flex" aria-label="Breadcrumb">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button onClick={() => downloadImage(locale === "en" ? data.imgURL_EN : data.imgURL_VI, data?.name)} className='bg-[#F9FFFB] p-1 rounded-full'>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="#156634" className="h-4 w-4" viewBox="0 0 24 24"><title>tray-arrow-down</title><path d="M2 12H4V17H20V12H22V17C22 18.11 21.11 19 20 19H4C2.9 19 2 18.11 2 17V12M12 15L17.55 9.54L16.13 8.13L13 11.25V2H11V11.25L7.88 8.13L6.46 9.55L12 15Z" /></svg>
                </button>
              </div>
            </div>
          </nav>
        </div>
      </section>
      <div className="w-full max-w-sm mx-auto px-4 md:px-8">
        <div className="flex items-center justify-center sm:justify-center mb-5">
          <Image
            placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(396, 349))}`}
            alt={data.name}
            width={396}
            height={349}
            loading='lazy'
            className="object-cover"
            src={locale === "en" ? data.imgURL_EN : data.imgURL_VI}
          />
        </div>
        <div className="bg-white mb-4 rounded-lg">
          <div className="block p-4 space-y-4">
            <h6 className="text-[#156634] inline-block text-[10px] py-2 px-4 rounded-lg bg-[#15663414]">{locale === "en" ? "Contact" : "Thông tin liên hệ"}</h6>
            <ContactDetail type="phone" value={data.contact} onCopy={copyToClipboard} />
            <ContactDetail type="email" value={data.gmail} onCopy={copyToClipboard} />
          </div>
        </div>
      </div>
    </section>
    <Contact />
    <Toaster position="bottom-center" />
  </>;
};

export default Partnership;

const ContactDetail = ({ type, value, onCopy }: { type: 'phone' | 'email'; value: string; onCopy: (text: string) => void }) => (
  <div className="flex items-center gap-2">
    <div className={`bg-black p-1 rounded-full ${type === 'phone' ? 'text-white' : ''}`}>
      <svg xmlns="http://www.w3.org/2000/svg" fill="white" width="8" height="9" viewBox="0 0 8 9">
        <title>{type}</title>
        {type === 'phone' ? (
          <path d="M6.5 3.5C6.4 3.5 6.31094 3.46406 6.23281 3.39219C6.15469 3.32031 6.10938 3.23125 6.09688 3.125C6.01562 2.54375 5.77031 2.04844 5.36094 1.63906C4.95156 1.22969 4.45625 0.984375 3.875 0.903125C3.76875 0.890625 3.67969 0.846875 3.60781 0.771875C3.53594 0.696875 3.5 0.60625 3.5 0.5C3.5 0.39375 3.5375 0.304688 3.6125 0.232813C3.6875 0.160938 3.775 0.13125 3.875 0.14375C4.6625 0.23125 5.33438 0.553125 5.89062 1.10938C6.44688 1.66563 6.76875 2.3375 6.85625 3.125C6.86875 3.225 6.83906 3.3125 6.76719 3.3875C6.69531 3.4625 6.60625 3.5 6.5 3.5ZM4.93438 3.5C4.85313 3.5 4.78125 3.47188 4.71875 3.41563C4.65625 3.35938 4.60938 3.28438 4.57812 3.19063C4.52813 3.00938 4.43281 2.84844 4.29219 2.70781C4.15156 2.56719 3.99063 2.47188 3.80938 2.42188C3.71563 2.39062 3.64063 2.34375 3.58438 2.28125C3.52813 2.21875 3.5 2.14375 3.5 2.05625C3.5 1.93125 3.54375 1.82969 3.63125 1.75156C3.71875 1.67344 3.81563 1.64688 3.92188 1.67188C4.27188 1.75313 4.57344 1.92031 4.82656 2.17344C5.07969 2.42656 5.24688 2.72813 5.32812 3.07812C5.35313 3.18438 5.325 3.28125 5.24375 3.36875C5.1625 3.45625 5.05938 3.5 4.93438 3.5ZM6.48125 6.875C5.7 6.875 4.92813 6.70469 4.16563 6.36406C3.40313 6.02344 2.70938 5.54063 2.08438 4.91563C1.45938 4.29063 0.976562 3.59688 0.635938 2.83438C0.295313 2.07188 0.125 1.3 0.125 0.51875C0.125 0.40625 0.1625 0.3125 0.2375 0.2375C0.3125 0.1625 0.40625 0.125 0.51875 0.125H2.0375C2.125 0.125 2.20312 0.154687 2.27188 0.214063C2.34063 0.273438 2.38125 0.34375 2.39375 0.425L2.6375 1.7375C2.65 1.8375 2.64688 1.92188 2.62813 1.99063C2.60938 2.05938 2.575 2.11875 2.525 2.16875L1.61563 3.0875C1.74063 3.31875 1.88906 3.54219 2.06094 3.75781C2.23281 3.97344 2.42188 4.18125 2.62813 4.38125C2.82188 4.575 3.025 4.75469 3.2375 4.92031C3.45 5.08594 3.675 5.2375 3.9125 5.375L4.79375 4.49375C4.85 4.4375 4.92344 4.39531 5.01406 4.36719C5.10469 4.33906 5.19375 4.33125 5.28125 4.34375L6.575 4.60625C6.6625 4.63125 6.73438 4.67656 6.79063 4.74219C6.84688 4.80781 6.875 4.88125 6.875 4.9625V6.48125C6.875 6.59375 6.8375 6.6875 6.7625 6.7625C6.6875 6.8375 6.59375 6.875 6.48125 6.875Z" fill="white"/>
        ) : (
          <path d="M1.33268 7.16683C1.14935 7.16683 0.992405 7.10155 0.861849 6.971C0.731293 6.84044 0.666016 6.6835 0.666016 6.50016V2.50016C0.666016 2.31683 0.731293 2.15988 0.861849 2.02933C0.992405 1.89877 1.14935 1.8335 1.33268 1.8335H6.66602C6.84935 1.8335 7.00629 1.89877 7.13685 2.02933C7.26741 2.15988 7.33268 2.31683 7.33268 2.50016V6.50016C7.33268 6.6835 7.26741 6.84044 7.13685 6.971C7.00629 7.10155 6.84935 7.16683 6.66602 7.16683H1.33268ZM3.99935 4.77516C4.02713 4.77516 4.05629 4.771 4.08685 4.76266C4.1174 4.75433 4.14657 4.74183 4.17435 4.72516L6.53268 3.25016C6.57713 3.22238 6.61046 3.18766 6.63268 3.146C6.6549 3.10433 6.66602 3.0585 6.66602 3.0085C6.66602 2.89738 6.61879 2.81405 6.52435 2.7585C6.4299 2.70294 6.33268 2.70572 6.23268 2.76683L3.99935 4.16683L1.76602 2.76683C1.66602 2.70572 1.56879 2.70433 1.47435 2.76266C1.3799 2.821 1.33268 2.90294 1.33268 3.0085C1.33268 3.06405 1.34379 3.11266 1.36602 3.15433C1.38824 3.196 1.42157 3.22794 1.46602 3.25016L3.82435 4.72516C3.85213 4.74183 3.88129 4.75433 3.91185 4.76266C3.9424 4.771 3.97157 4.77516 3.99935 4.77516Z" fill="white"/>
        )}
      </svg>
    </div>
    <p className='text-sm'>{value}</p>
    <button className="bg-[#F9FFFB] p-2 rounded-full" onClick={() => onCopy(value)}>
      <svg width="10" height="12" viewBox="0 0 10 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3.38178 9.23536C3.08521 9.23536 2.83133 9.12976 2.62014 8.91857C2.40894 8.70738 2.30335 8.4535 2.30335 8.15693V1.68634C2.30335 1.38977 2.40894 1.13589 2.62014 0.924699C2.83133 0.713507 3.08521 0.60791 3.38178 0.60791H8.23472C8.53129 0.60791 8.78517 0.713507 8.99636 0.924699C9.20755 1.13589 9.31315 1.38977 9.31315 1.68634V8.15693C9.31315 8.4535 9.20755 8.70738 8.99636 8.91857C8.78517 9.12976 8.53129 9.23536 8.23472 9.23536H3.38178ZM1.22492 11.3922C0.928347 11.3922 0.674466 11.2866 0.463274 11.0754C0.252081 10.8642 0.146484 10.6104 0.146484 10.3138V3.30399C0.146484 3.15121 0.198159 3.02315 0.301509 2.9198C0.404859 2.81645 0.532922 2.76477 0.6857 2.76477C0.838478 2.76477 0.966542 2.81645 1.06989 2.9198C1.17324 3.02315 1.22492 3.15121 1.22492 3.30399V10.3138H6.61707C6.76985 10.3138 6.89791 10.3655 7.00126 10.4688C7.10461 10.5722 7.15629 10.7002 7.15629 10.853C7.15629 11.0058 7.10461 11.1338 7.00126 11.2372C6.89791 11.3405 6.76985 11.3922 6.61707 11.3922H1.22492Z" fill="#156634" />
      </svg>
    </button>
  </div>
);