import type { NextPage } from "next"
import Head from "next/head"
import { useState } from "react"
import axios from "axios"
import { makeStaticProps } from '../../lib/getStatic';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [
        {
            params: {
                locale: 'en',
                slug: "test",
                label: "test2",
            }
        },
        {
            params: {
                locale: 'vi',
                slug: "test",
                label: "test2",
            }
        }],
})

export { getStaticPaths, getStaticProps }
const EventsPMH: NextPage = () => {
    const [form, setForm] = useState({
        fullName: "",
        phoneNumber: "",
        knownEchoMedi: "Đã biết",
        knownFamilyDoctor: "Chưa biết",
        healthConcern: ["Quản lý bệnh mạn tính"] as string[],
        eventName: "Sự kiện PMH",
        eventCode: "PMH2025",
    })

    const [success, setSuccess] = useState(false)
    const [phoneError, setPhoneError] = useState(false)
    const [nameError, setNameError] = useState(false)
    const [healthConcernError, setHealthConcernError] = useState(false)
    const [error, setError] = useState("")
    const healthConcernOptions = [
        "Quản lý bệnh mạn tính",
        "Bác sĩ gia đình",
        "Nhi Khoa",
        "Tâm lý người lớn",
        "Tâm lý trẻ em",
        "Khác"
    ]

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target

        if (name === "phoneNumber") {
            const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/
            setPhoneError(!phoneRegex.test(value))
        }

        if (name === "fullName") {
            const nameRegex = /^[A-Za-zÀ-ỹ\s]{2,}$/
            setNameError(!nameRegex.test(value))
        }

        setForm({ ...form, [name]: value })
    }

    const handleHealthConcernChange = (option: string) => {
        const updatedConcerns = form.healthConcern.includes(option)
            ? form.healthConcern.filter((item) => item !== option)
            : [...form.healthConcern, option]

        setForm({ ...form, healthConcern: updatedConcerns })

        if (updatedConcerns.length > 0) {
            setHealthConcernError(false)
        }
    }

    const handleRadioChange = (name: string, value: string) => {
        setForm({ ...form, [name]: value })
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        let hasError = false

        if (form.fullName.trim() === "") {
            setNameError(true)
            hasError = true
        }

        if (form.phoneNumber.trim() === "") {
            setPhoneError(true)
            hasError = true
        }

        if (form.healthConcern.length === 0) {
            setHealthConcernError(true)
            hasError = true
        }

        if (phoneError || nameError || healthConcernError || hasError) {
            return
        }

        try {
            const res = await axios.post("https://api.echomedi.com/api/events", form)

            if (res.status === 201) {
                setSuccess(true)
                setForm({
                    fullName: "",
                    phoneNumber: "",
                    knownEchoMedi: "Đã biết",
                    knownFamilyDoctor: "Chưa biết",
                    healthConcern: ["Quản lý bệnh mạn tính"],
                    eventName: "Sự kiện PMH",
                    eventCode: "PMH2025",
                })
            }
        } catch (error: any) {
            if (axios.isAxiosError(error)) {
                if (error.response?.data?.message === "Phone number already exists") {
                    setError("Số điện thoại này đã được đăng ký trước đó. Vui lòng kiểm tra lại.");
                } else {
                    setError("Có lỗi xảy ra khi gửi thông tin. Vui lòng thử lại sau.")
                }
            } else {
                setError("Đã xảy ra lỗi không xác định. Vui lòng thử lại.")
            }
        }
    }

    return (
        <>
            <Head>
                <title>Ngày Hội PMH - Nhập Thông Tin</title>
            </Head>
            <main className="min-h-screen bg-white py-12 px-4">
                <div className="max-w-lg mx-auto space-y-10">
                    <section className="text-center">
                        <h1 className="text-2xl font-bold uppercase">ĐĂNG KÝ THAM GIA MINIGAME</h1>
                    </section>

                    <section>
                        {success && (
                            <div className="space-y-6 bg-white p-6">
                                <div className="!w-32 !h-32 mx-auto mb-4 rounded-full overflow-hidden bg-gray-100">
                                    <img
                                        src="https://api.echomedi.com/uploads/success_66e7882952.png"
                                        className="!w-32 !h-32 object-cover"
                                    />
                                </div>
                                <h1 className="text-center font-bold uppercase lg:text-xl text-sm">
                                    Bạn đã nhận được một lượt THAM GIA MINIGAME
                                </h1>
                            </div>
                        )}

                        {!success && (
                            <form className="space-y-6 bg-white p-6" onSubmit={handleSubmit}>
                                <div>
                                    <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Họ và tên <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="fullName"
                                        name="fullName"
                                        value={form.fullName}
                                        onChange={handleChange}
                                        className={`w-full px-3 py-2 border rounded-md ${nameError ? "border-red-500" : "border-gray-300"}`}
                                    />
                                    {nameError && (
                                        <p className="mt-1 text-sm text-red-500">
                                            Vui lòng nhập họ tên hợp lệ
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                                        Số điện thoại <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="tel"
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        value={form.phoneNumber}
                                        onChange={handleChange}
                                        className={`w-full px-3 py-2 border rounded-md ${phoneError ? "border-red-500" : "border-gray-300"}`}
                                    />
                                    {phoneError && (
                                        <p className="mt-1 text-sm text-red-500">
                                            Vui lòng nhập đúng định dạng số điện thoại
                                        </p>
                                    )}

                                </div>

                                <div>
                                    <p className="block text-sm font-medium text-gray-700 mb-2">Bạn đã biết đến Hệ thống Phòng khám và Nhà thuốc ECHO MEDI chưa?</p>
                                    <div className="flex items-center justify-between space-x-4">
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                name="knownEchoMedi"
                                                checked={form.knownEchoMedi === "Đã biết"}
                                                onChange={() => handleRadioChange("knownEchoMedi", "Đã biết")}
                                                className="h-4 w-4 "
                                            />
                                            <span className="ml-2">Đã biết</span>
                                        </label>
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                name="knownEchoMedi"
                                                checked={form.knownEchoMedi === "Chưa biết"}
                                                onChange={() => handleRadioChange("knownEchoMedi", "Chưa biết")}
                                                className="h-4 w-4 "
                                            />
                                            <span className="ml-2">Chưa biết</span>
                                        </label>
                                    </div>
                                </div>

                                <div>
                                    <p className="block text-sm font-medium text-gray-700 mb-2">Bạn đã biết về Bác sĩ gia đình chưa?</p>
                                    <div className="flex items-center justify-between space-x-4">
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                name="knownFamilyDoctor"
                                                checked={form.knownFamilyDoctor === "Đã biết"}
                                                onChange={() => handleRadioChange("knownFamilyDoctor", "Đã biết")}
                                                className="h-4 w-4 "
                                            />
                                            <span className="ml-2">Đã biết</span>
                                        </label>
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                name="knownFamilyDoctor"
                                                checked={form.knownFamilyDoctor === "Chưa biết"}
                                                onChange={() => handleRadioChange("knownFamilyDoctor", "Chưa biết")}
                                                className="h-4 w-4 "
                                            />
                                            <span className="ml-2">Chưa biết</span>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <p className="block text-sm font-medium text-gray-700 mb-2">
                                        Bạn quan tâm đến vấn đề sức khỏe nào? (Có thể chọn nhiều)<span className="text-red-500">*</span>
                                    </p>
                                    <div className="space-y-2">
                                        {healthConcernOptions.map((option) => (
                                            <label key={option} className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={form.healthConcern.includes(option)}
                                                    onChange={() => handleHealthConcernChange(option)}
                                                    className="h-5 w-5 rounded border border-red-500"
                                                />
                                                <span className="ml-2">{option}</span>
                                            </label>
                                        ))}
                                    </div>
                                    {healthConcernError && (
                                        <p className="mt-2 text-sm text-red-500">
                                            Hãy chọn ít nhất một lựa chọn.
                                        </p>
                                    )}
                                </div>
                                {error && (
                                    <div className="mt-1 text-sm text-red-500">
                                        {error}
                                    </div>
                                )}
                                <button
                                    type="submit"
                                    className="w-full font-medium flex items-center justify-center px-6 py-2.5 text-white rounded-full bg-[#156634] hover:bg-[#14813d]"
                                    disabled={phoneError || nameError}
                                >
                                    Gửi thông tin
                                </button>
                            </form>
                        )}
                    </section>
                </div>
            </main>
        </>
    )
}

export default EventsPMH