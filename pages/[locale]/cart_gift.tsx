import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import React, { useEffect, useState } from "react";
import toast from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import { DotLottiePlayer } from "@dotlottie/react-player";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

const productO1 = [
  {
    id: 1,
    quantity: 1,
    service: {
      id: 713,
      label: "<PERSON>ói <PERSON>ức Khỏe Tổng Quát",
      en_label: "General Healthcare",
      price: 2078000,
      quantity: 1,
    }
  },
  {
    id: 2,
    quantity: 1,
    service: {
      id: 693,
      label: "Gói Khám Sức Khỏe Tổng Quát Dành Cho Nam Giới",
      en_label: "Men's Healthcare",
      price: 5000000,
      quantity: 1,
    }
  },
  {
    id: 3,
    quantity: 1,
    service: {
      id: 692,
      label: "Gói Khám Sức Khỏe Tổng Quát Dành Cho Phụ Nữ",
      en_label: "Women's Healthcare",
      price: 5900000,
      quantity: 1,
    }
  },
  {
    id: 4,
    quantity: 1,
    service: {
      id: 696,
      label: "Gói Quản Lý Ngoại Trú Bệnh Tăng Huyết Áp",
      en_label: "Hypertension Management",
      price: 4880000,
      quantity: 1,
    }
  },
  {
    id: 5,
    quantity: 1,
    service: {
      id: 720,
      label: "Gói Quản Lý Ngoại Trú Bệnh Đái Tháo Đường",
      en_label: "Diabetes",
      price: 5120000,
      quantity: 1,
    }
  },
  {
    id: 6,
    quantity: 1,
    service: {
      id: 841,
      label: "Tầm Soát Sức Khỏe Sinh Sản",
      price: 1000000,
      quantity: 1,
    }
  },
  {
    id: 7,
    quantity: 1,
    service: {
      id: 718,
      label: "Gói Khám Sức Khỏe Hậu COVID-19",
      en_label: "Post COVID-19",
      price: 2258500,
      quantity: 1,
    }
  },
  {
    id: 8,
    quantity: 1,
    service: {
      id: 840,
      label: "Tham Vấn Tâm Lý",
      en_label: "Psychological Consultation 60 Minutes",
      price: 500000,
      quantity: 1,
    }
  },
  {
    id: 9,
    quantity: 1,
    service: {
      id: 757,
      label: "Tầm Soát Bệnh Lý Cơ Xương Khớp",
      en_label: "Musculoskeletal Screening",
      price: 1403500,
      quantity: 1,
    }
  },
  {
    id: 10,
    quantity: 1,
    service: {
      id: 740,
      label: "Gói Thành Viên Vàng",
      en_label: "Gold Membership",
      price: 4000000,
      quantity: 1,
    }
  },
  {
    id: 11,
    quantity: 1,
    service: {
      id: 741,
      label: "Gói Thành Viên Bạch Kim",
      en_label: "Platinum",
      price: 8000000,
      quantity: 1,
    }
  },
  {
    id: 12,
    quantity: 1,
    service: {
      id: 839,
      label: "Gói Bác Sĩ riêng của bạn",
      en_label: "Your Personal Doctor Package",
      price: 1000000,
      quantity: 1,
    }
  },
];

const productO2 = [
  {
    id: 1,
    quantity: 1,
    service: {
      id: 659,
      label: "Chương Trình Cai Thuốc Lá",
      en_label: "Smoking Cessation Program",
      price: 2600000,
      quantity: 1,
    }
  },
  // {
  //   id: 2,
  //   quantity: 1,
  //   service: {
  //     id: 65,
  //     label: "Sức Khỏe Tinh Thần",
  //     en_label: "Mental Wellbeing",
  //     price: 2600000,
  //     quantity: 1,
  //   }
  // },
  // {
  //   id: 3,
  //   quantity: 1,
  //   service: {
  //     id: 65,
  //     label: "Quản Lý Cân Nặng",
  //     en_label: "Weight Management",
  //     price: 3300000,
  //     quantity: 1,
  //   }
  // },
  // {
  //   id: 4,
  //   quantity: 1,
  //   service: {
  //     id: 65,
  //     label: "Chương Trình Sức Khỏe Tim Mạch",
  //     en_label: "Heart Health Program",
  //     price: 2600000,
  //     quantity: 1,
  //   }
  // },
  // {
  //   id: 5,
  //   quantity: 1,
  //   service: {
  //     id: 690,
  //     label: "Sức Khỏe Nội Tiết Tố",
  //     en_label: "Hormonal Health",
  //     price: 5000000,
  //     quantity: 1,
  //   }
  // },
  // {
  //   id: 6,
  //   quantity: 1,
  //   service: {
  //     id: 65,
  //     label: "Trao Đổi Chất Và Sức Khoẻ Tim Mạch",
  //     en_label: "Metabolism And Heart Health",
  //     price: 2000000,
  //     quantity: 1,
  //   }
  // },
  // {
  //   id: 7,
  //   quantity: 1,
  //   service: {
  //     id: 42,
  //     label: "Chăm Sóc Da Và Ngăn Ngừa Lão Hóa",
  //     en_label: "Skincare And Anti-aging Therapy",
  //     price: 1700000,
  //     quantity: 1,
  //   }
  // },
];

const Home: NextPage = () => {
  const [lines, setCartLines] = useState<any[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [senderName, setSenderName] = useState("");
  const [senderEmail, setSenderEmail] = useState("");
  const [receiverName, setReceiverName] = useState("");
  const [receiverPhone, setReceiverPhone] = useState("");
  const [message, setMessage] = useState("");
  const [verified, setVerified] = useState(false);
  const [step, setStep] = useState(1);

  useEffect(() => {
    ; (async () => {
      const search = window.location.search;
      const params = new URLSearchParams(search);
      const o1 = params.get('o1');
      const o2 = params.get('o2');

      let total = 0;
      let lines = productO1.filter(p => p.id.toString() == o1);
      if (!!o2) {
        lines = lines.concat(productO2.filter(p => p.id.toString() == o2))
      }
      lines.forEach((l: any) => {
        total += (l.product ? l.product.price : parseInt(l.service.price)) * l.quantity;
      })
      setTotalPrice(total);
      setCartLines(lines);

    })()
  }, []);

  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Giỏ hàng" : "Cart"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className="bg-[#F0F0F0]"
        style={{
          backgroundSize: 'cover',
          backgroundImage: 'url(https://d3e4m6b6rxmux9.cloudfront.net/Rectangle_358_a9729888a4.png)'
        }}
      >
        {lines.length === 0 ? (
          <div className="container mx-auto">
            <div className="p-10">
              <img src="https://d3e4m6b6rxmux9.cloudfront.net/preview_b0649685a8.png?updated_at=2023-07-04T09:54:15.960Z" className='rounded-lg w-[250px] mx-auto' />
              <h1 className="text-xl text-gray-600 text-center font-bold">
                {locale === "en" ? "No items in the shopping cart" : "Không có sản phẩm trong giỏ hàng"}
              </h1>
              <div className="mt-8 flex justify-center">
                <a href="/">
                  <button className="bg-[#166534] text-white px-10 max-sm:w-full py-2 rounded-md hover:bg-green-700 font-bold">{locale == "en" ? "Continue shopping" : "Tiếp tục mua hàng"}</button>
                </a>
              </div>
            </div>
          </div>
        ) : (
          <div className="container mx-auto text-sm font-sans">
            <div className="lg:flex hidden max-w-7xl mx-auto pb-10 pt-5 opacity-95">
              <div className="bg-white w-8/12 mr-6 h-full shadow-2xl relative">
                <div className="flex px-5 pb-3 pt-3 font-bold border-t text-lg">
                  <div className="w-[80%]">{locale === "en" ? "Product" : "Sản phẩm"}</div>
                  <div className="w-[20%] text-center">{locale === "en" ? "Price" : "Giá tiền"}</div>
                </div>
                {step == 2 && <DotLottiePlayer
                  src="https://lottie.host/66ea44be-551b-4419-af50-57300a105d33/X8wCSMUSiR.json"
                  loop
                  autoplay
                  speed={1}
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    opacity: 0.7
                  }}
                />}
                {lines.map((line: any, index: number) =>
                  <div className="flex px-5" key={index}>
                    <div className="w-[80%]">
                      <p className="mt-[6px] font-medium text-base">{locale === "en" ? (line.product ? line.product.en_label : line.service.en_label) : (line.product ? line.product.label : line.service.label)}</p>
                    </div>
                    <div className="w-[20%] text-center mt-[6px] text-green-900 font-medium text-base">{numberWithCommas((line.product ? line.product.price : line.service.price) * line.quantity)}{locale === "en" ? " VNĐ" : "đ"}</div>
                  </div>

                )}
                <div className="w-full text-left p-1 rounded-md border-b px-5 py-3 text-base">
                  <p className="text-left mb-2 font-semibold text-base">{locale === "en" ? "Who's the gift for?" : "Thông tin người nhận món quà sức khỏe này?"}</p>
                  <div className="flex items-center">
                    <span className="w-[350px] mr-2 text-base">{locale === "en" ? "Your name" : "Tên của bạn"}<span className="text-rose-600">*</span>: </span>
                    <input
                      disabled={step == 2}
                      type="text"
                      className="
                        form-control
                        block
                        w-full
                        px-3
                        py-1.5
                        text-base
                        font-normal
                        text-gray-700
                        bg-white bg-clip-padding
                        border border-solid border-gray-300
                        rounded
                        transition
                        ease-in-out
                        m-0
                        focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                      "
                      id="exampleFormControlInput1"
                      onChange={(e) => { setSenderName(e.target.value); console.log('e', senderName) }}
                    />
                  </div>
                  {verified && senderName == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi biết tên của bạn." : "Please let us know your name."}</p>}
                  <div className="flex items-center">
                    <span className="w-[350px] mr-2 text-base">{locale === "en" ? "Your email" : "Email của bạn"}<span className="text-rose-600">*</span>: </span>
                    <input
                      disabled={step == 2}
                      type="text"
                      className="
                    form-control
                    mt-3
                    mb-3
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                      id="exampleFormControlInput1"
                      onChange={(e) => { setSenderEmail(e.target.value) }}
                    />
                  </div>
                  {verified && senderEmail == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi biết email của bạn." : "Please let us know your email."}</p>}


                  <div className="flex items-center">
                    <span className="w-[350px] mr-2 text-base">{locale === "en" ? "Receiver's name" : "Tên người thân của bạn"}<span className="text-rose-600">*</span>: </span>
                    <input
                      disabled={step == 2}
                      type="text"
                      className="
                    form-control
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                      id="exampleFormControlInput1"
                      onChange={(e) => { setReceiverName(e.target.value) }}
                    />
                  </div>
                  {verified && receiverName == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi biết tên người nhận." : "Please let us know receiver's name"}</p>}

                  <div className="flex items-center">
                    <span className="w-[350px] mr-2 text-base">{locale === "en" ? "Receiver's phone number" : "Số điện thoại của người thân"}<span className="text-rose-600">*</span>: </span>
                    <input
                      disabled={step == 2}
                      type="text"
                      className="
                    form-control
                    mt-3
                    mb-3
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                      id="exampleFormControlInput1"
                      onChange={(e) => { setReceiverPhone(e.target.value) }}
                    />
                  </div>
                  {verified && receiverPhone == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi số điện thoại người nhận." : "Please let us know receiver's phone number"}</p>}


                  <div className="mt-2">
                    <p className="mb-2">{locale == "en" ? "What message would you like to include?" : "Thông điệp bạn muốn nhắn nhủ đến người nhận?"}<span className="text-rose-600">*</span>:</p>
                    <textarea disabled={step == 2} onChange={e => setMessage(e.target.value)} className="w-full h-32 border border-1 p-2" />
                  </div>
                  {verified && message == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi lời nhắn của bạn." : "Please let us know your message."}</p>}

                </div>
              </div>

              <div className="bg-white w-4/12 shadow-2xl h-full text-[16px] sticky top-[80px]">
                <div className="p-5">
                  <div className="flex">
                    <div className="w-1/2 text-left">{locale === "en" ? "Total price" : "Tổng tiền"}</div>
                    <div className="w-1/2 text-right text-green-800 font-semibold">{numberWithCommas(totalPrice)} {locale === "en" ? " VND" : "VND"}</div>
                  </div>
                  <div className="flex mt-2">
                    <div className="w-1/2 text-left">{locale === "en" ? "Discount" : "Giảm giá"}</div>
                    <div className="w-1/2 text-right text-green-800 font-semibold"> {locale === "en" ? "0 VND" : "0 VND"}</div>
                  </div>
                  <hr className="my-4" />
                  <div className="flex mt-2 text-lg">
                    <div className="w-1/2 text-left font-semibold">{locale === "en" ? "Provisional" : "Tạm tính"}</div>
                    <div className="w-1/2 text-right text-green-800 font-semibold">{numberWithCommas(totalPrice)} {locale === "en" ? " VND" : "VND"}</div>
                  </div>

                  <button
                    className="w-full mt-4"
                    onClick={async e => {
                      setVerified(true);
                      if (!senderName || !senderEmail || !receiverName || !receiverPhone || !message) {
                        toast.error(locale == "vi" ? "Xin vui lòng nhập đầy đủ thông tin cần thiết." : "Please fill out required fields")
                        return;
                      }
                      (e.target as HTMLElement).remove();
                      setStep(2);
                    }}>

                    <span
                      // onClick={handleNav}
                      style={{
                        backgroundColor: "rgb(22 101 52)",
                        color: "white",
                        marginLeft: 0,
                        display: 'block',
                        letterSpacing: '1px'
                      }}
                      className="rounded-full max-[1400px]:text-xs text-base full-w block text-center font-bold mt-1 flex px-2 py-3 text-black leading-tight uppercase shadow-md hover:bg-green-700 hover:shadow-lg focus:bg-green-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg transition duration-150 ease-in-out bg-green-800"
                    >
                      {locale === "en" ? "CONTINUE" : "TIẾP TỤC"}
                    </span>
                  </button>
                  {step == 2 && <p className="text-center">{locale == "vi" ? "Cám ơn bạn đã chọn ECHO MEDI" : "Thank You for choosing Us"}</p>}
                  {step == 2 && <p className="text-center">{locale == "vi" ? "Mời bạn thanh toán để hoàn tất" : "Proceed with the payment"}</p>}
                </div>
              </div>
            </div>
            <div className="lg:hidden w-full">
              <div className="h-full">
                <div style={{
                  height: 'calc(100vh - 160px)',
                  overflow: 'scroll',
                }} className="bg-white h-full shadow-2xl relative">
                  <div className="grid grid-cols-2 pl-5 pt-4 pb-4 pr-5">
                    <div className="col-span-1 flex">
                      <p className="font-bold text-[16px]">{locale === "en" ? "Your shopping cart" : "Giỏ hàng của bạn"}</p></div>
                  </div>
                  {step == 2 && <DotLottiePlayer
                    src="https://lottie.host/66ea44be-551b-4419-af50-57300a105d33/X8wCSMUSiR.json"
                    loop
                    autoplay
                    speed={1}
                    style={{
                      position: 'absolute',
                      width: '100%',
                      height: '100%',
                      opacity: 0.7
                    }}
                  />}
                  {lines.map((line: any, index: number) =>
                    <div className="flex px-5 py-1">
                      <div className="w-12/12">
                        <p className="font-bold text-base">{locale === "en" ? (line.product ? line.product.en_label : line.service.en_label) : (line.product ? line.product.label : line.service.label)} - {numberWithCommas((line.product ? line.product.price : line.service.price) * line.quantity)}</p>
                      </div>
                    </div>
                  )}
                  <div className="w-full text-left p-1 rounded-md border-b px-5 py-3 text-base">
                    <p className="text-left mb-2  text-base">{locale === "en" ? "Who's the gift for?" : "Thông tin người nhận món quà sức khỏe này?"}</p>
                    <div className="flex flex-col items-start">
                      <span className="mr-2 text-base">{locale === "en" ? "Your name" : "Tên của bạn"}<span className="text-rose-600">*</span>: </span>
                      <input
                        disabled={step == 2}
                        type="text"
                        className="
                        form-control
                        block
                        w-full
                        px-3
                        py-1.5
                        text-base
                        font-normal
                        text-gray-700
                        bg-white bg-clip-padding
                        border border-solid border-gray-300
                        rounded
                        transition
                        ease-in-out
                        m-0
                        focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                      "
                        id="exampleFormControlInput1"
                        onChange={(e) => { setSenderName(e.target.value); }}
                      />
                    </div>
                    {verified && senderName == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi biết tên của bạn." : "Please let us know your name."}</p>}
                    <div className="flex flex-col items-start">
                      <span className="mr-2 text-base">{locale === "en" ? "Your email" : "Email của bạn"}<span className="text-rose-600">*</span>: </span>
                      <input
                        disabled={step == 2}
                        type="text"
                        className="
                    form-control
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                        id="exampleFormControlInput1"
                        onChange={(e) => { setSenderEmail(e.target.value) }}
                      />
                    </div>
                    {verified && senderEmail == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi biết email của bạn." : "Please let us know your email."}</p>}


                    <div className="flex flex-col items-start">
                      <span className="mr-2 text-base">{locale === "en" ? "Receiver's name" : "Tên người thân của bạn"}<span className="text-rose-600">*</span>: </span>
                      <input
                        disabled={step == 2}
                        type="text"
                        className="
                    form-control
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                        id="exampleFormControlInput1"
                        onChange={(e) => { setReceiverName(e.target.value) }}
                      />
                    </div>
                    {verified && receiverName == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi biết tên người nhận." : "Please let us know receiver's name"}</p>}

                    <div className="flex flex-col items-start">
                      <span className="mr-2 text-base">{locale === "en" ? "Receiver's phone number" : "Số điện thoại của người thân"}<span className="text-rose-600">*</span>: </span>
                      <input
                        disabled={step == 2}
                        type="text"
                        className="
                    form-control
                    block
                    w-full
                    px-3
                    py-1.5
                    text-base
                    font-normal
                    text-gray-700
                    bg-white bg-clip-padding
                    border border-solid border-gray-300
                    rounded
                    transition
                    ease-in-out
                    m-0
                    focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none
                  "
                        id="exampleFormControlInput1"
                        onChange={(e) => { setReceiverPhone(e.target.value) }}
                      />
                    </div>
                    {verified && receiverPhone == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi số điện thoại người nhận." : "Please let us know receiver's phone number"}</p>}


                    <div className="mt-2">
                      <p className="mb-2">{locale == "en" ? "What message would you like to include?" : "Thông điệp bạn muốn nhắn nhủ đến người nhận?"}<span className="text-rose-600">*</span>:</p>
                      <textarea disabled={step == 2} onChange={e => setMessage(e.target.value)} className="w-full h-32 border border-1" />
                    </div>
                    {verified && message == "" && <p className="text-right text-xs text-orange-600">{locale == "vi" ? "Hãy cho chúng tôi lời nhắn của bạn." : "Please let us know your message."}</p>}

                  </div>
                </div>
              </div>
              <div className="bg-white first-letter:shadow-2xl w-full text-[16px] fixed bottom-0 overflow-scroll max-h-full left-0">

                <div className="p-4 pb-0">

                  <div className="flex">
                    <div className="w-1/2 text-left text-gray-500">
                      {locale === "en" ? "Total price" : "Tổng tiền"}
                    </div>
                    <div className="w-1/2 text-right text-green-800 font-semibold">
                      {numberWithCommas(totalPrice)}
                      {locale === "en" ? " VNĐ" : "đ"}
                    </div>
                  </div>
                </div>
                <button
                  className="my-4 px-2 m-auto w-[200px]"
                  onClick={async e => {
                    setVerified(true);
                    if (!senderName || !senderEmail || !receiverName || !receiverPhone || !message) {
                      toast.error(locale == "vi" ? "Xin vui lòng nhập đầy đủ thông tin cần thiết." : "Please fill out required fields")
                      return;
                    }
                    (e.target as HTMLElement).remove();
                    setStep(2);
                  }}>

                  <span
                    // onClick={handleNav}
                    style={{
                      backgroundColor: "rgb(22 101 52)",
                      color: "white",
                      marginLeft: 0,
                      display: 'block',
                      letterSpacing: '1px'
                    }}
                    className="rounded-full text-base full-w block text-center font-bold mt-1 flex px-2 py-3 text-black leading-tight uppercase shadow-md hover:bg-green-700 hover:shadow-lg focus:bg-green-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg transition duration-150 ease-in-out bg-green-800"
                  >
                    {locale === "en" ? "CONTINUE" : "TIẾP TỤC"}
                  </span>
                </button>
                {step == 2 && <p className="text-center">{locale == "vi" ? "Cám ơn bạn đã chọn ECHO MEDI" : "Thank You for choosing Us"}</p>}
                {step == 2 && <p className="text-center">{locale == "vi" ? "Mời bạn thanh toán để hoàn tất" : "Proceed with the payment"}</p>}
              </div>

            </div>
          </div>
        )}
      </div>
      <div className="lg:block hidden"><Contact /></div>
    </>
  );
};

export default Home;
