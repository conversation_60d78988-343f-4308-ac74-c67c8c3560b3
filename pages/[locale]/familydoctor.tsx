import { NextPage } from 'next';
import Image from 'next/image'
import { useRouter } from 'next/router';
import React, { useState } from 'react'
import { shimmer, toBase64 } from '../../lib/ui';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper';
import { Toaster } from "react-hot-toast";
import Contact from "../../components/Contact/Contact";
import { dataImage, dataImageCompany } from '../../utils/dataBusiness';
import useIsMobile from '../../utils/detectMob';
import { makeStaticProps } from '../../lib/getStatic';
import BookingBusiness from '../../components/BookingBusiness';
import Head from 'next/head';
import ModalBooking from '../../components/BookingService';
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }

const FamilyDoctorPages: NextPage = () => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
    const isMobile = useIsMobile();
    const [showModal, setShowModal] = useState(false);
    const [message, setMessage] = useState("");
    const handleShowModal = () => {
        setShowModal(true);
        setMessage("Khách hàng Đăng ký tư vấn doanh nghiệp")
    };
    const services = dataFamilyDocTorIcon(locale);
    return (
        <>
            <Head>
                <title>ECHO MEDI - Bác Sĩ Gia Đình</title>
                <meta name="description" content="Tìm hiểu về dịch vụ bác sĩ gia đình của Echo Medi, cung cấp chăm sóc sức khỏe tại nhà và dịch vụ y tế toàn diện cho cả gia đình bạn." />
                <meta name="keywords" content="Echo Medi, bác sĩ gia đình, chăm sóc sức khỏe tại nhà, y tế cho gia đình, dịch vụ y tế" />
                <meta property="og:title" content="Echo Medi - Bác Sĩ Gia Đình Tin Cậy" />
                <meta property="og:description" content="Khám phá dịch vụ bác sĩ gia đình từ Echo Medi, hỗ trợ sức khỏe và điều trị ngay tại nhà." />
                <meta property="og:image" content="/banner/Family-Doctor-VIE.webp" />
                <meta property="og:type" content="website" />
                <meta property="twitter:card" content="summary_large_image" />
                <meta property="twitter:title" content="Dịch Vụ Bác Sĩ Gia Đình - Echo Medi" />
                <meta property="twitter:description" content="Echo Medi cung cấp dịch vụ bác sĩ gia đình toàn diện, chăm sóc sức khỏe tận nơi cho gia đình bạn." />
                <meta property="twitter:image" content="/banner/Family-Doctor-VIE.webp" />
            </Head>
            <section className='block md:hidden'>
                <div className="mx-auto">
                    <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
                        <div className="w-full relative">
                            <Image
                                src="/banner/banner_new.webp"
                                alt="Banner"
                                width={1920}
                                height={300}
                                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 500))}`}
                                className="object-center"
                                layout="responsive"
                            />
                        </div>
                        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 2xl:w-1/2 absolute md:px-16 px-4">
                            <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                                {locale === "vi" ? "BÁC SĨ GIA ĐÌNH" : "Family Doctor"}
                            </h2>
                        </div>
                    </div>
                    <p className="text-sm my-6 text-justify px-4 block md:hidden">
                        {locale === "en" ? "Family doctors monitor the health of each family member, promptly detect issues, provide advice to improve quality of life, identify abnormal signs early, and reduce treatment costs, bringing numerous health benefits." : "Bác Sĩ Gia Đình đồng hành chăm sóc và theo dõi sức khỏe cho từng khách hàng, giúp phát hiện sớm các vấn đề, phòng ngừa nguy cơ, từ đó tiết kiệm thời gian và chi phí, giảm thiểu việc lạm dụng thuốc và xét nghiệm không cần thiết."}
                    </p>
                </div>
            </section>
            <section className="relative mt-6">
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto">
                    <section className='hidden md:block'>
                        <h3 className="text-2xl md:text-4xl text-center text-[#156634] font-bold my-4">{locale === "en" ? "Family Doctor" : "Bác sĩ gia đình"}</h3>
                        <p className="mb-3 text-lg text-center align-middle">
                            {locale === "en" ? "Family doctors monitor the health of each family member, detect issues early, offer advice to improve quality of life, identify abnormal signs, and reduce treatment costs, providing numerous health benefits." : "Bác Sĩ Gia Đình đồng hành chăm sóc và theo dõi sức khỏe cho từng khách hàng, giúp phát hiện sớm các vấn đề, phòng ngừa nguy cơ, từ đó tiết kiệm thời gian và chi phí, giảm thiểu việc lạm dụng thuốc và xét nghiệm không cần thiết."}
                        </p>
                    </section>
                </div>
                {isMobile ? (
                    <div className="px-4 mx-auto bg-body-family-doctor pt-8">
                        <ul role="list">
                            {services.map((item, index) => (
                                <li key={item.id}>
                                    <div className="relative pb-8">
                                        {index < services.length - 1 && (
                                            <span className="absolute left-6 top-4 -ml-px h-full w-0.5 border-l border-dashed border-[#C8EAD4]"></span>
                                        )}
                                        <div className="relative flex space-x-3">
                                            <div>
                                                <span className="h-12 w-12 rounded-full flex items-center justify-center bg-white">
                                                    <img src={item.image} alt="" className="w-10 h-10" />
                                                </span>
                                            </div>
                                            <div className="flex-col">
                                                <h4 className="text-base md:text-center font-medium">{item.title}</h4>
                                                <p className="text-sm md:text-center text-justify">{item.desc}</p>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                ) : (
                    <div className="max-w-screen-2xl md:px-16 px-4 mx-auto mt-12">
                        <div className="grid md:grid-cols-2 bg-white rounded-[18px]">
                            {services.map((service, index) => (
                                <div
                                    key={service.id}
                                    className={`p-4 flex items-start justify-center gap-4 ${index % 2 === 0 ? 'ml-8' : 'mr-8'} ${index < services.length - 2 ? 'border-b-[1px]' : ''
                                        } ${index % 2 === 0 ? 'border-r-[1px]' : ''} ${index >= 2 ? 'mb-8' : 'mt-8'}`}
                                >
                                    <div>
                                        <div className={`h-20 bg-white rounded-xl shadow-2xl flex items-center justify-center w-20`}>
                                            <img src={service.image} alt="icon family doctor" className='object-cover' />
                                        </div>
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-xl mb-2">{service.title}</h3>
                                        <p className="text-base">{service.desc}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                <div className="w-full max-w-screen-2xl md:px-16 px-4 mx-auto my-16">
                    <div className='flex items-start gap-8 flex-col md:flex-row'>
                        <div className="bg-white md:p-8 p-4 md:w-1/2 w-full rounded-2xl order-2 md:order-1">
                            {/* <p className="text-sm md:text-lg font-medium mb-4">{locale === "en" ? "For just 1,000,000 VND per year (original price: 3,000,000 VND), you will receive the following benefits:" : "Chỉ với 1.000.000 VND/năm (giá gốc 3.000.000 VND), bạn sẽ nhận được:"}</p> */}
                            <section className="space-y-2">
                                {benefitsData.map((benefit, index) => (
                                    <div key={index} className="flex items-center gap-1">
                                        <div>
                                            <svg width="45" height="44" viewBox="0 0 45 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g filter="url(#filter0_d_9661_11508)">
                                                    <rect x="8.41016" y="4.99992" width="28.0089" height="28" rx="7.86426" fill="white" />
                                                </g>
                                                <path fillRule="evenodd" clipRule="evenodd" d="M30.1063 14.9764C30.5028 15.3608 30.5125 15.9939 30.1281 16.3904L22.8553 23.8904L22.1656 24.6017L21.4479 23.9186L16.7207 19.4186C16.3207 19.0378 16.3051 18.4048 16.6859 18.0048C17.0666 17.6048 17.6996 17.5892 18.0996 17.97L22.1093 21.7868L28.6923 14.9981C29.0767 14.6016 29.7098 14.5919 30.1063 14.9764Z" fill="#14813D" />
                                                <defs>
                                                    <filter id="filter0_d_9661_11508" x="0.545898" y="0.281369" width="43.7383" height="43.7285" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                                                        <feFlood floodOpacity="0" result="BackgroundImageFix" />
                                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                                        <feOffset dy="3.1457" />
                                                        <feGaussianBlur stdDeviation="3.93213" />
                                                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
                                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9661_11508" />
                                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9661_11508" result="shape" />
                                                    </filter>
                                                </defs>
                                            </svg>
                                        </div>
                                        <div>
                                            <p className='text-sm md:text-base'>{locale === "en" ? benefit.en : benefit.vi}</p>
                                        </div>
                                    </div>
                                ))}
                            </section>
                            <div className="flex gap-4 justify-center mt-4">
                                <button onClick={() => handleShowModal()} className="md:px-16 px-10 md:py-2 py-1 rounded-full border border-[#156634]">
                                    <span className="text-center text-[#156634] text-sm md:text-base font-medium">{locale === "en" ? "Booking" : "Đặt lịch"}</span>
                                </button>
                                <button className="md:px-8 px-4 md:py-2 py-1 rounded-full bg-[#156634] hover:bg-[#14813d]">
                                    <a href="tel:1900638408" className="text-center text-white text-sm md:text-base font-medium">{locale === "en" ? "Contact us for consultation" : "Liên hệ tư vấn"}</a>
                                </button>
                            </div>
                        </div>
                        <div className="md:w-1/2 w-full order-1 md:order-2">
                            <div className='swiper slide'>
                                <Swiper
                                    slidesPerView={1}
                                    className="mySwiper"
                                    pagination={{
                                        clickable: true,
                                    }}
                                    modules={[Pagination]}
                                >
                                    {dataImageFamily.map((data) => (
                                        <SwiperSlide>
                                            <img className="object-cover w-full bg-[#F8F9FB] pl-8" src={data.img} />
                                        </SwiperSlide>
                                    ))}
                                </Swiper>
                            </div>
                            <div className="max-w-3xl mx-auto md:px-4">
                                <figure className="relative">
                                    <span className="absolute -top-2 md:-left-4 -left-1 text-6xl text-[#156634] font-serif"><svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g opacity="0.4">
                                            <path d="M11.8023 19.0243C11.8023 20.1408 11.4115 21.0759 10.63 21.8295C9.84841 22.5832 8.87146 22.96 7.69913 22.96C6.58262 22.96 5.63358 22.5273 4.85202 21.662C4.07047 20.7968 3.67969 19.7082 3.67969 18.3963C3.67969 17.4472 3.88903 16.4005 4.30772 15.2561C4.75433 14.1116 5.32654 12.9533 6.02436 11.7809C6.75009 10.6086 7.55956 9.45021 8.45277 8.30579C9.37389 7.13345 10.295 6.04485 11.2161 5.03999L12.3466 5.96111C11.0905 7.44049 10.0438 8.93382 9.20642 10.4411C8.36903 11.9484 7.8666 13.4278 7.69913 14.8792C8.81564 14.963 9.77863 15.3817 10.5881 16.1353C11.3976 16.889 11.8023 17.852 11.8023 19.0243ZM23.8188 19.0243C23.8188 20.1408 23.428 21.0759 22.6464 21.8295C21.8649 22.5832 20.8879 22.96 19.7156 22.96C18.5991 22.96 17.65 22.5273 16.8685 21.662C16.0869 20.7968 15.6961 19.7082 15.6961 18.3963C15.6961 17.4472 15.9055 16.4005 16.3242 15.2561C16.7708 14.1116 17.343 12.9533 18.0408 11.7809C18.7665 10.6086 19.576 9.45021 20.4692 8.30579C21.3903 7.13345 22.3115 6.04485 23.2326 5.03999L24.3212 5.96111C23.0651 7.44049 22.0184 8.93382 21.181 10.4411C20.3715 11.9484 19.8831 13.4278 19.7156 14.8792C20.8321 14.963 21.7951 15.3817 22.6045 16.1353C23.414 16.889 23.8188 17.852 23.8188 19.0243Z" fill="#14813D" />
                                        </g>
                                    </svg>
                                    </span>
                                    <blockquote className="text-[#156634] font-medium text-sm md:text-base text-center px-8 pt-4">
                                        {locale === "en" ? "ECHO MEDI prioritizes comprehensive healthcare, focusing on both physical and mental well-being, with disease treatment being just one aspect." : " ECHO MEDI tập trung vào chăm sóc sức khỏe toàn diện cả về thể chất và tinh thần mà điều trị bệnh lý chỉ là một phần trong đó."}
                                    </blockquote>
                                    <span className="absolute -bottom-2 md:-right-4 -right-1 text-6xl text-[#156634] font-serif">
                                        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <g opacity="0.4">
                                                <path d="M16.1977 8.97571C16.1977 7.8592 16.5885 6.92412 17.37 6.17048C18.1516 5.41683 19.1285 5.04001 20.3009 5.04001C21.4174 5.04001 22.3664 5.47266 23.148 6.33795C23.9295 7.20325 24.3203 8.29185 24.3203 9.60375C24.3203 10.5528 24.111 11.5995 23.6923 12.7439C23.2457 13.8884 22.6735 15.0467 21.9756 16.2191C21.2499 17.3914 20.4404 18.5498 19.5472 19.6942C18.6261 20.8665 17.705 21.9551 16.7839 22.96L15.6534 22.0389C16.9095 20.5595 17.9562 19.0662 18.7936 17.5589C19.631 16.0516 20.1334 14.5722 20.3009 13.1208C19.1844 13.037 18.2214 12.6183 17.4119 11.8647C16.6024 11.111 16.1977 10.148 16.1977 8.97571ZM4.18125 8.97571C4.18125 7.8592 4.57203 6.92412 5.35359 6.17048C6.13514 5.41683 7.11209 5.04001 8.28443 5.04001C9.40094 5.04001 10.35 5.47266 11.1315 6.33795C11.9131 7.20325 12.3039 8.29185 12.3039 9.60375C12.3039 10.5528 12.0945 11.5995 11.6758 12.7439C11.2292 13.8884 10.657 15.0467 9.95919 16.2191C9.23346 17.3914 8.42399 18.5498 7.53078 19.6942C6.60966 20.8665 5.68854 21.9551 4.76742 22.96L3.67882 22.0389C4.93489 20.5595 5.98162 19.0662 6.819 17.5589C7.62848 16.0516 8.11695 14.5722 8.28443 13.1208C7.16792 13.037 6.20492 12.6183 5.39546 11.8647C4.58598 11.111 4.18125 10.148 4.18125 8.97571Z" fill="#14813D" />
                                            </g>
                                        </svg>

                                    </span>
                                </figure>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            {showModal && (
              <ModalBooking
                visible={showModal}
                onClose={() => setShowModal(false)}
                currentBlog={{
                    title: 'Bác Sĩ Gia Đình',
                    title_en: 'Family Doctor'
                  }}
                locale={locale}
              />
            )}
            <Contact />
            <Toaster position="bottom-center" />
        </>
    )
}
export default FamilyDoctorPages;

const benefitsData = [
    {
        en: "1 free consultation with a doctor at the clinic",
        vi: "Một buổi thăm khám trực tiếp cùng bác sĩ tại phòng khám",
    },
    {
        en: "12 online healthcare consultations each year",
        vi: "Tư vấn và chăm sóc sức khỏe trực tuyến 12 lần/năm",
    },
    {
        en: "Secure lifetime storage of your medical records",
        vi: "Bảo mật và lưu trữ hồ sơ khám trọn đời",
    },
    {
        en: "A 10% discount on additional examinations and testing services",
        vi: "Giảm 10% cho dịch vụ thăm khám và xét nghiệm khác"
    }
]

const dataFamilyDocTorIcon = (locale: string) => [
    {
        id: 1,
        image: "https://api.echomedi.com/uploads/1_22512afd42.png",
        title: locale === "en" ? "Personalized Heallthcare Plan" : "Chăm sóc sức khỏe cá nhân hóa",
        desc: locale === "en"
            ? "A family physician designs and manages a personalized healthcare plan based on medical history, lifestyle, and habits to enhance and maintain overall health."
            : "Bác sĩ gia đình thiết kế và theo dõi kế hoạch chăm sóc sức khỏe cá nhân hóa, dựa trên tiền sử bệnh, lối sống và thói quen, nhằm cải thiện và duy trì sức khỏe tốt.",
    },
    {
        id: 2,
        image: "https://api.echomedi.com/uploads/2_04cd39c003.png",
        title: locale === "en" ? "Convenient Online Health Examination" : "Thăm khám sức khỏe trực tuyến tiện lợi",
        desc: locale === "en"
            ? "Online health evaluations offer convenience with flexible timing, catering to busy schedules and individual healthcare needs."
            : "Thăm khám sức khỏe trực tuyến mang đến sự tiện lợi với thời gian linh hoạt, phù hợp với lịch trình bận rộn và nhu cầu chăm sóc sức khỏe cá nhân.",
    },
    {
        id: 3,
        image: "https://api.echomedi.com/uploads/3_bfb28fe07c.png",
        title: locale === "en" ? "Early detection of abnormalities" : "Phát hiện sớm những dấu hiệu bất thường",
        desc: locale === "en"
            ? "Continuous monitoring allows family doctors to detect abnormal health issues early and take timely action, especially for serious conditions like cancer and cardiovascular diseases."
            : "Việc theo dõi liên tục cho phép bác sĩ gia đình phát hiện sớm các vấn đề sức khỏe bất thường và can thiệp kịp thời, đặc biệt đối với những bệnh nghiêm trọng như ung thư, tim mạch...",
    },
    {
        id: 4,
        image: "https://api.echomedi.com/uploads/4_087eedd5ce.png",
        title: locale === "en" ? "Comprehensive solution at a reasonable cost" : "Giải pháp toàn diện với chi phí hợp lý",
        desc: locale === "en"
            ? "Regular health monitoring allows for early detection and prompt treatment, saving time and costs while improving healthcare efficiency."
            : "Theo dõi sức khỏe thường xuyên giúp phát hiện sớm và điều trị kịp thời, tiết kiệm chi phí và thời gian, nâng cao hiệu quả chăm sóc sức khỏe.",
    },
];

const dataImageFamily = [
    {
        id: 1,
        img: "https://api.echomedi.com/uploads/BSGD_ECHOMEDI_1_b27a0ff62b.png"
    },
    {
        id: 2,
        img: "https://api.echomedi.com/uploads/BSGD_ECHOMEDI_2_c081307ff0.png"
    },
    {
        id: 3,
        img: "https://api.echomedi.com/uploads/BSGD_ECHOMEDI_3_dbb275f70a.png"
    },
    {
        id: 4,
        img: "https://api.echomedi.com/uploads/BSGD_ECHOMEDI_4_61ef640f8b.png"
    },
    {
        id: 5,
        img: "https://api.echomedi.com/uploads/BSGD_ECHOMEDI_5_b1f08f098b.png"
    },
    {
        id: 6,
        img: "https://api.echomedi.com/uploads/BSGD_ECHOMEDI_6_3be7186079.png"
    },

];