import type { NextPage } from "next";
import Head from "next/head";
import { useRouter } from 'next/router';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import Contact from "../../components/Contact/Contact";
import LinkComponent from "../../components/Link";
import useUserData from "../../hooks/useUserData";
import useToken from "../../hooks/useToken";
import MenuSlider from "../../components/LayoutUser/LayoutUser";
import { menuItems } from "./personal_information";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})

export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
  const { token } = useToken();
  const { userData } = useUserData(token);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const router = useRouter();
  const locale = (router.query.locale as string) || "vi";

  const handleTogglePassword = (field: "current" | "new" | "confirm") => {
    setShowPasswords((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const handleChangePassword = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      toast.error(locale === "en" ? "Please fill in all fields" : "Thông tin không phù hợp");
      return;
    }
    if (newPassword !== confirmPassword) {
      toast.error(locale === "en" ? "Passwords do not match" : "Mật khẩu không khớp");
      return;
    }

    const toastId = toast.loading(locale === "en" ? "Loading..." : "Đang xử lý...");
    axios
      .post(
        "https://api.echomedi.com/api/auth/change-password",
        {
          currentPassword,
          password: newPassword,
          passwordConfirmation: confirmPassword,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      )
      .then(() => {
        toast.success(locale === "en" ? "Password changed successfully" : "Thay đổi mật khẩu thành công");
      })
      .catch(() => {
        toast.error(locale === "en" ? "Failed to change password" : "Không thể thay đổi mật khẩu");
      })
      .finally(() => {
        toast.dismiss(toastId);
      });
  };
  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Thay Đổi Mật Khẩu" : "Change Password"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <div className="bg-[#FAFBFD] 2xl:container 2xl:mx-auto lg:px-20 my-4 md:px-6 px-2">
        <section>
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="inline-flex items-center -space-x-1 md:space-x-1">
              <li className="inline-flex items-center">
                <LinkComponent locale={locale} skipLocaleHandling={false} href="/">
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Home" : "Trang chủ"}
                  </span>
                </LinkComponent>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="md:mx-1 w-5 h-[12px] md:h-5 mt-1 md:mt-0" viewBox="0 0 5 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.12561 1.13672L0.999943 18.8633" stroke="#E5E7EB" strokeWidth="1.6" strokeLinecap="round" />
                  </svg>
                  <span className="inline-flex items-center text-[#8E8E8E] hover:underline hover:text-[#156634] text-[8px] mt-1 md:mt-0 md:text-xs font-normal whitespace-nowrap">
                    {locale === "en" ? "Change Password" : "Thay đổi mật khẩu"}
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </section>
        <div className="flex md:flex-row flex-col gap-8 mt-4">
          <div className="md:w-64 md:h-2/3 bg-white shadow-md rounded-2xl hidden md:block">
            <div className="p-4 flex flex-col items-center justify-center">
              <svg width="111" height="111" viewBox="0 0 111 111" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M110.083 55.4993C110.083 85.6449 85.6449 110.083 55.4993 110.083C25.3538 110.083 0.916016 85.6449 0.916016 55.4993C0.916016 25.3538 25.3538 0.916016 55.4993 0.916016C85.6449 0.916016 110.083 25.3538 110.083 55.4993ZM71.8743 39.1243C71.8743 48.168 64.543 55.4993 55.4993 55.4993C46.4557 55.4993 39.1243 48.168 39.1243 39.1243C39.1243 30.0807 46.4557 22.7493 55.4993 22.7493C64.543 22.7493 71.8743 30.0807 71.8743 39.1243ZM55.4993 101.895C65.2372 101.895 74.2744 98.8951 81.7371 93.7688C85.0332 91.5047 86.4418 87.1917 84.5255 83.682C80.5528 76.4061 72.3669 71.8743 55.499 71.8743C38.6314 71.8743 30.4454 76.406 26.4727 83.6816C24.5563 87.1913 25.9649 91.5043 29.2609 93.7685C36.7237 98.895 45.7611 101.895 55.4993 101.895Z" fill="#14813D" />
              </svg>
              <p className='mt-4'>{userData?.patient?.full_name}</p>
              <p>{userData?.phone}</p>
            </div>
            <MenuSlider menuItems={menuItems} defaultActiveId="change-password" />
          </div>
          <div className="flex-1">
            <div className="bg-white rounded-lg md:p-6 pb-6">
              <h1 className="text-xl font-bold text-[#156634] md:mb-6 p-4">
                {locale === "en" ? "Change Password" : "Thay đổi mật khẩu"}
              </h1>
              <form className="w-full col-span-1 px-5" onSubmit={(e) => e.preventDefault()}>
                <div className="mb-6">
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-900 mb-2">
                    {locale === "en" ? "Current Password" : "Mật khẩu Hiện tại"}
                  </label>
                  <div className="relative">
                    <input
                      id="currentPassword"
                      type={showPasswords.current ? "text" : "password"}
                      className="bg-gray-50 border border-gray-300 rounded-lg w-full p-2.5"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                    />
                    <span
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={() => handleTogglePassword("current")}
                    >
                      {showPasswords ? (
                        <>
                          <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                          </svg>
                        </>
                      ) : (
                        <>
                          <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                          </svg>
                        </>
                      )}
                    </span>
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-900 mb-2">
                    {locale === "en" ? "New Password" : "Mật khẩu Mới"}
                  </label>
                  <div className="relative">
                    <input
                      id="newPassword"
                      type={showPasswords.new ? "text" : "password"}
                      className="bg-gray-50 border border-gray-300 rounded-lg w-full p-2.5"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                    />
                    <span
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={() => handleTogglePassword("new")}
                    >
                      {showPasswords ? (
                        <>
                          <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                          </svg>
                        </>
                      ) : (
                        <>
                          <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                          </svg>
                        </>
                      )}
                    </span>
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-900 mb-2">
                    {locale === "en" ? "Confirm New Password" : "Nhập Lại Mật khẩu Mới"}
                  </label>
                  <div className="relative">
                    <input
                      id="confirmPassword"
                      type={showPasswords.confirm ? "text" : "password"}
                      className="bg-gray-50 border border-gray-300 rounded-lg w-full p-2.5"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                    <span
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={() => handleTogglePassword("confirm")}
                    >
                      {showPasswords ? (
                        <>
                          <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                          </svg>
                        </>
                      ) : (
                        <>
                          <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                          </svg>
                        </>
                      )}
                    </span>
                  </div>
                </div>

                <div className="mt-10 flex justify-center relative">
                  <button
                    type="submit"
                    onClick={handleChangePassword}
                    className="text-[#156634] font-bold py-2 px-4 rounded-full border border-[#156634]"
                  >
                    {locale === "en" ? "Change Password" : "Thay đổi mật khẩu"}
                  </button>
                </div>
              </form>
            </div>
          </div>

        </div>
      </div>
      <Contact />
      <Toaster
        position="bottom-center"
      />
    </>
  );
};

export default Home;
