import type {
  InferGetStaticPropsType,
} from 'next';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import React, { useEffect, useState } from 'react';
import Image from 'next/image'
import { addToCart, shimmer, toBase64 } from '../../../lib/ui';

import { getStaticPathsPackagesChronicDiseases, getStaticPropsPackage } from '../../../lib/getStatic';

import { CardPackageItem, CardPackageSlideItem } from '../../../components/CardPackage/CardPackageItem';
import ModalBooking from '../../../components/BookingService';
export { getStaticPathsPackagesChronicDiseases as getStaticPaths, getStaticPropsPackage as getStaticProps };


const detectMob = () => {
  const toMatch = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i
  ];

  return toMatch.some((toMatchItem) => {
    return navigator.userAgent.match(toMatchItem);
  });
}

interface Chronicdiseases_package {
  id: string;
  label: string;
  label_en: string;
}

const Chronicdiseases_packages: Chronicdiseases_package[] = [
  { id: 'chronicdiseases_1', label: 'Nội Tiết - Chuyển Hoá', label_en: "Endocrinology - Metabolism" },
  { id: 'chronicdiseases_2', label: 'Tim Mạch - Thần Kinh', label_en: "Cardiovascular - Neurology" },
  { id: 'chronicdiseases_3', label: 'Tiêu Hoá', label_en: "Gastrointestinal" },
  { id: 'chronicdiseases_4', label: 'Hô Hấp', label_en: "Respiratory" },
  { id: 'chronicdiseases_5', label: 'Dị Ứng - Miễn Dịch', label_en: "Allergy - Immunity" },
  { id: 'chronicdiseases_6', label: 'Thận', label_en: "Nephrology" },
  { id: 'chronicdiseases_7', label: 'Nội Tiết - Chuyển Hoá', label_en: "Endocrinology - Metabolism" },
  { id: 'chronicdiseases_8', label: 'Tim Mạch - Thần kinh', label_en: "Cardiovascular - Neurology" },
  { id: 'chronicdiseases_9', label: 'Tiêu Hoá', label_en: "Gastrointestinal" },
  { id: 'chronicdiseases_10', label: 'Hô Hấp', label_en: "Respiratory" },
  { id: 'chronicdiseases_11', label: 'Dị Ứng - Miễn Dịch', label_en: "Allergy - Immunity" },
  { id: 'chronicdiseases_12', label: 'Thận', label_en: "Nephrology" },

];


const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsPackage>) => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [cnt, setCnt] = useState(detectMob() ? 2 : 8);
  const [showModal, setShowModal] = useState(false);
  const [message, setMessage] = useState("");
  const [currentBlog, setCurrentBlog] = useState({
    title: '',
    title_en: ''
  });
  const handleShowModal = (title: string, title_en: string) => {
    setCurrentBlog({
      title: title,
      title_en: title_en
    });
    setShowModal(true);
    setMessage("Khách hàng đặt lịch khám ở phòng khám về gói" + title)
  };
  const [activeStates, setActiveStates] = useState('')
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    const isMobile = detectMob();
    setActiveStates(isMobile ? 'chronicdiseases_7' : 'chronicdiseases_1');
  }, []);

  const handleSetActive = (key: string) => {
    setActiveStates(prevState => (prevState === key ? '' : key));
    const element = document.getElementById(key);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };
  const checkActiveSection = () => {
    const sections = document.querySelectorAll('section[id]');
    let currentId = '';

    sections.forEach((section) => {
      const rect = section.getBoundingClientRect();
      if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
        currentId = section.id;
      }
    });

    setActiveStates(currentId);
  };

  useEffect(() => {
    window.addEventListener('scroll', checkActiveSection);
    checkActiveSection();
    return () => {
      window.removeEventListener('scroll', checkActiveSection);
    };
  }, []);
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const handleClickDefault = (key: string) => (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    handleSetActive(key);
    const anchorElement = e.currentTarget;
    if (anchorElement) {
      anchorElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      });
    }
  };
  return (
    <>
      <Head>
        <title>{locale === "en" ? props.en_label : props.label}</title>
        <meta name="description" content={locale == "en" ? (props.en_desc ?? "") : (props.desc ?? "")} />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section>
        <div className="mx-auto noselect">
          <div className="flex justify-between items-center flex-col lg:flex-row gap-8 ">
            <div className="w-full relative">
              <Image
                src="/banner/banner_new.webp"
                alt="Banner"
                width={1920}
                height={1000}
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 1000))}`}
                className="object-cover"
              />
            </div>
            <div className="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 2xl:w-2/3 absolute md:px-16 px-4">
              <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase pb-2 hidden md:block">
                {locale === "vi" ? "CÁC GÓI QUẢN LÝ\n BỆNH MẠN TÍNH" : "Chronic Disease\n Management Packages"}
              </h2>
              <h2 className="text-left font-bold md:text-[28px] text-xl text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                {locale === "vi" ? "CÁC GÓI QUẢN LÝ\n BỆNH MẠN TÍNH" : "Chronic Disease\n Management Packages"}
              </h2>
              <p className="text-base mb-8 text-justify hidden md:block">
                {locale === "en" ? "Chronic diseases are long-term, progressive conditions that require ongoing monitoring and treatment, such as hypertension, diabetes, and hepatitis B. Without proper management, these conditions can lead to serious complications." : "Bệnh mạn tính là những bệnh lý tiến triển kéo dài, cần được theo dõi và điều trị liên tục như tăng huyết áp, đái tháo đường, viêm gan B... Nếu không được quản lý đúng cách, bệnh có thể gây ra nhiều biến chứng nguy hiểm."}
              </p>

              <section className='hidden md:block'>
                <div className="flex w-full flex-wrap gap-4">
                  {Chronicdiseases_packages.slice(0, 6).map((chronicdiseases_package) => (
                    <a
                      key={chronicdiseases_package.id}
                      href={`#${chronicdiseases_package.id}`}
                      onClick={handleClickDefault(chronicdiseases_package.id)}
                      className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === chronicdiseases_package.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                        }`}
                    >
                      {locale === "vi" ? chronicdiseases_package.label : chronicdiseases_package.label_en}
                    </a>
                  ))}
                </div>
              </section>
            </div>
          </div>
          {/* <p className="text-sm my-4 text-justify px-4 block md:hidden">
            {locale === "en" ? props.en_desc : props.desc}
          </p> */}
          <p className="text-sm my-4 text-justify px-4 block md:hidden">
            {locale === "en" ? "Chronic diseases are long-term, progressive conditions that require ongoing monitoring and treatment, such as hypertension, diabetes, and hepatitis B. Without proper management, these conditions can lead to serious complications." : "Bệnh mạn tính là những bệnh lý tiến triển kéo dài, cần được theo dõi và điều trị liên tục như tăng huyết áp, đái tháo đường, viêm gan B... Nếu không được quản lý đúng cách, bệnh có thể gây ra nhiều biến chứng nguy hiểm."}
              </p>
          <section className='block md:hidden'>
            <div className="flex gap-2 px-4 overflow-x-scroll whitespace-nowrap hide-scrollbar">
              {Chronicdiseases_packages.slice(6, 12).map((chronicdiseases_package) => (
                <a
                  key={chronicdiseases_package.id}
                  href={`#${chronicdiseases_package.id}`}
                  onClick={handleClickDefault(chronicdiseases_package.id)}
                  className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === chronicdiseases_package.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                    }`}
                >
                  {locale === "vi" ? chronicdiseases_package.label : chronicdiseases_package.label_en}
                </a>
              ))}
            </div>
          </section>
        </div>
      </section>
      <div style={{
        background: "white",
        zIndex: 100,
        position: "fixed",
        top: 45,
        width: "100%",
        textAlign: "left",
      }}
      >
        {isVisible && (
          <>
            <section className='md:px-16 px-4'>
              <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase py-2 mt-4">
               {locale === "vi" ? "CÁC GÓI QUẢN LÝ\n BỆNH MẠN TÍNH" : "Chronic Disease\n Management Packages"}
              </h2>
              <section className='md:hidden block'>
                <div className="flex gap-2 px-4 overflow-x-scroll whitespace-nowrap hide-scrollbar pb-3">
                  {Chronicdiseases_packages.slice(6, 12).map((chronicdiseases_package) => (
                    <a
                      key={chronicdiseases_package.id}
                      href={`#${chronicdiseases_package.id}`}
                      onClick={handleClickDefault(chronicdiseases_package.id)}
                      className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === chronicdiseases_package.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                        }`}
                    >
                      {locale === "vi" ? chronicdiseases_package.label : chronicdiseases_package.label_en}
                    </a>
                  ))}
                </div>
              </section>
              <section className='md:block hidden'>
                <div className="flex w-full flex-wrap gap-4 pb-3">
                  {Chronicdiseases_packages.slice(0, 6).map((chronicdiseases_package) => (
                    <a
                      key={chronicdiseases_package.id}
                      href={`#${chronicdiseases_package.id}`}
                      onClick={handleClickDefault(chronicdiseases_package.id)}
                      className={`py-1 px-4 text-sm rounded-lg text-center ${activeStates === chronicdiseases_package.id ? 'bg-[#15663414] text-[#156634] border border-[#156634]' : 'bg-[#15663414] text-[#156634]'
                        }`}
                    >
                      {locale === "vi" ? chronicdiseases_package.label : chronicdiseases_package.label_en}
                    </a>
                  ))}
                </div>
              </section>
            </section>
          </>
        )}
      </div>
      <section className='mx-auto max-w-screen-2xl'>
        <div className="md:px-16 px-4">
        <div>
          <p className="md:text-base text-sm mb-2 mt-4 md:mt-12 text-justify">
            {locale === "en" ? "Echo Medi offers 17 comprehensive chronic disease management packages, including hypertension, diabetes, prediabetes, dyslipidemia, obesity, hepatitis B, hepatitis C, fatty liver disease, hypothyroidism, hyperthyroidism, gout, chronic kidney disease, asthma, allergies, ischaemic stroke, hemorrhagic stroke, and chronic fatigue syndrome. Committed to providing holistic, personalized care with continuous follow-up, we empower patients to proactively and effectively manage their health." : "Echo Medi giới thiệu 17 gói quản lý bệnh mạn tính bao gồm: tăng huyết áp, đái tháo đường, tiền đái tháo đường, rối loạn mỡ máu, béo phì, viêm gan B, viêm gan C, gan nhiễm mỡ, suy giáp, cường giáp, Gout, bệnh thận mạn, hen phế quản, dị ứng, nhồi máu não, xuất huyết não, hội chứng mệt mỏi mạn tính.  Với cam kết chăm sóc toàn diện, cá nhân hóa và theo dõi liên tục, chúng tôi sẽ giúp người bệnh kiểm soát sức khỏe một cách chủ động và hiệu quả."}
          </p>
          <p className="mb-3 md:text-base text-sm">
            {locale === "en" ? "By enrolling in an Echo Medi management package, you will benefit from:" : "Khi tham gia gói dịch vụ tại Echo Medi, bạn sẽ nhận được:"}
          </p>
          <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
            {locale === "en" ? "Personalized healthcare plans: Customized to your specific condition, lifestyle, and health goals.  " : "Kế hoạch chăm sóc sức khỏe cá nhân hóa: Được xây dựng riêng phù hợp với tình trạng bệnh, thói quen sinh hoạt và mục tiêu sức khỏe của từng người."}
          </p>
          <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
            {locale === "en" ? "A multidisciplinary team: Including physicians, nurses, nutritionists, and psychologists who provide regular monitoring, timely advice, and flexible treatment adjustments." : "Đội ngũ bác sĩ, điều dưỡng và chuyên gia dinh dưỡng, chuyên viên tâm lý đồng hành sát sao: Theo dõi thường xuyên, tư vấn kịp thời, điều chỉnh phác đồ linh hoạt."}
          </p>
          <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
            {locale === "en" ? "Convenient and timely access to medical services: Easy appointment scheduling and remote health monitoring save you time, effort and cost." : "Tiếp cận dịch vụ y tế nhanh chóng, thuận tiện: Đặt lịch dễ dàng, theo dõi sức khỏe từ xa, tiết kiệm thời gian, công sức và chi phí."}
          </p>
          <p className="md:text-base text-sm ml-4 mb-2 relative pl-6 before:absolute before:left-0 before:top-1/2 before:transform before:-translate-y-1/2 before:w-1 before:h-1 before:bg-black before:rounded-full">
            {locale === "en" ? "Rational use of medications and diagnostic tests: Ensuring appropriate, effective, and safe treatment without unnecessary interventions." : "Không lạm dụng thuốc và xét nghiệm: Chỉ định hợp lý, hiệu quả, hướng đến điều trị an toàn và bền vững."}
          </p>
          <p className="mb-3 md:text-base text-sm">
            {locale === "en" ? "Echo Medi’s chronic disease management packages offer an optimal solution to help you live a healthier, more proactive, and confident life every day." : "Gói quản lý bệnh mạn tính tại Echo Medi là giải pháp tối ưu giúp bạn sống khỏe, chủ động và an tâm mỗi ngày."}
          </p>
        </div>
          <div className="mx-auto text-left noselect pb-8">
            {props.sub_packages?.map((sp: any, id: any) => {
              if (id === 0) {
                return (
                  <>
                    <section id='chronicdiseases_1' className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id='chronicdiseases_7' className='block md:hidden' >
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 1) {
                return (
                  <>
                    <section id='chronicdiseases_2' className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id='chronicdiseases_8' className='block md:hidden'>
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 2) {
                return (
                  <>
                    <section id='chronicdiseases_3' className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id='chronicdiseases_9' className='block md:hidden'>
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 3) {
                return (
                  <>
                    <section id='chronicdiseases_4' className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id='chronicdiseases_10' className='block md:hidden'>
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 4) {
                return (
                  <>
                    <section id='chronicdiseases_5' className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id='chronicdiseases_11' className='block md:hidden'>
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
              if (id === 5) {
                return (
                  <>
                    <section id='chronicdiseases_6' className='hidden md:block'>
                      <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mt-12 mb-4">{locale === "en" ? sp.en_label : sp.label}</h2>
                      <div className="mx-auto grid max-w-md grid-cols-1 gap-4 lg:mx-0 lg:max-w-none lg:grid-cols-4">
                        {sp.services.map((sv: any) =>
                          <>
                            <section key={sv.id}>
                              <CardPackageItem sv={sv} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                            </section>
                          </>
                        )}
                      </div>
                    </section>
                    <section id='chronicdiseases_12' className='block md:hidden'>
                      <div className="mx-auto py-6">
                        <h2 className="text-left font-bold md:text-[28px] text-2xl text-[#156634] my-4 uppercase">{locale === "en" ? sp.en_label : sp.label}</h2>
                        <section key={sp.id}>
                          <CardPackageSlideItem sp={sp} locale={locale} height_cart={"h-[600px]"} addToCart={addToCart} handleShowModal={handleShowModal} />
                        </section>
                      </div>
                    </section>
                  </>
                )
              }
            })}
          </div>
        </div>
      </section>
      {showModal && (
        <ModalBooking
          visible={showModal}
          onClose={() => setShowModal(false)}
          currentBlog={currentBlog}
          locale={locale}
        />
      )}
      <Contact />
    </>
  );
};

function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Blog;
