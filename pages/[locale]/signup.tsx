import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import OtpInput from "react-otp-input";
import { makeStaticProps } from '../../lib/getStatic';
import Modal from "../../components/components/Modal";
import Link from "next/link";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})

export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
  const [email, setEmail] = useState("");
  const [phone_number, setPhoneNumber] = useState("");
  const [fullname, setFullname] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [otp, setOtp] = useState('');
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [countdown, setCountdown] = useState(60);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [showModalLogin, setShowModalLogin] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  useEffect(() => {
    if (countdown === 0) {
      setResendDisabled(false);
      return;
    }

    const timer = setTimeout(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, 1000);

    setResendDisabled(true);

    return () => clearTimeout(timer);
  }, [countdown]);

  const handleResendOTP = async () => {
    setCountdown(60);
    await axios.post('https://api.echomedi.com/api/resend-otp', {
      "phone": phone_number,
      "otp": otp,
    })
      .then(function (response) {
        if (response.data.userExist === true) {
          toast.error(locale == "vi" ? "Số điện thoại đã được đăng ký." : "Phone number registered")
        } else {
          toast.success(locale == "vi" ? 'Bạn đã xác thực thành công, vui lòng đăng nhập!' : "You have successfully authenticated, please log in!");
          location.href = "/login"
        }
      })
      .catch(function (error) {
        toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
      });
  };

  const register = async () => {
    if (phone_number == "" || fullname == "" || email == "") {
      toast.error(locale == "vi" ? "Thông tin không phù hợp. Vui lòng điền đầy đủ thông tin." : "Please fill out required fields.")
    } else
      if (password != confirmPassword) {
        toast.error(locale == "vi" ? "Mật khẩu và xác nhận mật khẩu không trùng nhau" : "Password and confirm password do not match")
      } else {
        await axios.post('https://api.echomedi.com/api/patient/createPatientAndUser', {
          "phone": phone_number,
          "name": fullname,
          "email": email,
          "password": password,
          "isWeb": true,
        })
          .then(function (response) {
            if (response.data.userExist) {
              toast.error(locale == "vi" ? "Số điện thoại đã được đăng ký." : "Phone number registered")
            } else {
              setShowModalLogin(true)
              // toast.success(locale == "vi" ? 'Đăng ký thành công, vui lòng đăng nhập!' : "Phone number registered sucessful");
            }
          })
          .catch(function (error) {
            toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
          });
      }
  }
  const verifyOTP = async () => {
    await axios.post('https://api.echomedi.com/api/user/verifyPhoneOTP', {
      "phone": phone_number,
      "otp": otp,
    })
      .then(async function (response) {
        const ok = response.data.ok;
        if (!ok) {
          toast.error(locale == "vi" ? "Mã OTP không chính xác." : "OTP is not valid.")
        } else {
          toast.success(locale == "vi" ? 'Bạn đã xác thực thành công, vui lòng đăng nhập!' : "You have successfully authenticated, please log in!");
          await register();
          location.href = "/login"
        }
      })
      .catch(function (error) {
        toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
      });
  }
  return (
    <>
      <Head>
        <title>ECHO MEDI - Đăng ký - Signup</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section className="bg-gray-50">
        <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
          <div className="w-full bg-white rounded-lg shadow md:mt-0 sm:max-w-md xl:p-0">
            <div className="p-6 space-y-4 md:space-y-4 sm:p-8">
              <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl text-center">
                {locale === "en" ? "Sign up" : "Đăng ký"}
              </h1>
              <form className="space-y-4 md:space-y-2">
                <div>
                  <label htmlFor="phone" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "Fullname" : "Họ và tên"} <span className="text-red-600">*</span>
                  </label>
                  <input
                    id="exampleFormControlInput1"
                    onChange={(e) => { setFullname(e.target.value) }}
                    type="text" name="fullname" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
                <div>
                  <label htmlFor="phone" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "Phone number" : "Số điện thoại"} <span className="text-red-600">*</span>
                  </label>
                  <input
                    id="exampleFormControlInput1"
                    onChange={(e) => { setPhoneNumber(e.target.value) }}
                    type="phone" name="phone" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
                <div>
                  <label htmlFor="phone" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "Email" : "Email"} <span className="text-red-600">*</span>
                  </label>
                  <input
                    id="exampleFormControlInput1"
                    onChange={(e) => { setEmail(e.target.value) }}
                    type="email" name="email" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
                <div className="relative">
                  <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "Password" : "Mật khẩu"} <span className="text-red-600">*</span>
                  </label>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="exampleFormControlInput1"
                    onChange={(e) => { setPassword(e.target.value) }}
                    name="password" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                  <div
                    className="absolute right-0 top-1/2 mt-2 mr-3 flex items-center cursor-pointer"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <>
                        <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                        </svg>
                      </>
                    ) : (
                      <>
                        <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                        </svg>
                      </>
                    )}
                  </div>
                </div>
                <div className="relative">
                  <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "Confirm password" : "Nhập lại mật khẩu"} <span className="text-red-600">*</span>
                  </label>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="exampleFormControlInput1"
                    onChange={(e) => { setConfirmPassword(e.target.value) }}
                    name="password" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                  <div
                    className="absolute right-0 top-1/2 mt-2 mr-3 flex items-center cursor-pointer"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <>
                        <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                        </svg>
                      </>
                    ) : (
                      <>
                        <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 10.334C10.0417 10.334 10.9271 9.9694 11.6562 9.24023C12.3854 8.51107 12.75 7.62565 12.75 6.58398C12.75 5.54232 12.3854 4.6569 11.6562 3.92773C10.9271 3.19857 10.0417 2.83398 9 2.83398C7.95833 2.83398 7.07292 3.19857 6.34375 3.92773C5.61458 4.6569 5.25 5.54232 5.25 6.58398C5.25 7.62565 5.61458 8.51107 6.34375 9.24023C7.07292 9.9694 7.95833 10.334 9 10.334ZM9 8.83398C8.375 8.83398 7.84375 8.61523 7.40625 8.17773C6.96875 7.74023 6.75 7.20898 6.75 6.58398C6.75 5.95898 6.96875 5.42773 7.40625 4.99023C7.84375 4.55273 8.375 4.33398 9 4.33398C9.625 4.33398 10.1562 4.55273 10.5938 4.99023C11.0312 5.42773 11.25 5.95898 11.25 6.58398C11.25 7.20898 11.0312 7.74023 10.5938 8.17773C10.1562 8.61523 9.625 8.83398 9 8.83398ZM9 12.834C7.13889 12.834 5.44097 12.334 3.90625 11.334C2.37153 10.334 1.15972 9.01454 0.270833 7.37565C0.201389 7.25065 0.149306 7.12218 0.114583 6.99023C0.0798611 6.85829 0.0625 6.72287 0.0625 6.58398C0.0625 6.4451 0.0798611 6.30968 0.114583 6.17773C0.149306 6.04579 0.201389 5.91732 0.270833 5.79232C1.15972 4.15343 2.37153 2.83398 3.90625 1.83398C5.44097 0.833984 7.13889 0.333984 9 0.333984C10.8611 0.333984 12.559 0.833984 14.0938 1.83398C15.6285 2.83398 16.8403 4.15343 17.7292 5.79232C17.7986 5.91732 17.8507 6.04579 17.8854 6.17773C17.9201 6.30968 17.9375 6.4451 17.9375 6.58398C17.9375 6.72287 17.9201 6.85829 17.8854 6.99023C17.8507 7.12218 17.7986 7.25065 17.7292 7.37565C16.8403 9.01454 15.6285 10.334 14.0938 11.334C12.559 12.334 10.8611 12.834 9 12.834Z" fill="#CACACA" />
                        </svg>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-start">
                    <div className="flex items-center h-4">
                      <input checked={isChecked}
                        onChange={(e) => setIsChecked(e.target.checked)}
                        id="remember" aria-describedby="remember" type="checkbox" className="w-4 h-4 border border-gray-300 rounded" />
                    </div>
                    <div className="ml-3 text-xs">
                      {locale === "en" ? "I agree to the" : "Tôi đồng ý với"}
                      <Link href={`/${locale}/policy/dieu-khoan-va-chinh-sach-hoat-dong/`}>
                        <label htmlFor="remember" className="text-[#156634] font-bold hover:underline px-1">{locale === "en" ? "operating policies" : "điều khoản và chính sách hoạt động"}</label>
                      </Link>
                      {locale === "en" ? "of the website" : "của website"}
                    </div>
                  </div>
                </div>
                <button style={{
                  background: "#406d48"
                }}
                  disabled={!isChecked}
                  type="button" onClick={async () => {
                    if (phone_number == "" || fullname == "" || email == "") {
                      toast.error(locale == "vi" ? "Thông tin không phù hợp. Vui lòng điền đầy đủ thông tin." : "Please fill out required fields.")
                    } else if (password != confirmPassword) {
                      toast.error(locale == "vi" ? "Mật khẩu và xác nhận mật khẩu không trùng nhau" : "Password and confirm password do not match")
                    } else {
                      await axios.get('https://api.echomedi.com/api/patient/checkUserExistByPhone/' + phone_number)
                        .then(function (response) {
                          if (response.data.userExist === true) {
                            toast.error(locale == "vi" ? "Số điện thoại đã được đăng ký." : "Phone number registered")
                          } else {
                            axios.get('https://api.echomedi.com/api/user/sendOTP/' + phone_number);
                            setShowModalLogin(true);
                          }
                        });
                    }
                  }} className="w-full text-white focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-lg text-sm px-5 py-2.5 text-center font-bold">
                  {locale === "en" ? "Sign up" : "Đăng ký"}
                </button>
                <p className="text-sm font-light text-gray-500">
                  {locale === "en" ? "Have an account yet? " : "Bạn đã có tài khoản? "}
                  <a style={{
                    color: "#4D6048"
                  }}
                    href="/login"
                    className="font-medium italic	hover:underline">
                    {locale === "en" ? "Log in" : "Đăng nhập"}
                  </a>
                </p>
              </form>
            </div>
          </div>
        </div>
      </section>
      <Modal
        showCloseButton
        visibleModal={showModalLogin}
        wrapperClassName="lg:!w-[500px] !w-[350px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalLogin(false)}
      >
        <section>
          <div>
            <h3 className="text-center text-2xl font-bold max-w-[280px] lg:max-w-xl lg:mt-2">Nhập mã xác thực để tiếp tục</h3>
            <form className="px-4 py-6">
              <div className="flex justify-center gap-2 mb-6">
                <OtpInput
                  value={otp}
                  onChange={setOtp}
                  numInputs={6}
                  renderSeparator={<span className="px-2"></span>}
                  renderInput={(props) => <input {...props} style={{ width: 35, height: 35, textAlign: 'center', border: '1px solid black', borderRadius: 5, fontWeight: 'bold' }} />}
                />
              </div>
              <div className="text-center">
                <p className="py-4">Mã xác thực vừa được gửi đến số {phone_number}</p>
                <span>Nếu chưa nhận được mã xác thực trong vòng
                  một phút, bấm</span>
                <button
                  className="ml-1 text-green-800 cursor-pointer"
                  type="button"
                  onClick={handleResendOTP}
                  disabled={resendDisabled}>
                  {resendDisabled ? `Resend OTP (${countdown}s)` : 'Resend OTP'}
                </button>
              </div>
              <div className="flex items-center justify-center mt-2">
                <button onClick={verifyOTP} className="bg-green-700 hover:bg-green-800 text-white font-bold py-2 w-full rounded focus:outline-none focus:shadow-outline" type="button">
                  {locale == "vi" ? "Xác nhận" : "Verify"}
                </button>
              </div>
            </form>
          </div>
        </section>
      </Modal>
      <Contact />
      <Toaster
        position="bottom-center"
      />
    </>
  );
};

export default Home;
