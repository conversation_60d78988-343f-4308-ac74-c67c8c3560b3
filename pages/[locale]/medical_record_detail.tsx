import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState  } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import dayjs from "dayjs";

require("dayjs/locale/vi");
dayjs.locale("vi");

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }


const Order = () => {
    const router = useRouter()
    const { code } = router.query;
    const [data, setData] = useState(null as any);
    const locale = router.query.locale as string || 'vi';
    const token = localStorage.getItem('token');

    const renderTagifyComponent = (fieldName : string) => {
        if (!data) return "";
        let result = "";
        let s = data[fieldName];
        if (!s) return "";
        let els = JSON.parse(s);
        els.forEach( (e: any) => {
            result = result + e["value"];
        })

        return result;
    }

    useEffect(() => {
      if (token && code) {
        axios.get('https://api.echomedi.com' + '/api/medical-record/findMedicalByUID/' + code, 
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
          .then(function (response) {
            setData(response.data)
            toast.success('Thành công');
          })
          .catch(function (error) {
            if (error.response.status == 401) {
                toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
                localStorage.removeItem("token");
                window.location.href = '/login';
              }
          });
      }
    }, [token, code]);

    return <>
        <Head>
            <title>ECHO MEDI</title>
            <meta
                name="ECHO MEDI"
                content="ECHO MEDI"
            />
            <meta name="keywords" content="ECHO MEDI"></meta>
            <link rel="icon" href="/favicon1.png" />
        </Head>
        <div className="container mx-auto mt-10 bg-green-100">
            <div className="">
                <div className="w-4/4 bg-green-100 px-10 py-10">
                    <div className="flex justify-between border-b pb-8">
                        <p className="font-semibold text-xl">{locale === "en" ? "Medical records" : "Bệnh án"}</p>
                    </div>
                    <p>{locale == "en" ? "Pulse" : "Mạch"}: {data?.circuit}</p>
                    <p>{locale == "en" ? "Temperature" : "Nhiệt độ"}: {data?.temperature}</p>
                    <p>{locale == "en" ? "Blood pressure" : "Huyết áp"}: {data?.blood_pressure} / {data?.blood_pressure2}</p>
                    <p>{locale == "en" ? "Respiratory rate" : "Nhịp tim"}: {data?.respiratory_rate}</p>
                    <p>{locale == "en" ? "Height" : "Chiều cao"}: {data?.height}</p>
                    <p>{locale == "en" ? "Weight" : "Cân nặng"}: {data?.weight}</p>
                    <p>BMI: {data?.bmi}</p>
                    <p>SPO2: {data?.spo2}</p>
                    <p>{locale == "en" ? "Reasons to get hospitalized" : "Nguyên nhân nhập viện"}: {renderTagifyComponent("reasons_to_get_hospitalized")}</p>
                    <p>{locale == "en" ? "Inquiry" : "Bệnh sử"}: {renderTagifyComponent("inquiry")}</p>
                    <p>{locale == "en" ? "Premise" : "Tiền căn"}: {renderTagifyComponent("premise")}</p>
                    <p>{locale == "en" ? "Main diagnose" : "Bệnh chính"}: {renderTagifyComponent("main_diagnose")}</p>
                    <p>{locale == "en" ? "Other diagnose" : "Bệnh phụ"}: {renderTagifyComponent("other_diagnose")}</p>
                    <p>{locale == "en" ? "Treatment regimen" : "Hướng điều trị"}: {renderTagifyComponent("treatment_regimen")}</p>
                </div>
            </div>
        </div>
        <Contact />
    </>
}

export default Order
