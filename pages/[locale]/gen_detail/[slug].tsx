import type {
    InferGetStaticPropsType,
} from 'next';
import Head from "next/head";
import Contact from "../../../components/Contact/Contact";
import { useRouter } from 'next/router'
import React from "react";
import parse from 'html-react-parser';
import { Tab, Tabs, Tab<PERSON><PERSON>, Tab<PERSON>anel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

import { getStaticPathsServicesGenDetail, getStaticPropsService } from '../../../lib/getStatic';
import { addToCart } from '../../../lib/ui';
export { getStaticPathsServicesGenDetail as getStaticPaths, getStaticPropsService as getStaticProps };

const Blog = (props: InferGetStaticPropsType<typeof getStaticPropsService>) => {
    const router = useRouter()
    const locale = router.query.locale as string || 'vi';
        console.log("props",props)
    return (
        <>
            <Head>
                <title>{locale === "en" ? props.en_label : props.label} | ECHO MEDI</title>
                <meta
                    name="description"
                    content="ECHO MEDI"
                />
                <meta name="keywords" content="ECHO MEDI"></meta>
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" />
                <link href="https://fonts.googleapis.com/css2?family=Quicksand&display=swap" rel="stylesheet" />
                <link rel="icon" href="/favicon1.png" />

            </Head>
            <section className="pt-10 max-sm:pt-0">
                <div className="max-w-6xl px-4 mx-auto">
                    <div className="flex flex-wrap mb-20 max-sm:mb-5 -mx-4">
                        <div className="w-full px-4 mb-8 md:w-1/2 md:mb-0">
                            <div className="">
                                <img className="object-contain w-full lg:h-full relative mt-1 max-sm:mt-0 mb-6 lg:mb-10 rounded-2xl border-white border-8 shadow-md" src={"https://api.echomedi.com" + props.genetica_image_url} alt="" />
                                <div className="border mx-6 lg:grid grid-cols-4 bg-gray-50 border-dashed rounded-lg hidden">
                                    <div className='p-3 text-gray-800 col-span-3'>
                                        <p className='text-sm font-bold'>{locale === "en" ? "Service includes" : "Dịch vụ bao gồm"}:</p>
                                        <ul className='list-disc text-xs pl-4 pt-2'>
                                            <li>
                                                {locale === "en" ? "DNA collection kit (Sample collection tube, Registration form, Guideline)" : "Bộ thu mẫu nước bọt (Kit lấy mẫu, Phiếu xác nhận, Phiếu hướng dẫn)"}
                                            </li>
                                            <li>
                                                {locale === "en" ? "Genetic reports on Genetica app" : "Báo cáo giải mã gen qua Ứng dụng Genetica"}
                                            </li>
                                        </ul>
                                    </div>
                                    {/* <div className='col-span-1'>
                                        <img src='https://genetica.asia/_next/static/media/sample-collector.2272a1e2.png' className='px-7 pt-5 pb-5' />
                                    </div> */}
                                </div>
                                <div className="border mx-6 bg-gray-50 border-dashed rounded-lg lg:hidden">
                                    <div className='p-3 text-gray-800'>
                                        <p className='text-sm font-bold'>{locale === "en" ? "Service includes" : "Dịch vụ bao gồm"}:</p>
                                        <ul className='list-disc text-xs pl-4 pt-2'>
                                            <li>
                                                {locale === "en" ? "DNA collection kit (Sample collection tube, Registration form, Guideline)" : "Bộ thu mẫu nước bọt (Kit lấy mẫu, Phiếu xác nhận, Phiếu hướng dẫn)"}
                                            </li>
                                            <li>
                                                {locale === "en" ? "Genetic reports on Genetica app" : "Báo cáo giải mã gen qua Ứng dụng Genetica"}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="w-full px-4 md:w-1/2">
                            <div className="lg:pl-20">
                                <div className="mb-6 ">
                                    <h2 className="max-w-xl mb-2 font-semibold md:text-2xl">
                                        {locale === "en" ? props.en_label : props.label}
                                    </h2>
                                    <div className='mt-5'>
                                        <p className='text-[15px] font-bold pb-2'>{locale === "en" ? "Benefits you'll get at ECHO MEDI" : "Lợi ích dịch vụ tại ECHO MEDI"}</p>
                                        <p className='text-sm text-light justify-between whitespace-pre-line'>{locale === "en" ? props.en_detail : props.detail}</p>
                                        {/* <p className='text-sm text-light justify-between'>{locale === "en" ? props.en_specification[4] : props.specification[4]}</p>
                                        <p className='text-sm text-light justify-between'>{locale === "en" ? props.en_specification[9] : props.specification[9]}</p> */}
                                    </div>
                                    <div className='mt-5'>
                                        <p className='text-[15px] font-bold pb-2'>{locale === "en" ? "Benefits you'll get at Genetica" : "Lợi ích dịch vụ tại Genetica"}</p>
                                        <span className='text-sm text-light justify-between'>{parse(locale == "en" ? (props.en_benefit ?? "") : (props.benefit ?? ""))}</span>
                                    </div>
                                </div>
                                <div className="mb-6">
                                    <h2 className="mb-2 text-lg font-bold text-black">{locale === "en" ? "Service features" : "Đặc điểm dịch vụ"}</h2>
                                    <div className="bg-gray-50 rounded-xl">
                                        <div className="p-3 lg:p-4 ">
                                            <div className="p-2 rounded-xl lg:p-6 bg-white">
                                                <div className="flex flex-wrap justify-between">
                                                    <div className="w-full mb-4 md:w-2/5">
                                                        <div className="flex">
                                                            <span className="mr-1 text-black">
                                                                <svg fill="#166534" width="30px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M6,22a1,1,0,0,0,2,0c0-.023.124-2.226,4-3.91,3.812,1.655,3.993,3.8,4,3.928A1,1,0,0,0,18,22c0-.13-.056-2.8-3.657-4.957C17.694,15.34,18,13.841,18,12s-.306-3.34-3.657-5.043C17.944,4.8,18,2.13,18,2a1,1,0,0,0-2,0c0,.023-.124,2.226-4,3.91C8.156,4.241,8,2.074,8,2A1,1,0,0,0,6,2c0,.13.056,2.8,3.657,4.957C6.306,8.66,6,10.159,6,12s.306,3.34,3.657,5.043C6.056,19.2,6,21.87,6,22ZM8,12c0-1.308.012-2.266,4-3.917,3.988,1.651,4,2.609,4,3.917s-.012,2.266-4,3.917C8.012,14.266,8,13.308,8,12Z" />
                                                                </svg>
                                                            </span>
                                                            <div>
                                                                <p className="mb-2 text-xs font-medium text-black">
                                                                    {locale === "en" ? props.en_properties[0] : props.properties[0]}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="w-full mb-4 md:w-2/5">
                                                        <div className="flex">
                                                            <span className="mr-1 text-black">
                                                                <svg fill="#166534" width="30px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>lightbulb</title><path d="M12,2A7,7,0,0,0,8,14.74V17a1,1,0,0,0,1,1h6a1,1,0,0,0,1-1V14.74A7,7,0,0,0,12,2ZM9,21a1,1,0,0,0,1,1h4a1,1,0,0,0,1-1V20H9Z" />
                                                                </svg>
                                                            </span>
                                                            <div>
                                                                <p className="mb-2 text-xs font-medium text-black">
                                                                    {locale === "en" ? props.en_properties[1] : props.properties[1]}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="w-full mb-4 lg:mb-0 md:w-2/5">
                                                        <div className="flex ">
                                                            <span className="mr-1 text-black">
                                                                <svg fill="#166534" width="30px" height="20px" viewBox="-64 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="M120 72c0-39.765 32.235-72 72-72s72 32.235 72 72c0 39.764-32.235 72-72 72s-72-32.236-72-72zm254.627 1.373c-12.496-12.497-32.758-12.497-45.254 0L242.745 160H141.254L54.627 73.373c-12.496-12.497-32.758-12.497-45.254 0-12.497 12.497-12.497 32.758 0 45.255L104 213.254V480c0 17.673 14.327 32 32 32h16c17.673 0 32-14.327 32-32V368h16v112c0 17.673 14.327 32 32 32h16c17.673 0 32-14.327 32-32V213.254l94.627-94.627c12.497-12.497 12.497-32.757 0-45.254z" />
                                                                </svg>
                                                            </span>
                                                            <div>
                                                                <p className="mb-2 text-xs font-medium text-black">
                                                                    {locale === "en" ? props.en_properties[2] : props.properties[2]}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="w-full mb-4 lg:mb-0 md:w-2/5">
                                                        <div className="flex ">
                                                            <span className="mr-1 text-black">
                                                                <svg width="30px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <g id="Environment / Water_Drop">
                                                                        <path id="Vector" d="M16.0001 13.3848C16.0001 14.6088 15.526 15.7828 14.6821 16.6483C14.203 17.1397 13.6269 17.5091 13 17.7364M19 13.6923C19 7.11538 12 2 12 2C12 2 5 7.11538 5 13.6923C5 15.6304 5.7375 17.4893 7.05025 18.8598C8.36301 20.2302 10.1436 20.9994 12.0001 20.9994C13.8566 20.9994 15.637 20.2298 16.9497 18.8594C18.2625 17.4889 19 15.6304 19 13.6923Z" stroke="#166534" strokeWidth="2" strokeLinecap="round" stroke-linejoin="round" />
                                                                    </g>
                                                                </svg>
                                                            </span>
                                                            <div>
                                                                <p className="mb-2 text-xs font-medium text-black">
                                                                    {locale === "en" ? props.en_properties[3] : props.properties[3]}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a href={props.genetica_pdf} target='blank' className='flex font-bold text-[#166534] hover:underline'>
                                    <svg className="mt-[1px] mr-[7px]" fill="#166534" height="20px" width="20px" version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 512 512" xmlSpace="preserve">
                                        <g>
                                            <path className="st0" d="M347.746,346.204c-8.398-0.505-28.589,0.691-48.81,4.533c-11.697-11.839-21.826-26.753-29.34-39.053
                            c24.078-69.232,8.829-88.91-11.697-88.91c-16.119,0-24.167,17.011-22.376,35.805c0.906,9.461,8.918,29.34,18.78,48.223
                            c-6.05,15.912-16.847,42.806-27.564,62.269c-12.545,3.812-23.305,8.048-31.027,11.622c-38.465,17.888-41.556,41.773-33.552,51.894
                            c15.197,19.226,47.576,2.638,80.066-55.468c22.243-6.325,51.508-14.752,54.146-14.752c0.304,0,0.721,0.097,1.204,0.253
                            c16.215,14.298,35.366,30.67,51.128,32.825c22.808,3.136,35.791-13.406,34.891-23.692
                            C382.703,361.461,376.691,347.942,347.746,346.204z M203.761,408.88c-9.401,11.178-24.606,21.9-29.972,18.334
                            c-5.373-3.574-6.265-13.86,5.819-25.497c12.076-11.623,32.29-17.657,35.329-18.787c3.59-1.337,4.482,0,4.482,1.791
                            C219.419,386.512,213.154,397.689,203.761,408.88z M244.923,258.571c-0.899-11.192,1.33-21.922,10.731-23.26
                            c9.386-1.352,13.868,9.386,10.292,26.828c-3.582,17.464-5.38,29.08-7.164,30.44c-1.79,1.338-3.567-3.144-3.567-3.144
                            C251.627,282.27,245.815,269.748,244.923,258.571z M248.505,363.697c4.912-8.064,17.442-40.702,17.442-40.702
                            c2.683,4.926,23.699,29.956,23.699,29.956S257.438,360.123,248.505,363.697z M345.999,377.995
                            c-13.414-1.768-36.221-17.895-36.221-17.895c-3.128-1.337,24.992-5.157,35.79-4.466c13.875,0.9,18.794,6.718,18.794,12.53
                            C364.362,373.982,359.443,379.787,345.999,377.995z"/>
                                            <path className="st0" d="M461.336,107.66l-98.34-98.348L353.683,0H340.5H139.946C92.593,0,54.069,38.532,54.069,85.901v6.57H41.353
                            v102.733h12.716v230.904c0,47.361,38.525,85.893,85.878,85.893h244.808c47.368,0,85.893-38.532,85.893-85.893V130.155v-13.176
                            L461.336,107.66z M384.754,480.193H139.946c-29.875,0-54.086-24.212-54.086-54.086V195.203h157.31V92.47H85.86v-6.57
                            c0-29.882,24.211-54.102,54.086-54.102H332.89v60.894c0,24.888,20.191,45.065,45.079,45.065h60.886v288.349
                            C438.855,455.982,414.636,480.193,384.754,480.193z M88.09,166.086v-47.554c0-0.839,0.683-1.524,1.524-1.524h15.108
                            c2.49,0,4.786,0.409,6.837,1.212c2.029,0.795,3.812,1.91,5.299,3.322c1.501,1.419,2.653,3.144,3.433,5.121
                            c0.78,1.939,1.182,4.058,1.182,6.294c0,2.282-0.402,4.414-1.19,6.332c-0.78,1.918-1.932,3.619-3.418,5.054
                            c-1.479,1.427-3.27,2.549-5.321,3.329c-2.036,0.78-4.332,1.174-6.822,1.174h-6.376v17.241c0,0.84-0.683,1.523-1.523,1.523h-7.208
                            C88.773,167.61,88.09,166.926,88.09,166.086z M134.685,166.086v-47.554c0-0.839,0.684-1.524,1.524-1.524h16.698
                            c3.173,0,5.968,0.528,8.324,1.568c2.386,1.062,4.518,2.75,6.347,5.009c0.944,1.189,1.694,2.504,2.236,3.916
                            c0.528,1.375,0.929,2.862,1.189,4.407c0.253,1.531,0.401,3.181,0.453,4.957c0.045,1.694,0.067,3.515,0.067,5.447
                            c0,1.924-0.022,3.746-0.067,5.44c-0.052,1.769-0.2,3.426-0.453,4.964c-0.26,1.546-0.661,3.025-1.189,4.399
                            c-0.55,1.427-1.3,2.743-2.23,3.909c-1.842,2.282-3.976,3.969-6.354,5.016c-2.334,1.04-5.135,1.568-8.324,1.568h-16.698
                            C135.368,167.61,134.685,166.926,134.685,166.086z M214.269,137.981c0.84,0,1.523,0.684,1.523,1.524v6.48
                            c0,0.84-0.683,1.524-1.523,1.524h-18.244v18.579c0,0.84-0.684,1.523-1.524,1.523h-7.209c-0.84,0-1.523-0.683-1.523-1.523v-47.554
                            c0-0.839,0.683-1.524,1.523-1.524h27.653c0.839,0,1.524,0.684,1.524,1.524v6.48c0,0.84-0.684,1.524-1.524,1.524h-18.92v11.444
                            H214.269z"/>
                                            <path className="st0" d="M109.418,137.706c1.212-1.092,1.798-2.645,1.798-4.749c0-2.096-0.587-3.649-1.798-4.741
                            c-1.263-1.13-2.928-1.68-5.098-1.68h-5.975v12.848h5.975C106.489,139.385,108.155,138.836,109.418,137.706z"/>
                                            <path className="st0" d="M156.139,157.481c1.13-0.424,2.103-1.107,2.973-2.088c0.944-1.055,1.538-2.571,1.769-4.511
                            c0.26-2.208,0.386-5.091,0.386-8.569c0-3.485-0.126-6.369-0.386-8.569c-0.231-1.946-0.825-3.462-1.762-4.51
                            c-0.869-0.982-1.873-1.679-2.972-2.089c-1.182-0.453-2.534-0.676-4.042-0.676h-7.164v31.68h7.164
                            C153.605,158.15,154.965,157.927,156.139,157.481z"/>
                                        </g>
                                    </svg>
                                    {locale === "en" ? "View sample report (PDF)" : "Xem báo cáo mẫu (PDF)"}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div className="bg-white hidden lg:block">
                        <h2 className="px-[165px] py-10 text-center font-bold text-black hover:text-gray-800">
                            {locale === "en" ? "Details" : "Thông tin chi tiết"}
                        </h2>
                        <h1 className='max-w-5xl px-4 mx-auto text-xl font-bold'>{locale === "en" ? "ECHO MEDI service details" : "Thông tin chi tiết về dịch vụ của ECHO MEDI"}</h1>
                        <ol className="relative text-black justify-center text-md max-w-4xl px-4 mx-auto mt-8">
                                {/* <h3 className="leading-tight font-bold text-[20px] whitespace-pre-line">{locale === "en" ? props.desc : props.desc}</h3> */}
                                    <p className='pt-2 whitespace-pre-line'>{locale === "en" ? props.en_detail : props.detail}</p>
                                    {/* <li className='pt-2'>{locale === "en" ? props.en_specification[5] : props.specification[5]}</li>
                                    <li className='pt-2'>{locale === "en" ? props.en_specification[6] : props.specification[6]}</li>
                                    <li className='pt-2'>{locale === "en" ? props.en_specification[7] : props.specification[7]}</li>
                                    <li className='pt-2'>{locale === "en" ? props.en_specification[8] : props.specification[8]}</li> */}
                            {/* <li>
                                <h3 className="leading-tight font-bold text-[20px] pt-8">{locale === "en" ? props.en_specification[9] : props.specification[9]}</h3>
                                <ul>
                                    <li className='pt-2'>{locale === "en" ? props.en_specification[10] : props.specification[10]}</li>
                                    <li className='pt-2'>{locale === "en" ? props.en_specification[11] : props.specification[11]}</li>
                                    <li className='pt-2'>{locale === "en" ? props.en_specification[12] : props.specification[12]}</li>
                                </ul>
                            </li> */}
                        </ol>
                        <h1 className='max-w-5xl px-4 mx-auto text-xl font-bold mt-10'>{locale === "en" ? "Genetica service details" : "Thông tin chi tiết về dịch vụ của Genetica"}</h1>
                        <div className='flex-wrap flex mt-10'>
                            <div className="max-w-4xl px-4 mx-auto">
                                <div className="flex flex-wrap mb-10">
                                    <div className="w-full md:w-2/2">
                                        <ol className="relative text-black justify-center text-md">
                                            <li className="mb-10">
                                                <h3 className="leading-tight font-bold text-[20px]">{locale === "en" ? props.en_properties[4] : props.properties[4]}</h3>
                                                <p className='pt-2'>{locale === "en" ? props.en_properties[5] : props.properties[5]}</p>
                                            </li>
                                            <li className="mb-10">
                                                <h3 className="leading-tight font-bold text-[20px]">{locale === "en" ? props.en_properties[6] : props.properties[6]}</h3>
                                                <p className='pt-2'>{locale === "en" ? props.en_properties[7] : props.properties[7]}</p>
                                            </li>
                                            <li className="mb-10">
                                                <h3 className="leading-tight font-bold text-[20px]">{locale === "en" ? props.en_properties[8] : props.properties[8]}</h3>
                                                <p className='pt-2'>{locale === "en" ? props.en_properties[9] : props.properties[9]}</p>
                                            </li>
                                            <li className="mb-10">
                                                <h3 className="leading-tight font-bold text-[20px]">{locale === "en" ? props.en_properties[10] : props.properties[10]}</h3>
                                                <p className='pt-2'>{locale === "en" ? props.en_properties[11] : props.properties[11]}</p>
                                            </li>
                                            <li className="mb-10">
                                                <h3 className="leading-tight font-bold text-[20px]">{locale === "en" ? props.en_properties[12] : props.properties[12]}</h3>
                                                <p className='pt-2'>{locale === "en" ? props.en_properties[13] : props.properties[13]}</p>
                                            </li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
            </div>
            <div className='lg:hidden block'>
                <div className='bg-gray-100 h-2'></div>
                <div className='bg-white  mx-4 mt-4 text-[14px]'>
                    <p className='max-w-5xl px-4 mx-auto font-bold text-lg'>{locale === "en" ? "ECHO MEDI service details" : "Thông tin chi tiết về dịch vụ của ECHO MEDI"}</p>
                    <ol className="relative text-black justify-center text-md max-w-4xl px-4 mx-auto mt-8">
                        <li>
                            <p className='text-sm text-light justify-between whitespace-pre-line'>{locale === "en" ? props.desc : props.desc}</p>
                            {/* <h3 className="leading-tight font-bold">{locale === "en" ? props.en_specification[4] : props.specification[4]}</h3>
                            <ul>
                                <li className='pt-2'>{locale === "en" ? props.en_specification[5] : props.specification[5]}</li>
                                <li className='pt-1'>{locale === "en" ? props.en_specification[6] : props.specification[6]}</li>
                                <li className='pt-1'>{locale === "en" ? props.en_specification[7] : props.specification[7]}</li>
                                <li className='pt-1'>{locale === "en" ? props.en_specification[8] : props.specification[8]}</li>
                            </ul> */}
                        </li>
                        <li className='pb-5'>
                            {/* <h3 className="leading-tight font-bold pt-8">{locale === "en" ? props.en_specification[9] : props.specification[9]}</h3>
                            <ul>
                                <li className='pt-2'>{locale === "en" ? props.en_specification[10] : props.specification[10]}</li>
                                <li className='pt-1'>{locale === "en" ? props.en_specification[11] : props.specification[11]}</li>
                                <li className='pt-1'>{locale === "en" ? props.en_specification[12] : props.specification[12]}</li>
                            </ul> */}
                        </li>
                    </ol>
                </div>
                <div className='bg-gray-100 h-2'></div>
                <div className='bg-white mx-4 mt-4 text-[14px]'>
                    <p className='max-w-5xl px-4 mx-auto text-lg font-bold'>{locale === "en" ? "Genetica service details " : "Thông tin chi tiết về dịch vụ của Genetica"}</p>
                    <div className='flex-wrap flex mt-10'>
                        <div className="max-w-4xl px-4 mx-auto">
                            <div className="flex flex-wrap mb-10">
                                <div className="w-full md:w-2/2">
                                    <ol className="relative text-black justify-center text-sm">
                                        <li className="mb-10">
                                            <h3 className="leading-tight font-bold">{locale === "en" ? props.en_properties[4] : props.properties[4]}</h3>
                                            <p className='pt-2'>{locale === "en" ? props.en_properties[5] : props.properties[5]}</p>
                                        </li>
                                        <li className="mb-10">
                                            <h3 className="leading-tight font-bold">{locale === "en" ? props.en_properties[6] : props.properties[6]}</h3>
                                            <p className='pt-2'>{locale === "en" ? props.en_properties[7] : props.properties[7]}</p>
                                        </li>
                                        <li className="mb-10">
                                            <h3 className="leading-tight font-bold">{locale === "en" ? props.en_properties[8] : props.properties[8]}</h3>
                                            <p className='pt-2'>{locale === "en" ? props.en_properties[9] : props.properties[9]}</p>
                                        </li>
                                        <li className="mb-10">
                                            <h3 className="leading-tight font-bold">{locale === "en" ? props.en_properties[10] : props.properties[10]}</h3>
                                            <p className='pt-2'>{locale === "en" ? props.en_properties[11] : props.properties[11]}</p>
                                        </li>
                                        <li>
                                            <h3 className="leading-tight font-bold">{locale === "en" ? props.en_properties[12] : props.properties[12]}</h3>
                                            <p className='pt-2'>{locale === "en" ? props.en_properties[13] : props.properties[13]}</p>
                                        </li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='bg-gray-100 h-2'></div>
            </div>
            <Contact />
        </>
    );
};

function numberWithCommas(x: number) {
    return x?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export default Blog;
