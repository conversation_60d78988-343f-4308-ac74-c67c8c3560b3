import axios from 'axios';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import Contact from '../../components/Contact/Contact';
import Head from "next/head";
import { makeStaticProps } from '../../lib/getStatic';
import { formatDate } from "../../utils/dateTime"
import dayjs from "dayjs";
import LinkComponent from '../../components/Link';
import Modal from "../../components/components/Modal";

require("dayjs/locale/vi");
dayjs.locale("vi");

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
    fallback: false,
    paths: [{
        params: {
            locale: 'en',
            slug: "test",
            label: "test2",
        }
    },
    {
        params: {
            locale: 'vi',
            slug: "test",
            label: "test2",
        }
    }],
})
export { getStaticPaths, getStaticProps }

const Order = () => {
    const router = useRouter()
    const { code } = router.query;
    const [data, setData] = useState([]);
    const locale = router.query.locale as string || 'vi';
    const [cartLines, setCartLines] = useState([]);
    const token = localStorage.getItem('token');
    const [results, setResults] = useState([]);
    const [showModalLogin, setShowModalLogin] = useState(false);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    useEffect(() => {
        if (token) {
            axios.post('https://api.echomedi.com' + '/api/medical-record/getRelatedMedicalRecords',
                {},
                {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(function (response) {
                    let results: any = [];
                    response.data.medicalRecords.forEach((m: any) => {
                        if (m["testResults"] != null && (typeof m["testResults"]) == "object") {
                            Object.keys(m["testResults"]).forEach(key => {
                                m["testResults"][key][0].label = key;
                                results.push(m["testResults"][key][0])
                            });
                        }
                    })
                    setResults(results);
                    toast.success('Thành công');
                })
                .catch(function (error) {
                    if (error.response.status == 401) {
                        toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
                        localStorage.removeItem("token");
                        window.location.href = '/login';
                    }
                });
        } else {
            toast.success('Vui lòng đăng nhập.');
            setShowModalLogin(true)
        }
    }, [token]);
    const login = () => {
        if (email == "" || password == "") {
            toast.error("Thông tin không phù hợp")
        } else {
            const toastId = toast.loading('Loading...');
            axios
                .post('https://api.echomedi.com/api/user/auth', {
                    identifier: email,
                    password: password,
                })
                .then(response => {
                    toast.success(locale == "vi" ? 'Đăng nhập thành công' : "Login successful");
                    setShowModalLogin(false);
                    localStorage.setItem('token', response.data.jwt);
                    window.location.reload();
                })
                .catch(error => {
                    toast.error(locale == "vi" ? "Đăng nhập không thành công. Vui lòng kiểm tra lại tên đăng nhập, mật khẩu" : "Login fail. Invalid credentials")
                })
                .finally(() => {
                    toast.dismiss(toastId);
                    // location.href = "/";
                });;
        }
    }

    return <>
        <Head>
            <title>ECHO MEDI</title>
            <meta
                name="ECHO MEDI"
                content="ECHO MEDI"
            />
            <meta name="keywords" content="ECHO MEDI"></meta>
            <link rel="icon" href="/favicon1.png" />
        </Head>
        <div className="container mx-auto mt-10 bg-gray-100">
            <div className="lg:max-w-5xl mx-auto max-w-full lg:p-10">
                <div className="bg-white p-4 shadow-md lg:rounded-md h-[70vh]">
                    <div className="flex justify-between border-b pb-8">
                        <p className="font-semibold text-xl">{locale === "en" ? "Test results" : "Kết quả xét nghiệm"}</p>
                    </div>
                    {results?.map((line: any) =>
                        <div className="flex items-center hover:bg-gray-100 -mx-8 px-6 py-5">
                            <div className="flex w-1/1">
                                <div className="flex flex-col justify-between flex-grow">
                                    <a target='_blank' href={"https://api.echomedi.com" + line.url}>
                                        <svg className='inline mr-2' xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="#000000" height="20px" width="20px" version="1.1" id="Capa_1" viewBox="0 0 489.9 489.9" xmlSpace="preserve">
                                            <g>
                                                <g>
                                                    <path d="M328.2,0H49.4v489.9h391.1V112.3L328.2,0z M122.7,120.5H154V89.6c0-13.9,11.2-25.1,25.1-25.1l0,0    c13.9,0,25.1,11.2,25.1,25.1v30.9h31.3c13.9,0,25.1,11.2,25.1,25.1l0,0c0,13.9-11.2,25.1-25.1,25.1h-31.3v31.2    c0,13.9-11.2,25.1-25.1,25.1l0,0c-13.9,0-25.1-11.2-25.1-25.1v-31.3h-31.3c-13.9,0-25.1-11.2-25.1-25.1l0,0    C97.6,131.7,108.9,120.5,122.7,120.5z M381.9,418.3H108.1c-5.4,0-10.1-4.3-10.1-10.1c0-5.4,4.3-10.1,10.1-10.1h273.3    c5.4,0,10.1,4.3,10.1,10.1C391.6,414,387.3,418.3,381.9,418.3z M381.9,332.8H108.1c-5.4,0-10.1-4.3-10.1-10.1    c0-5.4,4.3-10.1,10.1-10.1h273.3c5.4,0,10.1,4.3,10.1,10.1C391.6,328.1,387.3,332.8,381.9,332.8z M308,132.1V19.8l112.8,112.4H308    V132.1z" />
                                                </g>
                                            </g>
                                        </svg>
                                        <span className="font-bold text-sm">{line.label} - {formatDate(line.createdAt)}</span>
                                    </a>
                                </div>
                            </div>
                        </div>)}
                    {results.length == 0 && <p>{locale == "vi" ? "Không dữ liệu" : "No data"}</p>}
                </div>
            </div>
        </div>
        <Modal
            showCloseButton
            visibleModal={showModalLogin}
            wrapperClassName="w-[380px] md:w-[500px]"
            contentClassName="!min-h-[0]" onClose={() => setShowModalLogin(false)}
        >
            <div className="w-full rounded-lg md:mt-0 sm:max-w-md xl:p-0">
                <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
                    <h1 className="text-sm font-bold leading-tight tracking-tight text-[#14532D] md:text-2xl">
                        {locale === "en" ? "Sign in" : "Đăng nhập"}
                    </h1>
                    <div>
                        <label htmlFor="email" className="block  mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Email or phone number" : "Email hoặc số điện thoại"}</label>
                        <input
                            id="exampleFormControlInput1"
                            onChange={(e) => { setEmail(e.target.value) }}
                            type="text" name="email" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                    </div>
                    <div>
                        <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Password" : "Mật khẩu"}</label>
                        <input
                            id="exampleFormControlInput1"
                            onChange={(e) => { setPassword(e.target.value) }}
                            type="password" name="password" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                    </div>
                    <div className="flex items-center justify-between">
                        <div className="flex items-start">
                            <div className="flex items-center h-4">
                                <input id="remember" aria-describedby="remember" type="checkbox" className="w-3 h-3 border border-gray-300 rounded" />
                            </div>
                            <div className="ml-3 text-xs">
                                <label htmlFor="remember" className="text-gray-500 font-bold">{locale === "en" ? "Remember me" : "Ghi nhớ đăng nhập"}</label>
                            </div>
                        </div>
                    </div>
                    <button type="button" onClick={login} className="w-full text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">{locale === "en" ? "Sign in" : "Đăng nhập"}</button>
                    <div className="flex items-center justify-between">
                        <p className="text-sm font-light text-gray-500">
                            {locale === "en" ? "Don’t have an account yet? " : "Bạn chưa có tài khoản? "}<a href={"/signup"}><button className="font-medium text-green-800 hover:underline">{locale === "en" ? "Sign up" : "Đăng ký"}</button></a>
                        </p>
                        <LinkComponent href={"/forgotpassword"} locale={locale} skipLocaleHandling={false}>
                            <p onClick={() => setShowModalLogin(false)}
                                className="font-medium text-green-800 hover:underline">{locale === "en" ? "Forgot password" : "Quên mật khẩu"}
                            </p>
                        </LinkComponent>
                    </div>
                </div>
            </div>
        </Modal>
        <Contact />
    </>
}

export default Order
