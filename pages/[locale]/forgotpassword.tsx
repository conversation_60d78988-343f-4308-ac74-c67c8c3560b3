import type { NextPage } from "next";
import Head from "next/head";
import { useRouter } from 'next/router';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import Contact from "../../components/Contact/Contact";
import ModalLogin from "../../components/components/ModalLogin";
import OtpInput from "react-otp-input";
import LinkComponent from "../../components/Link";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})

export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
  const [password, setPassword] = useState("");
  const [newpassword, setNewPassword] = useState("");
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showModalForgotPassword, setShowModalFotgotPassword] = useState(false);
  const [showModalPhone, setShowModalPhone] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showModalSuccess, setShowModalSuccess] = useState(false);
  const [phone_number, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState('');
  const [countdown, setCountdown] = useState(90);
  const [resendDisabled, setResendDisabled] = useState(false);
  useEffect(() => {
    if (countdown === 0) {
      setResendDisabled(false);
      return;
    }

    const timer = setTimeout(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, 1000);

    setResendDisabled(true);

    return () => clearTimeout(timer);
  }, [countdown]);

  const handleResendOTP = async () => {
    setCountdown(90);
    await axios.post('https://api.echomedi.com/api/resend-otp', {
      "phone": phone_number,
      "otp": otp,
    })
      .then(function (response) {
        if (response.data.userExist === true) {
          toast.error(locale == "vi" ? "Số điện thoại đã được đăng ký." : "Phone number registered")
        } else {
          toast.success(locale == "vi" ? 'Bạn đã xác thực thành công, vui lòng đăng nhập!' : "You have successfully authenticated, please log in!");
          location.href = "/login"
        }
      })
      .catch(function (error) {
        toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
      });
  };
  const forgotpassword = () => {
    if (password == "" || newpassword == "") {
      toast.error("Thông tin không phù hợp")
    } else {
      const toastId = toast.loading('Loading...');
      axios
        .post('https://api.echomedi.com/api/user/resetPassword', {
          code: otp,
          password: password,
          passwordConfirmation: newpassword
        })
        .then(response => {
          toast.success('Thay đổi mật khẩu thành công');
          setShowModalFotgotPassword(false);
          setShowModalSuccess(true);
        })
        .catch(error => {
          toast.error("Không thể thay đổi mật khẩu. Vui lòng kiểm tra lại quá trình đổi mật khẩu")
        })
        .finally(() => {
          toast.dismiss(toastId);
        });;
    }

  }
  const verifyOTP = async () => {
    await axios.post('https://api.echomedi.com/api/user/verifyPhoneOTP', {
      "phone": phone_number,
      "otp": otp,
    })
      .then(async function (response) {
        const ok = response.data.ok;
        if (!ok) {
          toast.error(locale == "vi" ? "Mã OTP không chính xác." : "OTP is not valid.")
        } else {
          toast.success(locale == "vi" ? 'Bạn đã xác thực thành công, vui lòng đăng nhập!' : "You have successfully authenticated, please log in!");
        }
      })
      .catch(function (error) {
        toast.error(locale == "vi" ? "Đăng ký thất bại" : "Register fail")
      });
    setShowModal(false);
    setShowModalFotgotPassword(true)
  }
  const handleSendPhone = async () => {
    axios.post('https://api.echomedi.com/api/auth/forgotPassword', {
      email: phone_number
    });
    setShowModalPhone(false);
    setShowModal(true);
  }
  const handleBack = () => {
    router.push("/")
  }
  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale == "vi" ? "Quên Mật Khẩu" : "Forgot Password"}</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <section className="flex flex-col items-center justify-center px-6 mx-auto md:h-screen">
        {showModal && (
          <ModalLogin
            showCloseButton
            visibleModal={showModal}
            wrapperClassName="lg:!w-[435px] !w-[370px]"
            contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}
          >
            <div className=" bg-white rounded-lg w-full max-w-md overflow-hidden">
              <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Nhập mã xác thực để tiếp tục" : "Enter the verification code to continue"}</h3>
              <div className="flex justify-center mt-4">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                    ✓
                  </div>
                  <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
                </div>
                <div className="flex items-center">
                  <div className="border border-[#14813D] rounded-full p-1">
                    <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center">
                      2
                    </div>
                  </div>
                  <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center border border-[#B3B3B3]">
                    3
                  </div>
                </div>
              </div>
              <div className="px-4 py-6">
                <p className="py-4 text-center text-base">{locale == "vi" ? "Vui lòng nhập mã xác thực vừa được gửi đến số điện thoại" : "The authentication code has just been sent to the number"} {phone_number}</p>
                <div className="flex flex-col gap-2">
                  <label htmlFor="OTP" className="font-semibold">{locale === 'en' ? "Verification code" : "Mã xác thực"}</label>
                  <OtpInput
                    value={otp}
                    onChange={setOtp}
                    numInputs={6}
                    renderSeparator={<span className="md:px-4 px-2.5"></span>}
                    renderInput={(props) => <input {...props} style={{ width: 35, height: 35, textAlign: 'center', border: '1px solid black', borderRadius: 5, fontWeight: 'bold' }} />}
                  />
                </div>
                <div className="text-center flex items-center justify-between py-2">
                  <button onClick={handleResendOTP}
                    disabled={resendDisabled} className="text-[#156634] text-xs">{resendDisabled ? "Resend OTP" : "Resend OTP"}</button>
                  <span className="text-xs">{locale == "vi" ? `Mã xác nhận sẽ hết hạn trong ${countdown}s` : `The verification code will expire in ${countdown}s`}</span>
                </div>
                <div className="flex items-center justify-center mt-2">
                  <button onClick={verifyOTP} className="bg-green-700 hover:bg-green-800 text-white font-bold py-2 w-full rounded-full focus:outline-none focus:shadow-outline" type="button">
                    {locale == "vi" ? "Xác thực" : "Verify"}
                  </button>
                </div>
              </div>
            </div>
          </ModalLogin>
        )}
      </section>
      <ModalLogin
        showCloseButton
        visibleModal={showModalPhone}
        wrapperClassName="lg:!w-[435px] !w-[370px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalPhone(false)}
      >
        <section>
          <div>
            <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Đặt lại mật khẩu" : "Reset Password"}</h3>
            <div className="flex justify-center mt-4">
              <div className="flex items-center">
                <div className="border border-[#14813D] rounded-full p-1">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center">
                    1
                  </div>
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full  flex items-center justify-center border border-[#B3B3B3]">
                  2
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full flex items-center justify-center border border-[#B3B3B3]">
                  3
                </div>
              </div>
            </div>

            <form className="px-4 py-6">
              <p className="text-center text-base">{locale == "vi" ? "Vui lòng nhập số điện thoại liên kết với tài khoản của bạn để nhận mã xác thực qua tin nhắn" : "Enter the phone number linked with your account to receive the verification code via SMS"}</p>
              <div className="mt-4">
                <label htmlFor="text" className="block mb-2 font-semibold">
                  {locale === "en" ? "Phone Number" : "Số điện thoại"}
                </label>
                <input
                  type="text"
                  id="exampleFormControlInput1"
                  onChange={(e) => { setPhoneNumber(e.target.value) }}
                  name="password" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
              </div>
              <div className="flex items-center justify-center mt-4 gap-2">
                <button onClick={handleBack} className="font-bold py-2 w-full focus:outline-none focus:shadow-outline" type="button">
                  {locale == "vi" ? "Quay lại" : "Back"}
                </button>
                <button onClick={handleSendPhone} disabled={!phone_number} className="bg-[#156634] rounded-full hover:bg-[#166534] text-white font-bold py-2 w-full focus:outline-none focus:shadow-outline" type="button">
                  {locale == "vi" ? "Tiếp theo" : "Next"}
                </button>
              </div>
            </form>
          </div>
        </section>
      </ModalLogin>
      <ModalLogin
        showCloseButton
        visibleModal={showModalForgotPassword}
        wrapperClassName="lg:!w-[435px] !w-[370px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalFotgotPassword(false)}
      >
        <section>
          <div>
            <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Đặt lại mật khẩu" : "Reset Password"}</h3>
            <div className="flex justify-center mt-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                  ✓
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                  ✓
                </div>
                <div className="h-[2px] w-8 bg-[#B3B3B3] mx-1"></div>
              </div>
              <div className="flex items-center">
                <div className="border border-[#14813D] rounded-full p-1">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center">
                    3
                  </div>
                </div>
              </div>
            </div>
            <p className="text-center text-sm my-2">{locale === "en" ? "Please reset your new password" : "Vui lòng đặt lại mật khẩu mới"}</p>
            <div className="p-6 space-y-4 sm:p-8">
              <div className="space-y-3">
                <div>
                  <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "New Password" : "Mật khẩu Mới"}
                  </label>
                  <input
                    type="password"
                    id="exampleFormControlInput1"
                    onChange={(e) => { setPassword(e.target.value) }}
                    name="password" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
                <div>
                  <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">
                    {locale === "en" ? "Re-enter your new password" : "Nhập Lại Mật khẩu Mới"}
                  </label>
                  <input
                    type="password"
                    id="exampleFormControlInput1"
                    onChange={(e) => { setNewPassword(e.target.value) }}
                    name="password" className="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
                </div>
              </div>
              <button
                onClick={forgotpassword} className="w-full bg-[#156634] rounded-full hover:bg-[#166534] text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium text-sm md:px-5 py-2.5 text-center">
                {locale === "en" ? "Change Password" : "Đổi Mật Khẩu"}
              </button>
            </div>
          </div>
        </section>
      </ModalLogin>
      <ModalLogin
        showCloseButton
        visibleModal={showModalSuccess}
        wrapperClassName="lg:!w-[435px] !w-[370px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalSuccess(false)}
      >
        <section>
          <div>
            <h3 className="text-center md:text-2xl text-xl font-bold lg:mt-2">{locale == "vi" ? "Đặt lại mật khẩu" : "Reset Password"}</h3>
            <div className="flex justify-center my-5">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-[#14813D] text-white flex items-center justify-center border-2 border-[#14813D]">
                    ✓
                  </div>
                  {i < 3 && <div className="h-1 w-8 bg-[#156634] mx-1"></div>}
                </div>
              ))}
            </div>

            <p className="text-base text-center">{locale === "en" ? "Password changed successfully!" : "Đổi mật khẩu thành công!"}</p>
            <p className="text-base text-center mb-6">{locale === "en" ? "Please log in again to continue" : "Vui lòng đăng nhập lại để tiếp tục"}</p>
            <LinkComponent href={"/login"} locale={locale} skipLocaleHandling={false}>
              <p className="w-full bg-[#156634] text-white focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm px-5 py-2.5 text-center">
                {locale === "en" ? "Login" : "Đăng nhập"}
              </p>
            </LinkComponent>
          </div>
        </section>
      </ModalLogin>
      <Contact />
      <Toaster
        position="bottom-center"
      />
    </>
  );
};

export default Home;
