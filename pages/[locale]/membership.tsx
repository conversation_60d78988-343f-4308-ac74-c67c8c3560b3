import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import { makeStaticProps } from '../../lib/getStatic';
import LinkComponent from "../../components/Link";
import Image from 'next/image'
import axios from "axios";
import { addToCart } from "../../lib/ui";
import Modal from "../../components/components/Modal";
import HeaderMemBership from "../../components/Membership/HeaderMemBership";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [
    {
      params: {
        locale: 'en',
        slug: "test",
        label: "test2",
      }
    },
    {
      params: {
        locale: 'vi',
        slug: "test",
        label: "test2",
      }
    }],
})

export { getStaticPaths, getStaticProps }
const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showModalLogin, setShowModalLogin] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");


  const tranlsate = (s: string, locale: string | undefined) => {
    switch (s) {
      case "buy_now":
        if (locale === "en")
          return "Buy Now";
        else
          return "Mua ngay";
      case "add_to_cart":
        if (locale === "en")
          return "Add to cart";
        else
          return "Thêm vào giỏ";
      case "learn_more":
        if (locale === "en")
          return "Learn More";
        else
          return "Tìm hiểu thêm";
    }
    return "";
  }
  const addMemberToCart = (id: number, locale: string) => {
    if (localStorage.getItem('token')) {
      const token = localStorage.getItem('token');
      axios.post('https://api.echomedi.com/api/product/addServiceToCart', {
        "service_id": id,
        "quantity": 1,
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
        .then(function (response) {
          toast.success(locale === "en" ? "Added to  cart" : "Thêm vào giỏ hàng thành công!");
          let els = document.getElementsByClassName('num-of-item');
          for (let i = 0; i < els.length; ++i) {
            let el = els[i] as HTMLElement;
            let parentNode = el.parentElement as HTMLElement;
            if (el) {
              el.innerText = (isNaN(parseInt(el.innerText)) ? 1 : parseInt(el.innerText) + 1).toString();
              parentNode.classList.remove('invisible');
            }
          }
          router.push(router.pathname.includes(`/${locale}/`) ? `/buy_now` : `/${locale}/buy_now`);
        })
        .catch(function (error) {
          toast.error("Thêm vào giỏ hàng thất bại")
          if (error.response.status == 401) {
            toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
            localStorage.removeItem("token");
            window.location.href = '/login';
          }
        });
    } else {
      toast.success('Vui lòng đăng nhập.');
      setShowModalLogin(true)
      // window.location.href = "/" + locale + "/login", "/" + locale + "/login";
    }
  }
  const login = () => {
    if (email == "" || password == "") {
      toast.error("Thông tin không phù hợp")
    } else {
      const toastId = toast.loading('Loading...');
      axios
        .post('https://api.echomedi.com/api/user/auth', {
          identifier: email,
          password: password,
        })
        .then(response => {
          toast.success(locale == "vi" ? 'Đăng nhập thành công' : "Login successful");
          setShowModalLogin(false);
          localStorage.setItem('token', response.data.jwt);
          window.location.reload();
        })
        .catch(error => {
          toast.error(locale == "vi" ? "Đăng nhập không thành công. Vui lòng kiểm tra lại tên đăng nhập, mật khẩu" : "Login fail. Invalid credentials")
        })
        .finally(() => {
          toast.dismiss(toastId);
          // location.href = "/";
        });;
    }
  }
  const data = [
    {
      id: 740,
      name: locale === "en" ? "Gold Membership" : "Thành viên vàng",
      description: locale === "en" ? "Gold membership offers members an enhanced healthcare package, including free clinic check-ups and monthly telemedicine examinations. Members also receive discounts when using clinic services and purchasing medications. Join as a Gold member for a low, fixed yearly price." : "Gói thành viên vàng là gói chăm sóc sức khỏe nâng cao cho thành viên với các đặc quyền như: Miễn phí khám bệnh tại phòng khám và tư vấn sức khỏe từ xa 1 lần mỗi tháng, và nhận được các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên vàng với mức chi phí tiết kiệm và cố định hằng năm.",
      priceBeforeDiscount: "8.000.000 VND",
      priceAfterDiscount: "4.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_vien_vang_5c5f60adab.svg",
      link: "/membergold",
    },
    {
      id: 741,
      name: locale === "en" ? "Platinum Membership" : "Thành viên bạch kim",
      description: locale === "en" ? "Platinum membership offers a comprehensive healthcare package, including free unlimited clinic evaluations and telemedicine, a complimentary general health check, as well as discounts on services and medications at the clinic. Become a Platinum member for a low, fixed yearly price." : "Gói thành viên bạch kim là gói chăm sóc sức khỏe toàn diện với các đặc quyền: Miễn phí không giới hạn khám tại phòng khám và tư vấn sức khỏe từ xa, miễn phí kiểm tra sức khỏe tổng quát, kèm các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên bạch kim với chi phí tiết kiệm và cố định hàng năm.",
      priceBeforeDiscount: "16.000.000 VND",
      priceAfterDiscount: "8.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_vien_bach_kim_b1f7b331a3.svg",
      link: "/memberplatinum",
    },
    {
      id: 839,
      name: locale === "en" ? "Family Doctor" : "Bác sĩ gia đình",
      description: locale === "en" ? "In today's fast-paced world, people are seeking healthcare solutions that are both efficient and reliable. ECHO MEDI has come up with an innovative service that is tailored to meet this need. The Family Doctor package allows customers to avail telehealth consultation service using cutting-edge technology. Customers can be assured that they are receiving high-quality healthcare services that are both convenient and secure." : "Đời sống ngày càng phát triển, nhu cầu về chất lượng và độ tiện dụng của dịch vụ ngày càng tăng cao. ECHO MEDI tạo ra gói Bác sĩ gia đình, cung cấp cho khách hàng dịch vụ tư vấn sức khỏe từ xa bằng cách ứng dụng công nghệ tiên tiến. Khách hàng không còn cần phải đến phòng khám để khám bệnh mà có thể tiếp cận đội ngũ y tế mọi lúc mọi nơi.",
      priceBeforeDiscount: "3.000.000 VND",
      priceAfterDiscount: "1.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Bac_si_gia_dinh_6448f2333d.svg",
      link: "/personal_medical_provider",
    },
    {
      id: 2,
      name: locale === "en" ? "Corporate Membership" : "Gói doanh nghiệp",
      description: locale === "en" ? "Package for the corporate members will change depending on the needs of each company. Please contact us directly for more information to purchase the most suitable offer for your company." : "Gói thành viên cho doanh nghiệp sẽ thay đổi phụ thuộc vào nhu cầu của từng doanh nghiệp. Hãy liên hệ trực tiếp với chúng tôi để được tư vấn và nhận ưu đãi phù hợp nhất dành cho doanh nghiệp của bạn.",
      priceBeforeDiscount: "",
      textPrice: locale === "en" ? "Contact" : "Liên hệ",
      contactText: locale === "en" ? "Contact" : "Liên hệ",
      contactNumber: "1900 638 408",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Goi_doanh_nghiep_1408ebc63c.png",
    },

  ];
  const datanhi = [
    {
      id: 859,
      name: locale === "en" ? "Children's membership\n (For children from 0-16 years old)" : "Thành viên trẻ em\n (Dành cho trẻ từ 0-16 tuổi)",
      description: locale === "en" ? "The child membership package offers comprehensive care for children, including unlimited clinic visits and online consultations. It also includes two free mental health consultations or therapy sessions, a free consultation on genetic decoding test results, a personalized child health monitoring handbook, and discounts for other services and when purchasing medicine at the clinic." : "Gói thành viên trẻ em giúp trẻ nhận được sự chăm sóc tối ưu với số lần khám tại phòng khám cũng như tư vấn trực tuyến không giới hạn, được tham vấn/ trị liệu tâm lý miễn phí 2 lần, miễn phí tư vấn sử dụng kết quả xét nghiệm gen, nhận được sổ tay theo dõi và chăm sóc thiết kế riêng cho trẻ cùng với các ưu đãi khi sử dụng những dịch vụ khác và mua thuốc tại phòng khám.",
      priceAfterDiscount: "3.000.000 VND",
      buttonText: locale === "en" ? "Buy Now" : "Mua ngay",
      learnMoreText: locale === "en" ? "Learn More" : "Tìm hiểu thêm",
      imageSrc: "https://d3e4m6b6rxmux9.cloudfront.net/Thanh_vien_nhi_59f30946ca.svg",
      link: "/memberchildren",
      contactNumber: "",
      priceBeforeDiscount: "",
      contactText: locale === "en" ? "" : "",
    },

  ];
  const [activeTab, setActiveTab] = useState('tabs-membership-1');

  const handleTabClick = (tab: any) => {
    setActiveTab(tab);
  };

  return (
    <>
      <Head>
        <title>ECHO MEDI - {locale === "vi" ? "Thành viên" : "Membership"}</title>
        <meta name="description" content={locale === "vi" ? "Tìm hiểu về các gói thành viên của ECHO MEDI, với nhiều lợi ích và ưu đãi đặc biệt dành cho khách hàng." : "Learn about ECHO MEDI's membership packages, offering special benefits and exclusive deals for customers."} />
        <meta name="keywords" content={locale === "vi" ? "ECHO MEDI, gói thành viên, ưu đãi, dịch vụ y tế" : "ECHO MEDI, membership package, benefits, healthcare services"} />
        <meta property="og:title" content="ECHO MEDI - Membership" />
        <meta property="og:description" content={locale === "vi" ? "Tìm hiểu về các gói thành viên của ECHO MEDI." : "Learn about ECHO MEDI's membership packages."} />
        <meta property="og:image" content="https://d3e4m6b6rxmux9.cloudfront.net/Old_Mobile_VIE_d75fc17c01.webp" />
        <meta property="og:url" content="https://echomedi.com/membership" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta property="twitter:title" content="ECHO MEDI - Membership" />
        <meta property="twitter:description" content={locale === "vi" ? "Các gói thành viên của ECHO MEDI." : "ECHO MEDI's membership packages."} />
        <meta property="twitter:image" content="https://d3e4m6b6rxmux9.cloudfront.net/Old_Mobile_VIE_d75fc17c01.webp" />
        <link rel="icon" href="/favicon1.png" />
      </Head>

      <HeaderMemBership locale={locale} />
      <div className="max-w-screen-2xl mx-auto">
        <div className="relative">
          <div className="max-w-[1280px] mx-auto text-center mt-6">
            <h3 className="text-2xl md:text-3xl text-center text-[#156634] font-bold mt-4">{locale === "en" ? "MEMBERSHIP" : "THÀNH VIÊN"}</h3>
            <p className="font-normal leading-6 text-gray-600 mb-3 text-base text-center align-middle md:mx-16 mx-4 my-2">
              {locale === "en" ? "ECHO MEDI offers a range of memberships tailored to meet the unique needs of each member. Our team works closely with you to identify the best membership option for your health and wellness requirements." : "ECHO MEDI cung cấp những gói thành viên khác nhau, được tạo ra nhằm đáp ứng nhu cầu đặc biệt của mỗi cá nhân. Đội ngũ của chúng tôi sẽ sát cánh với bạn trong việc chọn lựa gói thành viên thích hợp với tình trạng sức khỏe."}
            </p>
          </div>
        </div>
        <div className="tabs">
          <div
            className="mb-15 border-b-[1px] flex flex-wrap justify-center rounded-[10px] md:flex-nowrap md:items-center lg:gap-7 xl:mb-21 xl:gap-12"
          >
            <div
              onClick={() => handleTabClick("tabs-membership-1")}
              className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${activeTab === "tabs-membership-1"
                ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                : ""
                }`}
            >
              <div className="md:w-3/5 lg:w-auto ">
                <button className={`text-lg ${activeTab === "tabs-membership-1" ? "text-[#156634] font-bold" : "text-black "
                  } xl:text-regular`}>
                  {locale === "en" ? "Adult" : "Người Lớn"}
                </button>
              </div>
            </div>
            <div
              onClick={() => handleTabClick("tabs-membership-2")}
              className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${activeTab === "tabs-membership-2"
                ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                : ""
                }`}
            >
              <div className="md:w-3/5 lg:w-auto">
                <button className={`text-lg  ${activeTab === "tabs-membership-2" ? "text-[#156634] font-bold" : "text-black "
                  } xl:text-regular`}>
                  {locale === "en" ? "Children" : "Trẻ Em"}
                </button>
              </div>
            </div>
          </div>
          <div className="mt-3">
            <div
              id="tabs-membership-1"
              role="tabpanel"
              aria-labelledby="tabs-membership-1"
              className={`tabcontent ${activeTab === 'tabs-membership-1' ? '' : 'hidden'}`}
            >
              <div className="w-full h-full col-span-2 md:col-span-1 row-span">
                <div className="mx-auto p-4">
                  <div className="grid grid-rows-none sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-4">
                    {data.map((item, index) => (
                      <>
                        {index === data.length - 1 ? (
                          <div key={index} className='flex flex-col relative pb-[55px] hover:border hover:border-[#14813d] bg-white rounded-xl group'>
                            <div className="h-[250px] w-full  relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                              <Image loading='lazy' layout="fill" className="w-full object-cover rounded-t-[12px] transition-opacity" alt="Image Tọa Đàm Sức Khỏe" src={item.imageSrc} />
                            </div>
                            <div className='px-[17px]'>
                              {item.textPrice && (
                                <p className='font-medium text-sm text-[#166534] mt-3'>{item.textPrice}</p>
                              )}
                              <p className="text-base font-semibold whitespace-pre-line mb-2">{item.name}</p>
                              <p className='text-sm text-justify'>{item.description}</p>
                            </div>
                            <div className='absolute bottom-4 w-full'>
                              <div className='mt-auto flex items-end justify-end px-4'>
                                <div className="flex items-end justify-end">
                                  {item.contactText && (
                                    <button className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-8 text-sm font-medium text-white">
                                      <a href={`tel:${item.contactNumber}`}>{item.contactText}</a>
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className='flex flex-col relative pb-[55px] hover:border hover:border-[#14813d] bg-white rounded-xl group'>
                            <LinkComponent href={item.link} skipLocaleHandling={false} locale={locale}>
                              <div className="h-[250px] w-full relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                                <Image loading='lazy' layout="fill" className="w-full object-cover rounded-t-[12px] transition-opacity group-hover:opacity-30" alt="Image Tọa Đàm Sức Khỏe" src={item.imageSrc} />
                                {item.id !== 839 ? (
                                  <Image loading='lazy' width={65} height={32} className="absolute top-1 -left-1" alt="Image Icon Sale" src={locale === "en" ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_14e4417fba.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Sale_952366e44c.svg"} />
                                ) 
                                : (
                                  ""
                                  // <Image loading='lazy' width={65} height={32} className="absolute top-1 -left-1" alt="Image Icon Sale" src={locale === "en" ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_8b7570c7d0.svg" : "https://d3e4m6b6rxmux9.cloudfront.net/Sale80_63954d67d0.svg"} />
                                )}
                                {item.priceAfterDiscount && (
                                  <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                                    <button className="px-4 py-2 text-base flex items-center gap-1 text-white transition-all duration-5000 ease-in-out transform translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-100">
                                      {locale === "en" ? "Learn More" : "Xem chi tiết"}
                                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.01343 3.62499C9.18832 3.62568 9.35592 3.69078 9.48009 3.80624L12.5401 6.67499C12.9146 7.02656 13.125 7.50312 13.125 7.99999C13.125 8.49687 12.9146 8.97343 12.5401 9.32499L9.48009 12.1937C9.35518 12.3101 9.18622 12.3755 9.01009 12.3755C8.83397 12.3755 8.665 12.3101 8.54009 12.1937C8.47761 12.1356 8.42801 12.0665 8.39417 11.9904C8.36032 11.9142 8.34289 11.8325 8.34289 11.75C8.34289 11.6675 8.36032 11.5858 8.39417 11.5096C8.42801 11.4335 8.47761 11.3643 8.54009 11.3062L11.6001 8.44374C11.6626 8.38564 11.7122 8.31651 11.746 8.24035C11.7799 8.16419 11.7973 8.0825 11.7973 7.99999C11.7973 7.91748 11.7799 7.83579 11.746 7.75963C11.7122 7.68347 11.6626 7.61434 11.6001 7.55624L8.54009 4.69374C8.47761 4.63564 8.42801 4.56651 8.39417 4.49035C8.36032 4.41419 8.3429 4.3325 8.3429 4.24999C8.3429 4.16748 8.36032 4.08579 8.39417 4.00963C8.42801 3.93347 8.47761 3.86434 8.54009 3.80624C8.60239 3.74832 8.67626 3.70249 8.75749 3.67138C8.83871 3.64028 8.92569 3.62452 9.01343 3.62499Z" fill="white" />
                                        <path d="M4.34689 3.62499C4.52178 3.62568 4.68938 3.69078 4.81355 3.80624L8.81354 7.55624C8.87603 7.61434 8.92563 7.68347 8.95947 7.75963C8.99332 7.83579 9.01074 7.91748 9.01074 7.99999C9.01074 8.0825 8.99332 8.16419 8.95947 8.24035C8.92563 8.31651 8.87603 8.38564 8.81354 8.44374L4.81355 12.1937C4.68864 12.3101 4.51967 12.3755 4.34355 12.3755C4.16743 12.3755 3.99846 12.3101 3.87355 12.1937C3.81107 12.1356 3.76147 12.0665 3.72762 11.9904C3.69378 11.9142 3.67635 11.8325 3.67635 11.75C3.67635 11.6675 3.69378 11.5858 3.72762 11.5096C3.76147 11.4335 3.81107 11.3643 3.87355 11.3062L7.40021 7.99999L3.87355 4.69374C3.81107 4.63564 3.76147 4.56651 3.72762 4.49035C3.69378 4.41419 3.67635 4.3325 3.67635 4.24999C3.67635 4.16748 3.69378 4.08579 3.72762 4.00963C3.76147 3.93347 3.81107 3.86434 3.87355 3.80624C3.93585 3.74832 4.00972 3.70249 4.09095 3.67138C4.17217 3.64028 4.25915 3.62452 4.34689 3.62499Z" fill="white" />
                                      </svg>
                                    </button>
                                  </div>
                                )}
                              </div>
                              <div className='px-[17px]'>
                                {/* <section className="flex items-center justify-between">
                                  {item.priceAfterDiscount && (
                                    <p className='font-medium text-sm text-[#166534] mt-3'>{item.priceAfterDiscount}{locale === "en" ? "/year" : "/năm"}</p>
                                  )}
                                  {item.priceBeforeDiscount && (
                                    <p className='font-medium text-sm text-[#8E8E8E] mt-3 line-through'>{item.priceBeforeDiscount}{locale === "en" ? "/year" : "/năm"}</p>
                                  )}
                                  {item.textPrice && (
                                    <p className='font-medium text-sm text-[#166534] mt-3'>{item.textPrice}</p>
                                  )}
                                </section> */}
                                <p className="text-base font-semibold whitespace-pre-line mb-2 mt-3">{item.name}</p>
                                <p className='text-sm text-justify'>{item.description}</p>
                              </div>
                            </LinkComponent>
                            {/* <div className='absolute bottom-4 w-full'>
                              <div className='mt-auto flex items-end justify-between px-4'>
                                <button onClick={() => addToCart(item.id, locale)} className="flex gap-1 rounded-3xl py-2 px-2 text-sm font-medium text-[#156634]">
                                  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 0.625C0.904822 0.625 0.625 0.904822 0.625 1.25C0.625 1.59518 0.904822 1.875 1.25 1.875H2.01201L4.23966 10.7856C3.58297 11.0772 3.125 11.7351 3.125 12.5C3.125 13.5355 3.96447 14.375 5 14.375C6.03553 14.375 6.875 13.5355 6.875 12.5C6.875 12.2809 6.8374 12.0705 6.76831 11.875H9.48169C9.4126 12.0705 9.375 12.2809 9.375 12.5C9.375 13.5355 10.2145 14.375 11.25 14.375C12.2855 14.375 13.125 13.5355 13.125 12.5C13.125 11.4645 12.2855 10.625 11.25 10.625H5.48799L5.17549 9.375H11.25C12.5401 9.375 13.3137 8.55992 13.743 7.65991C14.1624 6.78045 14.3085 5.72932 14.3594 4.9648C14.4303 3.90009 13.5502 3.125 12.5757 3.125H3.61299L3.22469 1.57183C3.08558 1.01537 2.5856 0.625 2.01201 0.625H1.25ZM11.25 8.125H4.86299L3.92549 4.375H12.5757C12.9214 4.375 13.129 4.62922 13.1121 4.88174C13.0646 5.59513 12.9316 6.45737 12.6147 7.12179C12.3076 7.76567 11.8876 8.125 11.25 8.125ZM11.25 13.1211C10.907 13.1211 10.6289 12.843 10.6289 12.5C10.6289 12.157 10.907 11.8789 11.25 11.8789C11.593 11.8789 11.8711 12.157 11.8711 12.5C11.8711 12.843 11.593 13.1211 11.25 13.1211ZM4.37886 12.5C4.37886 12.843 4.65695 13.1211 5 13.1211C5.34305 13.1211 5.62114 12.843 5.62114 12.5C5.62114 12.157 5.34305 11.8789 5 11.8789C4.65695 11.8789 4.37886 12.157 4.37886 12.5Z" fill="#156634" />
                                  </svg>
                                  {tranlsate("add_to_cart", locale)}
                                </button>
                                {item.buttonText && (
                                  <button onClick={() => addMemberToCart(item.id, locale)} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-medium text-white">
                                    {tranlsate("buy_now", locale)}
                                  </button>
                                )}
                              </div>
                            </div> */}
                          </div>
                        )}
                      </>
                    ))}
                  </div >
                </div>
              </div>
            </div>
            <div
              id="tabs-membership-2"
              role="tabpanel"
              aria-labelledby="tabs-membership-2"
              className={`tabcontent ${activeTab === 'tabs-membership-2' ? '' : 'hidden'}`}
            >
              <div className="w-full h-full col-span-2 md:col-span-1 row-span">
                <div className="mx-auto p-4">
                  <div className="grid grid-rows-none sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-4">
                    {datanhi.map((item, index) => (
                      <>
                        <div key={index} className='flex flex-col relative pb-[55px] hover:border hover:border-[#14813d] bg-white rounded-xl group'>
                          <LinkComponent href={item.link} skipLocaleHandling={false} locale={locale}>
                            <div className="h-[250px] w-full  relative rounded-t-[12px] overflow-hidden bg-[#12512A] transition-all duration-2000 ease-in-out group-hover:bg-[#0C3A1D]">
                              <Image loading='lazy' layout="fill" className="w-full object-cover rounded-t-[12px] transition-opacity group-hover:opacity-30" alt="Image Tọa Đàm Sức Khỏe" src={item.imageSrc} />
                              {item.priceAfterDiscount && (
                                <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                                  <button className="px-4 py-2 text-base flex items-center gap-1 text-white transition-all duration-5000 ease-in-out transform translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-100">
                                    {locale === "en" ? "Learn More" : "Xem chi tiết"}
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.01343 3.62499C9.18832 3.62568 9.35592 3.69078 9.48009 3.80624L12.5401 6.67499C12.9146 7.02656 13.125 7.50312 13.125 7.99999C13.125 8.49687 12.9146 8.97343 12.5401 9.32499L9.48009 12.1937C9.35518 12.3101 9.18622 12.3755 9.01009 12.3755C8.83397 12.3755 8.665 12.3101 8.54009 12.1937C8.47761 12.1356 8.42801 12.0665 8.39417 11.9904C8.36032 11.9142 8.34289 11.8325 8.34289 11.75C8.34289 11.6675 8.36032 11.5858 8.39417 11.5096C8.42801 11.4335 8.47761 11.3643 8.54009 11.3062L11.6001 8.44374C11.6626 8.38564 11.7122 8.31651 11.746 8.24035C11.7799 8.16419 11.7973 8.0825 11.7973 7.99999C11.7973 7.91748 11.7799 7.83579 11.746 7.75963C11.7122 7.68347 11.6626 7.61434 11.6001 7.55624L8.54009 4.69374C8.47761 4.63564 8.42801 4.56651 8.39417 4.49035C8.36032 4.41419 8.3429 4.3325 8.3429 4.24999C8.3429 4.16748 8.36032 4.08579 8.39417 4.00963C8.42801 3.93347 8.47761 3.86434 8.54009 3.80624C8.60239 3.74832 8.67626 3.70249 8.75749 3.67138C8.83871 3.64028 8.92569 3.62452 9.01343 3.62499Z" fill="white" />
                                      <path d="M4.34689 3.62499C4.52178 3.62568 4.68938 3.69078 4.81355 3.80624L8.81354 7.55624C8.87603 7.61434 8.92563 7.68347 8.95947 7.75963C8.99332 7.83579 9.01074 7.91748 9.01074 7.99999C9.01074 8.0825 8.99332 8.16419 8.95947 8.24035C8.92563 8.31651 8.87603 8.38564 8.81354 8.44374L4.81355 12.1937C4.68864 12.3101 4.51967 12.3755 4.34355 12.3755C4.16743 12.3755 3.99846 12.3101 3.87355 12.1937C3.81107 12.1356 3.76147 12.0665 3.72762 11.9904C3.69378 11.9142 3.67635 11.8325 3.67635 11.75C3.67635 11.6675 3.69378 11.5858 3.72762 11.5096C3.76147 11.4335 3.81107 11.3643 3.87355 11.3062L7.40021 7.99999L3.87355 4.69374C3.81107 4.63564 3.76147 4.56651 3.72762 4.49035C3.69378 4.41419 3.67635 4.3325 3.67635 4.24999C3.67635 4.16748 3.69378 4.08579 3.72762 4.00963C3.76147 3.93347 3.81107 3.86434 3.87355 3.80624C3.93585 3.74832 4.00972 3.70249 4.09095 3.67138C4.17217 3.64028 4.25915 3.62452 4.34689 3.62499Z" fill="white" />
                                    </svg>
                                  </button>
                                </div>
                              )}
                            </div>
                            <div className='px-[17px]'>
                              {/* {item.priceAfterDiscount && (
                                <p className='font-medium text-sm text-[#166534] mt-3'>{item.priceAfterDiscount}{locale === "en" ? "/year" : "/năm"}</p>
                              )} */}
                              <p className="text-base font-semibold whitespace-pre-line mb-2 mt-3">{item.name}</p>
                              <p className='text-sm text-justify'>{item.description}</p>
                            </div>
                          </LinkComponent>
                          {/* <div className='absolute bottom-4 w-full'>
                            <div className='mt-auto flex items-center justify-between px-4'>
                              <button onClick={() => addToCart(item.id, locale)} className="flex gap-1 rounded-3xl py-2 px-2 text-sm font-medium text-[#156634]">
                                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 0.625C0.904822 0.625 0.625 0.904822 0.625 1.25C0.625 1.59518 0.904822 1.875 1.25 1.875H2.01201L4.23966 10.7856C3.58297 11.0772 3.125 11.7351 3.125 12.5C3.125 13.5355 3.96447 14.375 5 14.375C6.03553 14.375 6.875 13.5355 6.875 12.5C6.875 12.2809 6.8374 12.0705 6.76831 11.875H9.48169C9.4126 12.0705 9.375 12.2809 9.375 12.5C9.375 13.5355 10.2145 14.375 11.25 14.375C12.2855 14.375 13.125 13.5355 13.125 12.5C13.125 11.4645 12.2855 10.625 11.25 10.625H5.48799L5.17549 9.375H11.25C12.5401 9.375 13.3137 8.55992 13.743 7.65991C14.1624 6.78045 14.3085 5.72932 14.3594 4.9648C14.4303 3.90009 13.5502 3.125 12.5757 3.125H3.61299L3.22469 1.57183C3.08558 1.01537 2.5856 0.625 2.01201 0.625H1.25ZM11.25 8.125H4.86299L3.92549 4.375H12.5757C12.9214 4.375 13.129 4.62922 13.1121 4.88174C13.0646 5.59513 12.9316 6.45737 12.6147 7.12179C12.3076 7.76567 11.8876 8.125 11.25 8.125ZM11.25 13.1211C10.907 13.1211 10.6289 12.843 10.6289 12.5C10.6289 12.157 10.907 11.8789 11.25 11.8789C11.593 11.8789 11.8711 12.157 11.8711 12.5C11.8711 12.843 11.593 13.1211 11.25 13.1211ZM4.37886 12.5C4.37886 12.843 4.65695 13.1211 5 13.1211C5.34305 13.1211 5.62114 12.843 5.62114 12.5C5.62114 12.157 5.34305 11.8789 5 11.8789C4.65695 11.8789 4.37886 12.157 4.37886 12.5Z" fill="#156634" />
                                </svg>
                                {tranlsate("add_to_cart", locale)}
                              </button>
                              {item.buttonText && (
                                <button onClick={() => addMemberToCart(item.id, locale)} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-6 text-sm font-medium text-white">
                                  {tranlsate("buy_now", locale)}
                                </button>
                              )}
                            </div>
                          </div> */}
                        </div>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        showCloseButton
        visibleModal={showModalLogin}
        wrapperClassName="w-[380px] md:w-[500px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModalLogin(false)}
      >
        <div className="w-full rounded-lg md:mt-0 sm:max-w-md xl:p-0">
          <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
            <h1 className="text-sm font-bold leading-tight tracking-tight text-[#14532D] md:text-2xl">
              {locale === "en" ? "Sign in" : "Đăng nhập"}
            </h1>
            <div>
              <label htmlFor="email" className="block  mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Email or phone number" : "Email hoặc số điện thoại"}</label>
              <input
                id="exampleFormControlInput1"
                onChange={(e) => { setEmail(e.target.value) }}
                type="text" name="email" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
            </div>
            <div>
              <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900">{locale === "en" ? "Password" : "Mật khẩu"}</label>
              <input
                id="exampleFormControlInput1"
                onChange={(e) => { setPassword(e.target.value) }}
                type="password" name="password" className="bg-gray-50 h-10 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5" />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-start">
                <div className="flex items-center h-4">
                  <input id="remember" aria-describedby="remember" type="checkbox" className="w-3 h-3 border border-gray-300 rounded" />
                </div>
                <div className="ml-3 text-xs">
                  <label htmlFor="remember" className="text-gray-500 font-bold">{locale === "en" ? "Remember me" : "Ghi nhớ đăng nhập"}</label>
                </div>
              </div>
            </div>
            <button type="button" onClick={login} className="w-full text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">{locale === "en" ? "Sign in" : "Đăng nhập"}</button>
            <div className="flex items-center justify-between">
              <p className="text-sm font-light text-gray-500">
                {locale === "en" ? "Don’t have an account yet? " : "Bạn chưa có tài khoản? "}<a href={"/signup"}><button className="font-medium text-green-800 hover:underline">{locale === "en" ? "Sign up" : "Đăng ký"}</button></a>
              </p>
              <LinkComponent href={"/forgotpassword"} locale={locale} skipLocaleHandling={false}>
                <p onClick={() => setShowModalLogin(false)}
                  className="font-medium text-green-800 hover:underline">{locale === "en" ? "Forgot password" : "Quên mật khẩu"}
                </p>
              </LinkComponent>
            </div>
          </div>
        </div>
      </Modal>
      <Contact />
      <Toaster
        position="bottom-center"
      />
    </>
  );
};

export default Home;
