import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import { makeStaticProps } from '../../lib/getStatic';
import React from "react";
import { addToCart } from "../../lib/ui";
import HeaderMemBership from "../../components/Membership/HeaderMemBership";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';

  return (
    <>
      <Head>
        <title>ECHO MEDI</title>
        <meta
          name="ECHO MEDI"
          content="ECHO MEDI"
        />
        <meta name="keywords" content="ECHO MEDI"></meta>
        <link rel="icon" href="/favicon1.png" />
      </Head>
      <HeaderMemBership locale={locale} />
      <div className="noselect 2xl:mx-auto lg:pb-16 lg:px-20 md:py-2 px-2 pb-9 m-auto">
        <h3 className="text-2xl md:text-3xl text-center text-[#156634] font-bold mt-4 whitespace-pre-line">{locale === "en" ? "Children's membership package\n (For children from 0-16 years old)" : "Gói thành viên trẻ em\n (Dành cho trẻ từ 0-16 tuổi)"}</h3>
        <div className="flex flex-col justify-between">
          <p className="font-normal leading-6 mb-3 text-base text-justify md:text-center align-middle my-2">
            {locale === "en" ? "The child membership package offers comprehensive care for children, including unlimited clinic visits and online consultations. It also includes two free mental health consultations or therapy sessions, a free consultation on genetic decoding test results, a personalized child health monitoring handbook, and discounts for other services and when purchasing medicine at the clinic." : "Gói thành viên trẻ em giúp trẻ nhận được sự chăm sóc tối ưu với số lần khám tại phòng khám cũng như tư vấn trực tuyến không giới hạn, được tham vấn/ trị liệu tâm lý miễn phí 2 lần, miễn phí tư vấn sử dụng kết quả xét nghiệm gen, nhận được sổ tay theo dõi và chăm sóc thiết kế riêng cho trẻ cùng với các ưu đãi khi sử dụng những dịch vụ khác và mua thuốc tại phòng khám."}
          </p>
          <div className="overflow-x-auto">
            <table className="table w-full text-sm">
              {/* head */}
              <thead>
                <tr>
                  <th className="text-center align-middle w-[30%] bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634] md:text-xl text-sm">{locale === "en" ? "Services" : "Dịch Vụ"}</th>
                  <th className="text-center align-middle w-[30%] bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634] md:text-xl text-sm">{locale === "en" ? "Non-member" : "Khách Hàng \n Thông Thường"}</th>
                  <th className="text-center py-6 md:py-0 text-white align-middle w-[40%] relative bg-header-family h-16 shadow-2xl border-none rounded-lg md:text-xl text-sm">
                    {locale === "en" ? "Offters" : "Ưu Đãi Áp Dụng"}
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* row 1 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "In-clinic physician evaluation" : "Khám tại phòng khám"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "350.000 VND/visit" : "350.000 VND/lần"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "Unlimited" : "Miễn phí\nkhông giới hạn"}
                    </section>
                  </td>
                </tr>
                {/* row 2 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "In-clinic psychological consultation (60 minutes)*" : "Tham vấn/Trị liệu tâm lý tại phòng khám*"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "800.000 VND/session" : "800.000 VND/lần"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "2 sessions/year" : "2 lần/năm"}
                    </section>
                  </td>
                </tr>
                {/* row 3 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Personalized Health Plan" : "Bác sĩ thiết kế riêng “Kế Hoạch Chăm Sóc Sức Khỏe” cho trẻ em"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "-" : "-"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "1 plan/year" : "1 lần/năm"}
                    </section>
                  </td>
                </tr>
                {/* row 4 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Telehealth consultation" : "Tư vấn sức khỏe từ xa"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "250.000 VND/call" : "250.000 VND/lần"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "Unlimited" : "Miễn phí\nkhông giới hạn"}
                    </section>
                  </td>
                </tr>
                {/* row 5 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "In clinic services for children" : "Dịch vụ xét nghiệm và gói khám thực hiện tại phòng khám"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "30% discount" : "Giảm 30%\ngiá niêm yết"}
                    </section>
                  </td>
                </tr>
                {/* row 6 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Medicine and supplements" : "Thuốc và thực phẩm chức năng"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "15% discount" : "Giảm 15%\ngiá niêm yết"}
                    </section>
                  </td>
                </tr>
                {/* row 7 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Children psychological consultation packages" : "Các gói dịch vụ tham vấn tâm lý trẻ em"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "30% discount" : "Giảm 30%\ngiá niêm yết"}
                    </section>
                  </td>
                </tr>
                {/* row 8 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Genetic decoding services collaborate with Genetica" : "Xét nghiệm gen cùng Genetica"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "30% discount" : "Giảm 30%\ngiá niêm yết"}
                    </section>
                  </td>
                </tr>
                {/* row 9 */}
                <tr>
                  <td className=" bg-white border-[#E6E9F5]">{locale === "en" ? "Genetic decoding test result consultation" : "Tư vấn sử dụng kết quả xét nghiệm gen"}</td>
                  <td className="text-center align-middle bg-white border-[#E6E9F5]">{locale === "en" ? "1.000.000 VND/session" : "1.000.000 VND/lần"}</td>
                  <td className="text-center align-middle bg-body-family border-[#E6E9F5]">
                    <section className="flex items-center md:justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                      <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                      </svg>
                      {locale === "en" ? "Free 1 session/year" : "Miễn phí\n1 lần/năm"}
                    </section>
                  </td>
                </tr>
                <tr>
                  <td colSpan={2} className="font-bold align-middle bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634]">{locale === "en" ? "Price membership" : "Giá gói Thành Viên"}</td>
                  <td className="py-6 text-center md:py-0 align-middle relative bg-header-family md:h-16 border-none rounded-lg text-white md:text-lg">
                    {locale === "en" ? "3.000.000 VND/year" : "3.000.000 VND/năm"}
                  </td>
                </tr>
                <tr>
                  <td colSpan={2} className="border-none">
                  </td>
                  <td className="border-none">
                    <section className="hidden md:block">
                      <div className='mt-4 flex items-center justify-around'>
                        <a href="tel:1900638408" className="border border-[#156634] rounded-3xl py-1.5 md:px-12 text-[#156634] font-medium" data-original-title="Liên hệ với chúng tôi">
                          {locale === "en" ? "Contact" : "Tư vấn gói"}
                        </a>
                        <button onClick={() => { addToCart(859, locale) }} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 md:px-12 text-sm font-medium text-white">
                          {locale === "en" ? "Add To Cart" : "Mua ngay"}
                        </button>
                      </div>
                    </section>
                  </td>
                </tr>
              </tbody>
            </table>
            <section className="block md:hidden">
              <div className="flex items-center justify-between">
                <div>
                  <a href="tel:1900638408" className="border border-[#156634] rounded-3xl py-1 px-12 text-[#156634] font-medium" data-original-title="Liên hệ với chúng tôi">
                    {locale === "en" ? "Contact" : "Tư vấn gói"}
                  </a>
                </div>
                <div>
                  <button onClick={() => { addToCart(859, locale) }} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-12 text-sm font-medium text-white">
                    {locale === "en" ? "Add To Cart" : "Mua ngay"}
                  </button>
                </div>
              </div>
            </section>
            {locale == "vi" ?
              <div className="text-sm">
                <p className="font-bold text-left my-2">Lưu ý:</p>
                <li className="my-1">(*) Sử dụng tham vấn tâm lý cho người mẹ đối với trẻ 0-12 tháng tuổi.</li>
                <li className="my-1">Mỗi gói thành viên chỉ áp dụng cho 01 trẻ trong 01 năm.</li>
                <li className="my-1">Ưu đãi 10% khi mua từ 02 gói thành viên trẻ em trở lên.</li>
                <li className="my-1">Nhanh chóng đưa trẻ đến bệnh viện gần nhất trong trường hợp khẩn cấp.</li>
              </div> :
              <div className="text-sm">
                <p className="font-bold text-left my-2">Note:</p>
                <li className="my-1"> (*) Provide psychological consultation for the mother if the child is between 0-12 months old.</li>
                <li className="my-1"> Each membership package is only applicable for 1 child per year.</li>
                <li className="my-1"> 10% discount when purchasing 2 or more children's membership packages.</li>
                <li className="my-1"> In case of an emergency, quickly take your child to the nearest hospital.</li>
              </div>}
          </div>
        </div>
      </div>
      <Contact />
    </>
  );
};

export default Home;
