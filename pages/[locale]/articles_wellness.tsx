import { NextPage } from "next";
import React from "react";
import { useState, useEffect, useCallback } from "react";
import { fetchAPI } from "../../utils/fetch-api";
import Loader from "../../components/ArticlePage/Loader";
import PostList from "../../components/ArticlePage/PostList";
import Contact from "../../components/Contact/Contact";
import Head from "next/head";
import { useRouter } from 'next/router'
import Modal from "../../components/components/Modal";
import { categoryTitles, categoryUrlMap, sectionCategoryMapPage, sectionTitles } from "../../utils/convertString";
import { renderSVGTitle } from "../../components/ArticlePage/renderSVGTitle";
import { BreadcrumbLayout, HeaderArticle, NewspapersLayout, QuestionLayout, SlidebarLayout, TitleVideoLayout, VideoCustomerLayout, VideoLayout } from "../../components/ArticlePage/ArticleLayout";
import { makeStaticProps } from "../../lib/getStatic";
const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }
const detectMob = (): boolean => {
  const toMatch = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i
  ];

  return toMatch.some((toMatchItem) => {
    return navigator.userAgent.match(toMatchItem);
  });
};
interface Meta {
  pagination: {
    start: number;
    limit: number;
    total: number;
  };
}

const Home: NextPage = () => {
  const router = useRouter();
  const locale = (router.query.locale as string) || 'vi';
  const [meta, setMeta] = useState<Meta | undefined>();
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string>("tintuc_wellness");
  const categoryURL = 'tintuc_wellness';
  const [category, setCategory] = useState<string>(categoryURL);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMobile(detectMob());
    }
  }, []);


  useEffect(() => {
    setCategory(categoryURL);
    handleActiveSection(categoryURL);
  }, [categoryURL]);

  const handleActiveSection = (categoryURL: string) => {
    const sectionMap: { [key: string]: string } = {
      tintuc: 'health_posts',
      tintuc_primary_care: 'health_posts',
      tintuc_chronic_diseases: 'health_posts',
      tintuc_wellness: 'health_posts',
      tintuc_pharmacy: 'health_posts',
      tintuc_pediatrics: 'health_posts',
      hoat_dong: 'news',
      tintuc_newspapers: 'news',
      newspapers: 'news',
      video_short: 'video',
      video_events: 'video',
      video_livestream: 'video',
      video_customer: 'video',
      question: 'question',
    };
    setActiveSection(sectionMap[categoryURL] || 'tintuc_wellness');
  };
 
  const handleCategoryChange = (newCategory: string) => {
    setCategory(newCategory);
    const newUrl = categoryUrlMap[newCategory]
    router.push(`/${locale}/${newUrl}/`);
    setLoading(true);
  };

  const fetchData = useCallback(async (start: number, limit: number) => {
    setLoading(true);
    try {
      const path = `/articles`;
      const urlParamsObject = {
        sort: { createdAt: "desc" },
        filters: { category },
        populate: {
          cover: { fields: ["url"] },
          authorsBio: { populate: "*" },
        },
        pagination: { start, limit },
      };
      const options = {};
      const responseData = await fetchAPI(path, urlParamsObject, options);

      setData(prevData => start === 0 ? responseData.data : [...prevData, ...responseData.data]);
      setMeta(responseData.meta);
      setTimeout(() => {
        const anchorElement = document.querySelector(`[data-category='${category}']`);
        if (anchorElement) {
          anchorElement.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
            inline: "start",
          });
        }
      }, 50);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  }, [category]);

  const loadMorePosts = useCallback(async () => {
    if (meta) {
      const nextPosts = meta.pagination.start + meta.pagination.limit;
      fetchData(nextPosts, 10);
    }
  }, [fetchData, meta]);

  const handleShowModalVideoShort = useCallback((urlVideo: string) => {
    setVideoUrl(urlVideo);
    setShowModal(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setShowModal(false);
    setVideoUrl(null);
  }, []);

  useEffect(() => {
    fetchData(0, 100);
  }, [category]);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const toggleSection = useCallback((section: string) => {
    setActiveSection(prevSection => prevSection === section ? "" : section);
    const sectionCategoryMap: { [key: string]: string } = {
      health_posts: 'tintuc',
      news: 'hoat_dong',
      video: 'video_short',
      question: 'question',
    };
    setCategory(sectionCategoryMap[section] || 'tintuc_wellness');
    setIsOpen(false);
    router.push(`/${locale}/${sectionCategoryMapPage[section]}/`);
    setLoading(true);
  }, []);

  const toggleSectionBreadcrumb = useCallback((section: string) => {
    const sectionCategoryMap: { [key: string]: string } = {
      health_posts: 'tintuc',
      news: 'hoat_dong',
      video: 'video_short',
      question: 'question',
    };
    setCategory(sectionCategoryMap[section] || 'tintuc');
  }, []);

  const renderCategoryTitle = (category: string, locale: string) => {
    const title = categoryTitles[category];
    return title ? (locale === "vi" ? title.vi : title.en) : "";
  };

  const renderTitle = (activeSection: string, locale: string) => {
    const title = sectionTitles[activeSection];
    return title ? (locale === "vi" ? title.vi : title.en) : (locale === "vi" ? "Bài viết sức khỏe" : "Healthcare Articles");
  };


  useEffect(() => {
    const scrollToElement = () => {
      const element = document.getElementById("scroll");
      if (element) {
        window.scrollTo({
          top: element.offsetTop - 50,
          behavior: "smooth",
        });
      }
    };

    setTimeout(scrollToElement, 500);
  }, [category, activeSection]);

  if (isLoading) return <Loader />;

  return (
    <>
      <Head>
        <title>{locale === "en" ? "Health Articles" : "Bài viết sức khoẻ"}</title>
        <meta name="description" content={locale === "en" ? "Explore our latest health articles and news." : "Khám phá các bài viết và tin tức sức khoẻ mới nhất của chúng tôi."} />
        <meta name="keywords" content="Health, Articles, News, ECHO MEDI" />
        <meta name="author" content="ECHO MEDI" />
        <meta property="og:title" content={locale === "en" ? "Health Articles" : "Bài viết sức khoẻ"} />
        <meta property="og:description" content={locale === "en" ? "Explore our latest health articles and news." : "Khám phá các bài viết và tin tức sức khoẻ mới nhất của chúng tôi."} />
        <meta property="og:image" content="https://d3e4m6b6rxmux9.cloudfront.net/Old_Mobile_VIE_d75fc17c01.webp" />
        <meta property="og:type" content="website" />
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:site" content="@echomedi" />
        <meta property="twitter:title" content={locale === "en" ? "Health Articles" : "Bài viết sức khoẻ"} />
        <meta property="twitter:description" content={locale === "en" ? "Explore our latest health articles and news." : "Khám phá các bài viết và tin tức sức khoẻ mới nhất của chúng tôi."} />
        <meta property="twitter:image" content="https://d3e4m6b6rxmux9.cloudfront.net/Old_Mobile_VIE_d75fc17c01.webp" />
      </Head>
      <BreadcrumbLayout
        locale={locale}
        category={category}
        activeSection={activeSection}
        toggleSectionBreadcrumb={toggleSectionBreadcrumb}
        renderTitle={renderTitle}
        renderCategoryTitle={renderCategoryTitle}
      />
      <HeaderArticle locale={locale} />
      <div id="scroll"></div>
      <div className="px-3 py-1 relative block md:hidden">
        <div
          className="h-14 text-base font-normal block w-full py-2.5 px-4 bg-[#F9FFFB] rounded-lg"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center gap-2">
            <div className="bg-[#F0FDF4] p-2 rounded-full">
              {renderSVGTitle(activeSection)}
            </div>
            <h1 className="font-bold text-[#156634] text-sm">{renderTitle(activeSection, locale)}</h1>
          </div>

          <svg className="absolute top-1/2 -translate-y-1/2 right-4 z-50" width="16" height="16"
            viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.0002 5.99845L8.00008 9.99862L3.99756 5.99609" stroke="#111827" strokeWidth="1.6"
              strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </div>

        {isOpen && (
          <ul className="absolute left-0 w-[calc(100%-24px)] bg-white z-50" style={{ margin: '0 12px' }}>
            <p className="font-medium text-base text-black ml-6">{locale === "vi" ? "Mục lục" : "Content"}</p>
            <div className={`${activeSection == "health_posts" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
              <div className="flex items-center justify-between" onClick={() => toggleSection('health_posts')}>
                <div className="flex items-center gap-2">
                  <div className="bg-[#F0FDF4] p-1 rounded-full">
                    <svg width="26" height="26" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5.34049 11.0224C5.46607 10.5201 5.97507 10.2147 6.47738 10.3403L11.4774 11.5903C11.9797 11.7159 12.2851 12.2249 12.1595 12.7272C12.0339 13.2295 11.5249 13.5349 11.0226 13.4093L6.02262 12.1593C5.52032 12.0337 5.21491 11.5247 5.34049 11.0224Z" fill="#156634" />
                      <path d="M6.47738 15.3403C5.97507 15.2147 5.46607 15.5201 5.34049 16.0224C5.21491 16.5247 5.52032 17.0337 6.02262 17.1593L11.0226 18.4093C11.5249 18.5349 12.0339 18.2295 12.1595 17.7272C12.2851 17.2249 11.9797 16.7159 11.4774 16.5903L6.47738 15.3403Z" fill="#156634" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M28.4375 6.16629C28.4375 4.33716 26.9809 2.76848 25.0698 2.83505C23.6469 2.88462 21.787 3.03264 20.3561 3.46099C19.1123 3.83335 17.7133 4.56065 16.6227 5.19183C15.6001 5.78366 14.3271 5.81318 13.291 5.26535C12.0467 4.60737 10.4096 3.82744 8.9892 3.45263C7.78819 3.13571 6.26896 2.98348 5.03551 2.90768C3.08771 2.78796 1.5625 4.37311 1.5625 6.24705V20.1793C1.5625 22.0995 3.11723 23.5987 4.96005 23.714C6.1585 23.789 7.50708 23.9331 8.5108 24.198C9.7515 24.5254 11.2858 25.2533 12.5245 25.9137C14.0662 26.7354 15.9338 26.7354 17.4755 25.9137C18.7142 25.2533 20.2485 24.5254 21.4892 24.198C22.4929 23.9331 23.8415 23.789 25.0399 23.714C26.8828 23.5987 28.4375 22.0995 28.4375 20.1793V6.16629ZM25.1351 4.70892C25.9036 4.68215 26.5625 5.31431 26.5625 6.16629V20.1793C26.5625 21.0365 25.8521 21.7845 24.9229 21.8427C23.6978 21.9193 22.1988 22.0715 21.0108 22.385C19.5479 22.7711 17.8534 23.5874 16.5935 24.2591C16.3832 24.3712 16.1631 24.4595 15.9375 24.5239V7.42734C16.5029 7.31417 17.0526 7.1094 17.5619 6.81465C18.625 6.19941 19.8686 5.56416 20.8939 5.25722C22.0803 4.90203 23.7366 4.75763 25.1351 4.70892ZM14.0625 7.46852C13.4937 7.38008 12.9362 7.19873 12.4146 6.92289C11.1936 6.27725 9.71345 5.58293 8.5108 5.26558C7.49554 4.99767 6.12797 4.85336 4.92049 4.77915C4.12804 4.73044 3.4375 5.37557 3.4375 6.24705V20.1793C3.4375 21.0365 4.14788 21.7845 5.0771 21.8427C6.3022 21.9193 7.80115 22.0715 8.9892 22.385C10.4521 22.7711 12.1466 23.5874 13.4065 24.2591C13.6168 24.3712 13.8369 24.4595 14.0625 24.5239V7.46852Z" fill="#156634" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M19.9847 16.4453C18.7587 15.5057 17.084 13.9536 17.084 12.4861C17.084 10.0344 19.3757 9.11911 21.2507 11.013C23.1256 9.11911 25.4173 10.0344 25.4173 12.486C25.4173 13.9536 23.7426 15.5057 22.5166 16.4453C21.9616 16.8707 21.6841 17.0833 21.2507 17.0833C20.8172 17.0833 20.5397 16.8707 19.9847 16.4453ZM23.1257 11.3542C23.2982 11.3542 23.4382 11.4941 23.4382 11.6667V12.1875H23.959C24.1316 12.1875 24.2715 12.3274 24.2715 12.5C24.2715 12.6726 24.1316 12.8125 23.959 12.8125H23.4382V13.3333C23.4382 13.5059 23.2982 13.6458 23.1257 13.6458C22.9531 13.6458 22.8132 13.5059 22.8132 13.3333V12.8125L22.2923 12.8125C22.1197 12.8125 21.9798 12.6726 21.9798 12.5C21.9798 12.3274 22.1197 12.1875 22.2923 12.1875H22.8132V11.6667C22.8132 11.4941 22.9531 11.3542 23.1257 11.3542Z" fill="#156634" />
                    </svg>
                  </div>
                  <h1 className={`${activeSection === "health_posts" ? "font-bold text-[#156634]" : ""} text-sm`}>{locale == "vi" ? "Bài viết sức khỏe" : "Healthcare Articles"}</h1>
                </div>
              </div>
            </div>
            <div className={`${activeSection == "news" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
              <div className="flex items-center justify-between" onClick={() => toggleSection('news')}>
                <div className="flex items-center gap-2">
                  <div className="bg-[#F0FDF4] p-1 rounded-full">
                    <svg width="26" height="26" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M26.25 16.2497H23.75C23.3958 16.2497 23.099 16.1299 22.8594 15.8903C22.6198 15.6507 22.5 15.3538 22.5 14.9997C22.5 14.6455 22.6198 14.3486 22.8594 14.109C23.099 13.8695 23.3958 13.7497 23.75 13.7497H26.25C26.6042 13.7497 26.901 13.8695 27.1406 14.109C27.3802 14.3486 27.5 14.6455 27.5 14.9997C27.5 15.3538 27.3802 15.6507 27.1406 15.8903C26.901 16.1299 26.6042 16.2497 26.25 16.2497ZM20.75 20.9997C20.9583 20.708 21.2292 20.5413 21.5625 20.4997C21.8958 20.458 22.2083 20.5413 22.5 20.7497L24.5 22.2497C24.7917 22.458 24.9583 22.7288 25 23.0622C25.0417 23.3955 24.9583 23.708 24.75 23.9997C24.5417 24.2913 24.2708 24.458 23.9375 24.4997C23.6042 24.5413 23.2917 24.458 23 24.2497L21 22.7497C20.7083 22.5413 20.5417 22.2705 20.5 21.9372C20.4583 21.6038 20.5417 21.2913 20.75 20.9997ZM24.5 7.74967L22.5 9.24967C22.2083 9.45801 21.8958 9.54134 21.5625 9.49967C21.2292 9.45801 20.9583 9.29134 20.75 8.99967C20.5417 8.70801 20.4583 8.39551 20.5 8.06217C20.5417 7.72884 20.7083 7.45801 21 7.24967L23 5.74967C23.2917 5.54134 23.6042 5.45801 23.9375 5.49967C24.2708 5.54134 24.5417 5.70801 24.75 5.99967C24.9583 6.29134 25.0417 6.60384 25 6.93717C24.9583 7.27051 24.7917 7.54134 24.5 7.74967ZM6.25 18.7497H5C4.3125 18.7497 3.72396 18.5049 3.23438 18.0153C2.74479 17.5257 2.5 16.9372 2.5 16.2497V13.7497C2.5 13.0622 2.74479 12.4736 3.23438 11.984C3.72396 11.4945 4.3125 11.2497 5 11.2497H10L14.3438 8.62467C14.7604 8.37467 15.1823 8.37467 15.6094 8.62467C16.0365 8.87467 16.25 9.23926 16.25 9.71842V20.2809C16.25 20.7601 16.0365 21.1247 15.6094 21.3747C15.1823 21.6247 14.7604 21.6247 14.3438 21.3747L10 18.7497H8.75V22.4997C8.75 22.8538 8.63021 23.1507 8.39062 23.3903C8.15104 23.6299 7.85417 23.7497 7.5 23.7497C7.14583 23.7497 6.84896 23.6299 6.60938 23.3903C6.36979 23.1507 6.25 22.8538 6.25 22.4997V18.7497ZM13.75 18.0622V11.9372L10.6875 13.7497H5V16.2497H10.6875L13.75 18.0622ZM17.5 19.1872V10.8122C18.0625 11.3122 18.5156 11.9215 18.8594 12.6403C19.2031 13.359 19.375 14.1455 19.375 14.9997C19.375 15.8538 19.2031 16.6403 18.8594 17.359C18.5156 18.0778 18.0625 18.6872 17.5 19.1872Z" fill="#156634" />
                    </svg>
                  </div>
                  <h1 className={`${activeSection === "news" ? "font-bold text-[#156634]" : ""} text-sm`}>{locale == "vi" ? "Tin tức" : "News"}</h1>
                </div>
              </div>
            </div>
            <div className={`${activeSection == "video" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
              <div className="flex items-center justify-between" onClick={() => toggleSection('video')}>
                <div className="flex items-center gap-2">
                  <div className="bg-[#F0FDF4] p-1 rounded-full">
                    <svg width="26" height="26" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12.8438 20L19.8125 15.5312C20 15.4062 20.0938 15.2292 20.0938 15C20.0938 14.7708 20 14.5938 19.8125 14.4688L12.8438 10C12.6354 9.85417 12.4219 9.84375 12.2031 9.96875C11.9844 10.0938 11.875 10.2812 11.875 10.5312V19.4688C11.875 19.7188 11.9844 19.9062 12.2031 20.0312C12.4219 20.1562 12.6354 20.1458 12.8438 20ZM5 25C4.3125 25 3.72396 24.7552 3.23438 24.2656C2.74479 23.776 2.5 23.1875 2.5 22.5V7.5C2.5 6.8125 2.74479 6.22396 3.23438 5.73438C3.72396 5.24479 4.3125 5 5 5H25C25.6875 5 26.276 5.24479 26.7656 5.73438C27.2552 6.22396 27.5 6.8125 27.5 7.5V22.5C27.5 23.1875 27.2552 23.776 26.7656 24.2656C26.276 24.7552 25.6875 25 25 25H5ZM5 22.5H25V7.5H5V22.5Z" fill="#156634" />
                    </svg>
                  </div>
                  <h1 className={`${activeSection === "video" ? "font-bold text-[#156634]" : ""} text-sm`}>Videos</h1>
                </div>
              </div>
            </div>
            <div className={`${activeSection == "question" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
              <div className="flex items-center justify-between" onClick={() => toggleSection('question')}>
                <div className="flex items-center gap-2">
                  <div className="bg-[#F0FDF4] p-1 rounded-full">
                    <svg width="26" height="26" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14.6875 23.75H14.375C11.4167 23.75 8.90625 22.7188 6.84375 20.6563C4.78125 18.5938 3.75 16.0833 3.75 13.125C3.75 10.1667 4.78125 7.65625 6.84375 5.59375C8.90625 3.53125 11.4167 2.5 14.375 2.5C15.8542 2.5 17.2344 2.77604 18.5156 3.32813C19.7969 3.88021 20.9219 4.64063 21.8906 5.60938C22.8594 6.57813 23.6198 7.70313 24.1719 8.98438C24.724 10.2656 25 11.6458 25 13.125C25 15.9167 24.2135 18.5104 22.6406 20.9063C21.0677 23.3021 19.0833 25.1771 16.6875 26.5313C16.4792 26.6354 16.2708 26.6927 16.0625 26.7031C15.8542 26.7135 15.6667 26.6667 15.5 26.5625C15.3333 26.4583 15.1875 26.3229 15.0625 26.1563C14.9375 25.9896 14.8646 25.7917 14.8438 25.5625L14.6875 23.75ZM17.5 22.9375C18.9792 21.6875 20.1823 20.224 21.1094 18.5469C22.0365 16.8698 22.5 15.0625 22.5 13.125C22.5 10.8542 21.7135 8.93229 20.1406 7.35938C18.5677 5.78646 16.6458 5 14.375 5C12.1042 5 10.1823 5.78646 8.60938 7.35938C7.03646 8.93229 6.25 10.8542 6.25 13.125C6.25 15.3958 7.03646 17.3177 8.60938 18.8906C10.1823 20.4635 12.1042 21.25 14.375 21.25H17.5V22.9375ZM14.3438 19.9688C14.6979 19.9688 15 19.8438 15.25 19.5938C15.5 19.3438 15.625 19.0417 15.625 18.6875C15.625 18.3333 15.5 18.0313 15.25 17.7813C15 17.5313 14.6979 17.4063 14.3438 17.4063C13.9896 17.4063 13.6875 17.5313 13.4375 17.7813C13.1875 18.0313 13.0625 18.3333 13.0625 18.6875C13.0625 19.0417 13.1875 19.3438 13.4375 19.5938C13.6875 19.8438 13.9896 19.9688 14.3438 19.9688ZM11.625 10.4688C11.8542 10.5729 12.0833 10.5781 12.3125 10.4844C12.5417 10.3906 12.7292 10.2396 12.875 10.0313C13.0625 9.78125 13.2812 9.58854 13.5312 9.45313C13.7812 9.31771 14.0625 9.25 14.375 9.25C14.875 9.25 15.2812 9.39063 15.5938 9.67188C15.9062 9.95313 16.0625 10.3125 16.0625 10.75C16.0625 11.0208 15.9844 11.2917 15.8281 11.5625C15.6719 11.8333 15.3958 12.1667 15 12.5625C14.4792 13.0208 14.0938 13.4531 13.8438 13.8594C13.5938 14.2656 13.4688 14.6771 13.4688 15.0938C13.4688 15.3438 13.5573 15.5573 13.7344 15.7344C13.9115 15.9115 14.125 16 14.375 16C14.625 16 14.8333 15.9063 15 15.7188C15.1667 15.5313 15.2917 15.3125 15.375 15.0625C15.4792 14.7083 15.6667 14.3854 15.9375 14.0938C16.2083 13.8021 16.4583 13.5417 16.6875 13.3125C17.125 12.875 17.4531 12.4375 17.6719 12C17.8906 11.5625 18 11.125 18 10.6875C18 9.72917 17.6719 8.95833 17.0156 8.375C16.3594 7.79167 15.4792 7.5 14.375 7.5C13.7083 7.5 13.0938 7.66146 12.5312 7.98438C11.9688 8.30729 11.5104 8.75 11.1562 9.3125C11.0312 9.54167 11.0156 9.76563 11.1094 9.98438C11.2031 10.2031 11.375 10.3646 11.625 10.4688Z" fill="#156634" />
                    </svg>
                  </div>
                  <h1 className={`${activeSection === "question" ? "font-bold text-[#156634]" : ""} text-sm`}>{locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}</h1>
                </div>
              </div>
            </div>
          </ul>
        )}
      </div>

      <section className="block md:hidden">
        <div className="flex gap-2 px-4 overflow-x-scroll whitespace-nowrap hide-scrollbar pb-3">
          {activeSection === 'health_posts' && (
            <>
              <div
                className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
              >
                <div
                  onClick={() => handleCategoryChange("tintuc")}
                  data-category="tintuc"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto ">
                    <div className={`text-sm ${category === "tintuc" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Chăm Sóc Phòng Ngừa" : "Preventive Care"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("tintuc_primary_care")}
                  data-category="tintuc_primary_care"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_primary_care"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "tintuc_primary_care" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Điều Trị Ban Đầu" : "Primary Care"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("tintuc_chronic_diseases")}
                  data-category="tintuc_chronic_diseases"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_chronic_diseases"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "tintuc_chronic_diseases" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Quản Lý Bệnh Mạn Tính" : "Chronic Diseases"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("tintuc_wellness")}
                  data-category="tintuc_wellness"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_wellness"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "tintuc_wellness" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Sức Khỏe Toàn Diện" : "Wellness"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("tintuc_pharmacy")}
                  data-category="tintuc_pharmacy"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_pharmacy"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "tintuc_pharmacy" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Nhà Thuốc" : "Pharmacy"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("tintuc_pediatrics")}
                  data-category="tintuc_pediatrics"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_pediatrics"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "tintuc_pediatrics" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Nhi Khoa" : "Pediatrics"}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
          {activeSection === 'news' && (
            <>
              <div
                className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
              >
                <div
                  onClick={() => handleCategoryChange("hoat_dong")}
                  data-category="hoat_dong"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "hoat_dong"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto ">
                    <div className={`text-sm ${category === "hoat_dong" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale == "vi" ? "Tin tức sự kiện" : "Events"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("newspapers")}
                  data-category="newspapers"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "newspapers"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "newspapers" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale == "vi" ? "Thông tin báo chí" : "Newspapers"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("tintuc_newspapers")}
                  data-category="tintuc_newspapers"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_newspapers"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "tintuc_newspapers" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale == "vi" ? "Khuyến mãi - Ưu đãi" : "Special Offers And Discounts"}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
          {activeSection === 'video' && (
            <>
              <div
                className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
              >
                <div
                  onClick={() => handleCategoryChange("video_short")}
                  data-category="video_short"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_short"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto ">
                    <div className={`text-sm ${category === "video_short" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Câu chuyện \n về chúng tôi" : "Our Stories"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("video_livestream")}
                  data-category="video_livestream"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_livestream"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "video_livestream" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Chuyên đề \n sức khỏe" : "Healthcare Topics"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("video_events")}
                  data-category="video_events"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_events"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "video_events" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Hoạt động\n sự kiện" : "Activities And Events"}
                    </div>
                  </div>
                </div>
                <div
                  onClick={() => handleCategoryChange("video_customer")}
                  data-category="video_customer"
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_customer"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto">
                    <div className={`text-sm ${category === "video_customer" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale === "vi" ? "Phản hồi \n của khách hàng" : "Customer's Feedback"}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
          {activeSection === 'question' && (
            <>
              <div
                className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
              >
                <div
                  onClick={() => handleCategoryChange("question")}
                  className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "question"
                    ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                    : ""
                    }`}
                >
                  <div className="md:w-3/5 lg:w-auto ">
                    <div className={`text-sm ${category === "question" ? "text-[#156634] font-bold" : "text-[#156634]"
                      } xl:text-base`}>
                      {locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </section>
      <div className="block md:hidden" style={{
        background: "white",
        zIndex: 100,
        position: "fixed",
        top: 45,
        width: "100%",
        textAlign: "left",
      }}
      >
        {isVisible && (
          <>
            <section className='block md:hidden'>
              <div className="px-3 py-1 relative">
                <div
                  className="h-14 text-base font-normal block w-full py-2.5 px-4 bg-[#F9FFFB] rounded-lg"
                  onClick={() => setIsOpen(!isOpen)}
                >
                  <div className="flex items-center gap-2">
                    <div className="bg-[#F0FDF4] p-2 rounded-full">
                      {renderSVGTitle(activeSection)}
                    </div>
                    <h1 className="font-bold text-[#156634] text-base">{renderTitle(activeSection, locale)}</h1>
                  </div>
                  <svg className="absolute top-1/2 -translate-y-1/2 right-4 z-50" width="16" height="16"
                    viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.0002 5.99845L8.00008 9.99862L3.99756 5.99609" stroke="#111827" strokeWidth="1.6"
                      strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                {isOpen && (
                  <ul className="absolute left-0 w-[calc(100%-24px)] bg-white z-50" style={{ margin: '0 12px' }}>
                    <p className="font-medium text-base text-black ml-6">{locale === "vi" ? "Mục lục" : "Content"}</p>
                    <div className={`${activeSection == "health_posts" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
                      <div className="flex items-center justify-between" onClick={() => toggleSection('health_posts')}>
                        <div className="flex items-center gap-2">
                          <div className="bg-[#F0FDF4] p-2 rounded-full">
                            <svg className="" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M5.34049 11.0224C5.46607 10.5201 5.97507 10.2147 6.47738 10.3403L11.4774 11.5903C11.9797 11.7159 12.2851 12.2249 12.1595 12.7272C12.0339 13.2295 11.5249 13.5349 11.0226 13.4093L6.02262 12.1593C5.52032 12.0337 5.21491 11.5247 5.34049 11.0224Z" fill="#156634" />
                              <path d="M6.47738 15.3403C5.97507 15.2147 5.46607 15.5201 5.34049 16.0224C5.21491 16.5247 5.52032 17.0337 6.02262 17.1593L11.0226 18.4093C11.5249 18.5349 12.0339 18.2295 12.1595 17.7272C12.2851 17.2249 11.9797 16.7159 11.4774 16.5903L6.47738 15.3403Z" fill="#156634" />
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M28.4375 6.16629C28.4375 4.33716 26.9809 2.76848 25.0698 2.83505C23.6469 2.88462 21.787 3.03264 20.3561 3.46099C19.1123 3.83335 17.7133 4.56065 16.6227 5.19183C15.6001 5.78366 14.3271 5.81318 13.291 5.26535C12.0467 4.60737 10.4096 3.82744 8.9892 3.45263C7.78819 3.13571 6.26896 2.98348 5.03551 2.90768C3.08771 2.78796 1.5625 4.37311 1.5625 6.24705V20.1793C1.5625 22.0995 3.11723 23.5987 4.96005 23.714C6.1585 23.789 7.50708 23.9331 8.5108 24.198C9.7515 24.5254 11.2858 25.2533 12.5245 25.9137C14.0662 26.7354 15.9338 26.7354 17.4755 25.9137C18.7142 25.2533 20.2485 24.5254 21.4892 24.198C22.4929 23.9331 23.8415 23.789 25.0399 23.714C26.8828 23.5987 28.4375 22.0995 28.4375 20.1793V6.16629ZM25.1351 4.70892C25.9036 4.68215 26.5625 5.31431 26.5625 6.16629V20.1793C26.5625 21.0365 25.8521 21.7845 24.9229 21.8427C23.6978 21.9193 22.1988 22.0715 21.0108 22.385C19.5479 22.7711 17.8534 23.5874 16.5935 24.2591C16.3832 24.3712 16.1631 24.4595 15.9375 24.5239V7.42734C16.5029 7.31417 17.0526 7.1094 17.5619 6.81465C18.625 6.19941 19.8686 5.56416 20.8939 5.25722C22.0803 4.90203 23.7366 4.75763 25.1351 4.70892ZM14.0625 7.46852C13.4937 7.38008 12.9362 7.19873 12.4146 6.92289C11.1936 6.27725 9.71345 5.58293 8.5108 5.26558C7.49554 4.99767 6.12797 4.85336 4.92049 4.77915C4.12804 4.73044 3.4375 5.37557 3.4375 6.24705V20.1793C3.4375 21.0365 4.14788 21.7845 5.0771 21.8427C6.3022 21.9193 7.80115 22.0715 8.9892 22.385C10.4521 22.7711 12.1466 23.5874 13.4065 24.2591C13.6168 24.3712 13.8369 24.4595 14.0625 24.5239V7.46852Z" fill="#156634" />
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M19.9847 16.4453C18.7587 15.5057 17.084 13.9536 17.084 12.4861C17.084 10.0344 19.3757 9.11911 21.2507 11.013C23.1256 9.11911 25.4173 10.0344 25.4173 12.486C25.4173 13.9536 23.7426 15.5057 22.5166 16.4453C21.9616 16.8707 21.6841 17.0833 21.2507 17.0833C20.8172 17.0833 20.5397 16.8707 19.9847 16.4453ZM23.1257 11.3542C23.2982 11.3542 23.4382 11.4941 23.4382 11.6667V12.1875H23.959C24.1316 12.1875 24.2715 12.3274 24.2715 12.5C24.2715 12.6726 24.1316 12.8125 23.959 12.8125H23.4382V13.3333C23.4382 13.5059 23.2982 13.6458 23.1257 13.6458C22.9531 13.6458 22.8132 13.5059 22.8132 13.3333V12.8125L22.2923 12.8125C22.1197 12.8125 21.9798 12.6726 21.9798 12.5C21.9798 12.3274 22.1197 12.1875 22.2923 12.1875H22.8132V11.6667C22.8132 11.4941 22.9531 11.3542 23.1257 11.3542Z" fill="#156634" />
                            </svg>
                          </div>
                          <h1 className={`${activeSection === "health_posts" ? "font-bold text-[#156634]" : ""} text-base`}>{locale == "vi" ? "Bài viết sức khỏe" : "Healthcare Articles"}</h1>
                        </div>
                      </div>
                    </div>
                    <div className={`${activeSection == "news" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
                      <div className="flex items-center justify-between" onClick={() => toggleSection('news')}>
                        <div className="flex items-center gap-2">
                          <div className="bg-[#F0FDF4] p-2 rounded-full">
                            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M26.25 16.2497H23.75C23.3958 16.2497 23.099 16.1299 22.8594 15.8903C22.6198 15.6507 22.5 15.3538 22.5 14.9997C22.5 14.6455 22.6198 14.3486 22.8594 14.109C23.099 13.8695 23.3958 13.7497 23.75 13.7497H26.25C26.6042 13.7497 26.901 13.8695 27.1406 14.109C27.3802 14.3486 27.5 14.6455 27.5 14.9997C27.5 15.3538 27.3802 15.6507 27.1406 15.8903C26.901 16.1299 26.6042 16.2497 26.25 16.2497ZM20.75 20.9997C20.9583 20.708 21.2292 20.5413 21.5625 20.4997C21.8958 20.458 22.2083 20.5413 22.5 20.7497L24.5 22.2497C24.7917 22.458 24.9583 22.7288 25 23.0622C25.0417 23.3955 24.9583 23.708 24.75 23.9997C24.5417 24.2913 24.2708 24.458 23.9375 24.4997C23.6042 24.5413 23.2917 24.458 23 24.2497L21 22.7497C20.7083 22.5413 20.5417 22.2705 20.5 21.9372C20.4583 21.6038 20.5417 21.2913 20.75 20.9997ZM24.5 7.74967L22.5 9.24967C22.2083 9.45801 21.8958 9.54134 21.5625 9.49967C21.2292 9.45801 20.9583 9.29134 20.75 8.99967C20.5417 8.70801 20.4583 8.39551 20.5 8.06217C20.5417 7.72884 20.7083 7.45801 21 7.24967L23 5.74967C23.2917 5.54134 23.6042 5.45801 23.9375 5.49967C24.2708 5.54134 24.5417 5.70801 24.75 5.99967C24.9583 6.29134 25.0417 6.60384 25 6.93717C24.9583 7.27051 24.7917 7.54134 24.5 7.74967ZM6.25 18.7497H5C4.3125 18.7497 3.72396 18.5049 3.23438 18.0153C2.74479 17.5257 2.5 16.9372 2.5 16.2497V13.7497C2.5 13.0622 2.74479 12.4736 3.23438 11.984C3.72396 11.4945 4.3125 11.2497 5 11.2497H10L14.3438 8.62467C14.7604 8.37467 15.1823 8.37467 15.6094 8.62467C16.0365 8.87467 16.25 9.23926 16.25 9.71842V20.2809C16.25 20.7601 16.0365 21.1247 15.6094 21.3747C15.1823 21.6247 14.7604 21.6247 14.3438 21.3747L10 18.7497H8.75V22.4997C8.75 22.8538 8.63021 23.1507 8.39062 23.3903C8.15104 23.6299 7.85417 23.7497 7.5 23.7497C7.14583 23.7497 6.84896 23.6299 6.60938 23.3903C6.36979 23.1507 6.25 22.8538 6.25 22.4997V18.7497ZM13.75 18.0622V11.9372L10.6875 13.7497H5V16.2497H10.6875L13.75 18.0622ZM17.5 19.1872V10.8122C18.0625 11.3122 18.5156 11.9215 18.8594 12.6403C19.2031 13.359 19.375 14.1455 19.375 14.9997C19.375 15.8538 19.2031 16.6403 18.8594 17.359C18.5156 18.0778 18.0625 18.6872 17.5 19.1872Z" fill="#156634" />
                            </svg>
                          </div>
                          <h1 className={`${activeSection === "news" ? "font-bold text-[#156634]" : ""} text-base`}>{locale == "vi" ? "Tin tức" : "News"}</h1>
                        </div>
                      </div>
                    </div>
                    <div className={`${activeSection == "video" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
                      <div className="flex items-center justify-between" onClick={() => toggleSection('video')}>
                        <div className="flex items-center gap-2">
                          <div className="bg-[#F0FDF4] p-2 rounded-full">
                            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12.8438 20L19.8125 15.5312C20 15.4062 20.0938 15.2292 20.0938 15C20.0938 14.7708 20 14.5938 19.8125 14.4688L12.8438 10C12.6354 9.85417 12.4219 9.84375 12.2031 9.96875C11.9844 10.0938 11.875 10.2812 11.875 10.5312V19.4688C11.875 19.7188 11.9844 19.9062 12.2031 20.0312C12.4219 20.1562 12.6354 20.1458 12.8438 20ZM5 25C4.3125 25 3.72396 24.7552 3.23438 24.2656C2.74479 23.776 2.5 23.1875 2.5 22.5V7.5C2.5 6.8125 2.74479 6.22396 3.23438 5.73438C3.72396 5.24479 4.3125 5 5 5H25C25.6875 5 26.276 5.24479 26.7656 5.73438C27.2552 6.22396 27.5 6.8125 27.5 7.5V22.5C27.5 23.1875 27.2552 23.776 26.7656 24.2656C26.276 24.7552 25.6875 25 25 25H5ZM5 22.5H25V7.5H5V22.5Z" fill="#156634" />
                            </svg>
                          </div>
                          <h1 className={`${activeSection === "video" ? "font-bold text-[#156634]" : ""} text-base`}>Video</h1>
                        </div>
                      </div>
                    </div>
                    <div className={`${activeSection == "question" ? "bg-[#F9FFFB] p-4" : "p-4"}`}>
                      <div className="flex items-center justify-between" onClick={() => toggleSection('question')}>
                        <div className="flex items-center gap-2">
                          <div className="bg-[#F0FDF4] p-2 rounded-full">
                            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M14.6875 23.75H14.375C11.4167 23.75 8.90625 22.7188 6.84375 20.6563C4.78125 18.5938 3.75 16.0833 3.75 13.125C3.75 10.1667 4.78125 7.65625 6.84375 5.59375C8.90625 3.53125 11.4167 2.5 14.375 2.5C15.8542 2.5 17.2344 2.77604 18.5156 3.32813C19.7969 3.88021 20.9219 4.64063 21.8906 5.60938C22.8594 6.57813 23.6198 7.70313 24.1719 8.98438C24.724 10.2656 25 11.6458 25 13.125C25 15.9167 24.2135 18.5104 22.6406 20.9063C21.0677 23.3021 19.0833 25.1771 16.6875 26.5313C16.4792 26.6354 16.2708 26.6927 16.0625 26.7031C15.8542 26.7135 15.6667 26.6667 15.5 26.5625C15.3333 26.4583 15.1875 26.3229 15.0625 26.1563C14.9375 25.9896 14.8646 25.7917 14.8438 25.5625L14.6875 23.75ZM17.5 22.9375C18.9792 21.6875 20.1823 20.224 21.1094 18.5469C22.0365 16.8698 22.5 15.0625 22.5 13.125C22.5 10.8542 21.7135 8.93229 20.1406 7.35938C18.5677 5.78646 16.6458 5 14.375 5C12.1042 5 10.1823 5.78646 8.60938 7.35938C7.03646 8.93229 6.25 10.8542 6.25 13.125C6.25 15.3958 7.03646 17.3177 8.60938 18.8906C10.1823 20.4635 12.1042 21.25 14.375 21.25H17.5V22.9375ZM14.3438 19.9688C14.6979 19.9688 15 19.8438 15.25 19.5938C15.5 19.3438 15.625 19.0417 15.625 18.6875C15.625 18.3333 15.5 18.0313 15.25 17.7813C15 17.5313 14.6979 17.4063 14.3438 17.4063C13.9896 17.4063 13.6875 17.5313 13.4375 17.7813C13.1875 18.0313 13.0625 18.3333 13.0625 18.6875C13.0625 19.0417 13.1875 19.3438 13.4375 19.5938C13.6875 19.8438 13.9896 19.9688 14.3438 19.9688ZM11.625 10.4688C11.8542 10.5729 12.0833 10.5781 12.3125 10.4844C12.5417 10.3906 12.7292 10.2396 12.875 10.0313C13.0625 9.78125 13.2812 9.58854 13.5312 9.45313C13.7812 9.31771 14.0625 9.25 14.375 9.25C14.875 9.25 15.2812 9.39063 15.5938 9.67188C15.9062 9.95313 16.0625 10.3125 16.0625 10.75C16.0625 11.0208 15.9844 11.2917 15.8281 11.5625C15.6719 11.8333 15.3958 12.1667 15 12.5625C14.4792 13.0208 14.0938 13.4531 13.8438 13.8594C13.5938 14.2656 13.4688 14.6771 13.4688 15.0938C13.4688 15.3438 13.5573 15.5573 13.7344 15.7344C13.9115 15.9115 14.125 16 14.375 16C14.625 16 14.8333 15.9063 15 15.7188C15.1667 15.5313 15.2917 15.3125 15.375 15.0625C15.4792 14.7083 15.6667 14.3854 15.9375 14.0938C16.2083 13.8021 16.4583 13.5417 16.6875 13.3125C17.125 12.875 17.4531 12.4375 17.6719 12C17.8906 11.5625 18 11.125 18 10.6875C18 9.72917 17.6719 8.95833 17.0156 8.375C16.3594 7.79167 15.4792 7.5 14.375 7.5C13.7083 7.5 13.0938 7.66146 12.5312 7.98438C11.9688 8.30729 11.5104 8.75 11.1562 9.3125C11.0312 9.54167 11.0156 9.76563 11.1094 9.98438C11.2031 10.2031 11.375 10.3646 11.625 10.4688Z" fill="#156634" />
                            </svg>
                          </div>
                          <h1 className={`${activeSection === "question" ? "font-bold text-[#156634]" : ""} text-base`}>{locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}</h1>
                        </div>
                      </div>
                    </div>
                  </ul>
                )}
              </div>
              <div className="flex gap-2 px-4 overflow-x-scroll whitespace-nowrap hide-scrollbar pb-3">
                {activeSection === 'health_posts' && (
                  <>
                    <div
                      className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
                    >
                      <div
                        onClick={() => handleCategoryChange("tintuc")}
                        data-category="tintuc"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto ">
                          <div className={`text-sm ${category === "tintuc" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Chăm Sóc Phòng Ngừa" : "Preventive Care"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("tintuc_primary_care")}
                        data-category="tintuc_primary_care"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_primary_care"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "tintuc_primary_care" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Điều Trị Ban Đầu" : "Primary Care"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("tintuc_chronic_diseases")}
                        data-category="tintuc_chronic_diseases"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_chronic_diseases"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "tintuc_chronic_diseases" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Quản Lý Bệnh Mạn Tính" : "Chronic Diseases"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("tintuc_wellness")}
                        data-category="tintuc_wellness"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_wellness"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "tintuc_wellness" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Sức Khỏe Toàn Diện" : "Wellness"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("tintuc_pharmacy")}
                        data-category="tintuc_pharmacy"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_pharmacy"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "tintuc_pharmacy" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Nhà Thuốc" : "Pharmacy"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("tintuc_pediatrics")}
                        data-category="tintuc_pediatrics"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_pediatrics"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "tintuc_pediatrics" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Nhi Khoa" : "Pediatrics"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {activeSection === 'news' && (
                  <>
                    <div
                      className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
                    >
                      <div
                        onClick={() => handleCategoryChange("hoat_dong")}
                        data-category="hoat_dong"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "hoat_dong"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto ">
                          <div className={`text-sm ${category === "hoat_dong" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale == "vi" ? "Tin tức sự kiện" : "Events"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("newspapers")}
                        data-category="newspapers"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "newspapers"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "newspapers" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale == "vi" ? "Thông tin báo chí" : "Newspapers"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("tintuc_newspapers")}
                        data-category="tintuc_newspapers"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "tintuc_newspapers"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "tintuc_newspapers" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale == "vi" ? "Khuyến mãi - Ưu đãi" : "Special Offers And Discounts"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {activeSection === 'video' && (
                  <>
                    <div
                      className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
                    >
                      <div
                        onClick={() => handleCategoryChange("video_short")}
                        data-category="video_short"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_short"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto ">
                          <div className={`text-sm ${category === "video_short" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Câu chuyện \n về chúng tôi" : "Our Stories"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("video_livestream")}
                        data-category="video_livestream"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_livestream"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "video_livestream" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Chuyên đề \n sức khỏe" : "Healthcare Topics"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("video_events")}
                        data-category="video_events"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_events"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "video_events" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Hoạt động\n sự kiện" : "Activities And Events"}
                          </div>
                        </div>
                      </div>
                      <div
                        onClick={() => handleCategoryChange("video_customer")}
                        data-category="video_customer"
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "video_customer"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto">
                          <div className={`text-sm ${category === "video_customer" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale === "vi" ? "Phản hồi \n của khách hàng" : "Customer's Feedback"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {activeSection === 'question' && (
                  <>
                    <div
                      className="mb-15 border-b-2 flex justify-center md:flex-nowrap md:items-center lg:gap-6 xl:mb-21 xl:gap-6"
                    >
                      <div
                        onClick={() => handleCategoryChange("question")}
                        className={`relative flex w-full cursor-pointer items-center gap-4 border-b border-stroke px-6 py-[12px] last:border-0 dark:border-strokedark md:w-auto md:border-0 xl:px-13.5 xl:py-[12px] ${category === "question"
                          ? "active before:absolute before:bottom-0 before:left-0 before:h-1 before:w-full before:rounded-tl-[4px] before:rounded-tr-[4px] before:bg-[#14813d]"
                          : ""
                          }`}
                      >
                        <div className="md:w-3/5 lg:w-auto ">
                          <div className={`text-sm ${category === "question" ? "text-[#156634] font-bold" : "text-[#156634]"
                            } xl:text-base`}>
                            {locale == "vi" ? "Câu hỏi thường gặp" : "Frequently asked questions"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </section>
          </>
        )}
      </div>

      <section className="relative my-8">
        <div className="w-full max-w-screen-2xl mx-auto px-4 md:px-8">
          <h2 className="hidden md:block text-center mb-8 md:text-left font-bold md:text-[26px] text-2xl text-[#156634] uppercase">{renderTitle(activeSection, locale)}{activeSection !== "question" && activeSection !== "video" ? ` - ${renderCategoryTitle(category, locale)}` : ''}
          </h2>
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-12 md:col-span-9">
              {activeSection === 'video' && (
                <div className="tabs hidden md:block">
                  <TitleVideoLayout
                    locale={locale}
                    category={category}
                    handleCategoryChange={handleCategoryChange}
                  />
                </div>
              )}

              {category === "question" && (
                <>
                  <QuestionLayout locale={locale} />
                </>
              )}
              {category === "newspapers" && (
                <>
                  <NewspapersLayout locale={locale} />
                </>
              )}
              {category === "video_customer" && (
                <>
                  <VideoCustomerLayout locale={locale} handleShowModalVideoShort={handleShowModalVideoShort} />
                  <Modal
                    showCloseButton
                    visibleModal={showModal}
                    wrapperClassName="!w-[370px] md:!w-[380px]"
                    contentClassName="!min-h-[0]" onClose={handleCloseModal}
                  >
                    <p className="text-2xl text-center font-bold text-[#156634] my-2">{locale === "vi" ? "Phản hồi của khách hàng" : "Customer's Feedback"}</p>
                    <div className="relative w-full pt-[107.78%] h-0 overflow-hidden">
                      {videoUrl && (
                        <iframe
                          src={`https://www.youtube.com/embed/${videoUrl}`}
                          title="Video"
                          className="absolute top-0 left-0 w-full h-full rounded-2xl pb-2" frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                          allowFullScreen
                        ></iframe>
                      )}
                    </div>
                  </Modal>
                </>
              )}
              {category === "video_short" && (
                <>
                  <VideoLayout locale={locale} handleShowModalVideoShort={handleShowModalVideoShort} />
                  <Modal
                    showCloseButton
                    visibleModal={showModal}
                    wrapperClassName="!w-[370px] md:!w-[380px]"
                    contentClassName="!min-h-[0]" onClose={handleCloseModal}
                  >
                    <p className="text-2xl text-center font-bold text-[#156634] my-2">{locale === "vi" ? "Câu chuyện về chúng tôi" : "Our Stories"}</p>
                    <div className="relative w-full pt-[107.78%] h-0 overflow-hidden">
                      {videoUrl && (
                        <iframe
                          src={`https://www.youtube.com/embed/${videoUrl}`}
                          title="Video"
                          className="absolute top-0 left-0 w-full h-full rounded-2xl pb-2" frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                          allowFullScreen
                        ></iframe>
                      )}
                    </div>
                  </Modal>
                </>
              )}
              <PostList data={data} />
            </div>
            <SlidebarLayout
              locale={locale}
              category={category}
              activeSection={activeSection}
              toggleSection={toggleSection}
              handleCategoryChange={handleCategoryChange}
            />
          </div>
        </div>
      </section >

      <Contact />
    </>
  );
}


export default Home;