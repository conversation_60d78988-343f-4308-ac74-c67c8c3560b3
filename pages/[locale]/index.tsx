import React from "react";
import type { NextPage } from "next";
import Head from "next/head";
import Slider from "../../components/Slider/Slider";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import LinkComponent from "../../components/Link";
import Modal from "../../components/components/Modal";
import Button from "../../components/components/Button";
import { useState } from "react";
import Partner from "../../components/InstagramGallery/Partner";
import { makeStaticProps } from '../../lib/getStatic';
import Image from 'next/image'
import BlogMainSeesion from "../../components/BlogMain/BlogMainSeesion";
import FAQ from "../../components/FAQ";
import SliderMembershipMainPC from "../../components/Slider/SliderMembershipMainPC";
import FeaturesTab from "../../components/FeaturesTab";
import SliderPharmacy from "../../components/Slider/SliderPharmacy";
import BlogTheMedia from "../../components/BlogTheMedia/BlogTheMedia";
import BlogMainSlide from "../../components/Slider/SliderBlogMain";
import BlogMediaMainSlide from "../../components/Slider/SliderBlogMediaMain";
import MainSlide from "../../components/Slider/SliderMain";
import BookingMobi from "../../components/BookingMobi/BookingMobi";
import FamilySeaction from "../../components/FamilySeaction/FamilySeaction";
import SliderOurPartners from "../../components/Slider/SliderOurPartners";
import OutService from "../../components/Slider/OutService";
import SliderMembershipMainMB from "../../components/Slider/SliderMembershipMainMB";
import { shimmer, toBase64 } from "../../lib/ui";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

const tranlsate = (term: string, locale: string) => {
  if (locale === "en") {
    switch (term) {
      case "what_we_offer":
        return "What We Offer";
      case "become_a_member":
        return "Become A Member";
      case "gifting":
        return "Gifting";
      case "book_now":
        return "Book Now";
    }
  } else {
    switch (term) {
      case "what_we_offer":
        return "Đồng hành cùng thành viên";
      case "become_a_member":
        return "Đăng ký thành viên";
      case "gifting":
        return "Quà tặng";
      case "book_now":
        return "Đặt lịch hẹn";
    }
  }
}


const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showModal, setShowModal] = useState(false);
  return (
    <>
      <Head>
        <title>ECHO MEDI - Hệ thống y tế toàn diện cho bạn và gia đình</title>
        <meta name="description" content="ECHO MEDI nâng cấp mô hình bác sĩ gia đình, tập trung chăm sóc sức khỏe chủ động, nhằm can thiệp sớm và duy trì sức khỏe tổng thể thay vì chỉ chú trọng vào điều trị khi bệnh lý phát sinh như các tiếp cận phổ biến ở các cơ sở y tế. Phương pháp này giúp khách hàng tiết kiệm thời gian, chi phí, tránh lạm dụng thuốc và các can thiệp không cần thiết, góp phần mang lại sự tích cực mới trong chăm sóc sức khỏe tại Việt Nam." />
        <meta name="keywords" content="bác sĩ gia đình, y tế, sức khỏe, chăm sóc sức khỏe, khám bệnh, bệnh viện, Echo Medi" />
        <meta property="og:title" content="Hệ thống y tế toàn diện cho bạn và gia đình" />
        <meta property="og:description" content="ECHO MEDI nâng cấp mô hình bác sĩ gia đình, tập trung chăm sóc sức khỏe chủ động, nhằm can thiệp sớm và duy trì sức khỏe tổng thể thay vì chỉ chú trọng vào điều trị khi bệnh lý phát sinh như các tiếp cận phổ biến ở các cơ sở y tế. Phương pháp này giúp khách hàng tiết kiệm thời gian, chi phí, tránh lạm dụng thuốc và các can thiệp không cần thiết, góp phần mang lại sự tích cực mới trong chăm sóc sức khỏe tại Việt Nam." />
        <meta property="og:image" content="https://res.cloudinary.com/dvyny6hes/image/upload/v1740474394/bzfubj9ldbwlk4plyupb.png" />
      </Head>
      <section className="relative">
        <div className="mx-auto">
          <div className="flex justify-between items-center flex-col lg:flex-row gap-8">
            <div className="w-full relative">
              <Image
                src="/banner/banner-2025.webp"
                alt="Banner"
                width={1920}
                height={300}
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(1920, 300))}`}
                className="object-center"
                layout="responsive"
                priority
              />
            </div>
            <div className="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 2xl:w-2/3 absolute md:px-16 px-4">
              <h2 className="text-left font-bold md:text-[28px] text-lg text-[#156634] uppercase pb-2 hidden md:block">
                {locale === "vi" ? "HỆ THỐNG PHÒNG KHÁM & NHÀ THUỐC" : "A COMPREHENSIVE HEALTHCARE SYSTEM"}
              </h2>
              <h2 className="text-left font-bold md:text-[28px] text-lg text-[#156634] uppercase block md:hidden mt-8 whitespace-pre-line">
                {locale === "vi" ? "HỆ THỐNG PHÒNG KHÁM\n & NHÀ THUỐC" : "A COMPREHENSIVE\n HEALTHCARE SYSTEM"}
              </h2>
              <p className="text-base text-justify hidden md:block mr-10">
                {locale === "en" ? "ECHO MEDI enhances the traditional family doctor model by emphasizing proactive health care. The focus is on early intervention and maintaining overall health instead of merely treating diseases as they arise, which is the common approach in other medical facilities." : "ECHO MEDI nâng cấp mô hình bác sĩ gia đình, tập trung chăm sóc sức khỏe chủ động, nhằm can thiệp sớm và duy trì sức khỏe tổng thể thay vì chỉ chú trọng vào điều trị khi bệnh lý phát sinh như các tiếp cận phổ biến ở các cơ sở y tế. Phương pháp này giúp khách hàng tiết kiệm thời gian, chi phí, tránh lạm dụng thuốc và các can thiệp không cần thiết, góp phần mang lại sự tích cực mới trong chăm sóc sức khỏe tại Việt Nam."}
              </p>
            </div>
          </div>
          <p className="text-sm my-6 text-justify px-4 block md:hidden">
            {locale === "en" ? "ECHO MEDI enhances the traditional family doctor model by emphasizing proactive health care. The focus is on early intervention and maintaining overall health instead of merely treating diseases as they arise, which is the common approach in other medical facilities." : "ECHO MEDI nâng cấp mô hình bác sĩ gia đình, tập trung chăm sóc sức khỏe chủ động, nhằm can thiệp sớm và duy trì sức khỏe tổng thể thay vì chỉ chú trọng vào điều trị khi bệnh lý phát sinh như các tiếp cận phổ biến ở các cơ sở y tế. Phương pháp này giúp khách hàng tiết kiệm thời gian, chi phí, tránh lạm dụng thuốc và các can thiệp không cần thiết, góp phần mang lại sự tích cực mới trong chăm sóc sức khỏe tại Việt Nam."}
          </p>
        </div>
      </section>
      <div className="bg-[#FBFFFD]">
        <Slider />
      </div>
      <div className="md:hidden block">
        <BookingMobi />
      </div>
      <div className="md:block hidden bg-white">
        <section className="mx-auto items-center justify-center flex relative ">
          <div className="absolute inset-1 z-0 -bottom-5 bg-white">
            <Image loading='lazy' className="w-full h-full" width={557} height={384} alt="background" src="/iconbg/BG.png" />
          </div>
          <div className="flex items-center justify-between flex-col md:flex-row gap-10 md:px-16 px-4 max-w-screen-2xl pb-8 relative z-10">
            <div className="md:w-3/5">
              <h2 className="text-center mb-6 md:text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase">
                {locale === "vi" ? "Đội ngũ y tế" : "Medical Team"}
              </h2>
              {/* <p className="text-base text-black text-justify md:pr-8">
                {locale === "vi" ? "Đội ngũ bao gồm các bác sĩ, dược sĩ, điều dưỡng, chuyên gia dinh dưỡng và chuyên viên tâm lý được đào tạo bài bản tại các cơ sở trong và ngoài nước như Đại học Y Dược TP. HCM, Đại học Y khoa Phạm Ngọc Thạch, Đại học Y Dược Cần Thơ, Barca Innovation Hub, v.v. Chúng tôi luôn đồng hành cùng khách hàng trong suốt hành trình chăm sóc sức khỏe, lắng nghe và thấu hiểu để mang đến các giải pháp tối ưu, giúp tiết kiệm thời gian, chi phí, đồng thời hạn chế việc lạm dụng thuốc và thực hiện các xét nghiệm không cần thiết." : "Our team comprises highly trained professionals, including doctors, pharmacists, nurses, nutritionists, and psychological counselors from esteemed domestic and international institutions, such as Ho Chi Minh City University of Medicine and Pharmacy, Pham Ngoc Thach University of Medicine, Can Tho University of Medicine and Pharmacy, and the Barca Innovation Hub. We are dedicated to accompanying clients throughout their healthcare journey by actively listening to their needs and delivering tailored, optimal solutions that save time and costs, while minimizing unnecessary invasive procedures and preventing medication misuse."}
              </p> */}
              <p className="text-base text-black text-justify md:pr-8">
                {locale === "vi" ? "Đội ngũ được đào tạo bài bản tại các cơ sở trong và ngoài nước như Đại học Y Dược TP. HCM, Đại học Y khoa Phạm Ngọc Thạch, Đại học Y Dược Cần Thơ, Barca Innovation Hub, v.v. Chúng tôi luôn đồng hành cùng khách hàng trong suốt hành trình chăm sóc sức khỏe, lắng nghe và thấu hiểu để mang đến các giải pháp tối ưu, giúp tiết kiệm thời gian, chi phí, đồng thời hạn chế việc lạm dụng thuốc và thực hiện các xét nghiệm không cần thiết." : "Our team comprises highly trained professionals from esteemed domestic and international institutions, such as Ho Chi Minh City University of Medicine and Pharmacy, Pham Ngoc Thach University of Medicine, Can Tho University of Medicine and Pharmacy, and the Barca Innovation Hub. We are dedicated to accompanying clients throughout their healthcare journey by actively listening to their needs and delivering tailored, optimal solutions that save time and costs, while minimizing unnecessary invasive procedures and preventing medication misuse."}
              </p>
            </div>
            <div className="md:w-2/5">
              <img
                className="rounded-lg mt-4 md:mt-0"
                src="/iconbg/trai-tim.png"
                alt="image"
                loading="lazy"
                width="557"
                height="384"
              />
            </div>
          </div>
        </section>
      </div>
      <div className="bg-[#FBFFFB] px-4 block md:hidden">
        <MainSlide />
      </div>
      <div className="mx-auto max-w-screen-2xl mt-16">
        <div>
          <div className="md:mx-16 rounded-3xl md:block hidden">
            <OutService />
            {/* <FeaturesTab /> */}
          </div>
          {/* <div className="md:mx-16 mx-4">
            <FamilySeaction />
          </div> */}
          <div className="block md:hidden my-16">
            <div className="flex items-center justify-between flex-col md:flex-row gap-4 md:mx-16 mx-4">
              <SliderPharmacy />
              <div className="md:w-1/2 items-center justify-center flex">
              </div>
              <div className="">
                <h2 className="text-center md:text-left font-bold md:text-[28px] text-2xl text-[#156634] uppercase mb-2">{locale == "vi" ? "Nhà thuốc" : "Pharmacy"}</h2>
                <p className="text-base text-black text-justify">{locale == "vi" ? "Chúng tôi cung cấp sản phẩm phù hợp với nhu cầu và tình trạng sức khỏe với từng khách hàng. Thông qua tư vấn trực tiếp, các dược sĩ đảm bảo thuốc an toàn, hiệu quả, hợp lý và giúp rút ngắn quá trình điều trị với chi phí hợp lý." : "We offer products tailored to meet each client's health needs. Through direct consultations, pharmacists ensure that the medications are safe, effective, and affordable, helping to expedite the treatment process."}</p>
                <div className="w-full col-span-2 md:col-span-1 row-span-2 flex sm:justify-start justify-center h-auto">
                  <div className="py-2 text-center inline text-base border border-[#14813d] hover:bg-[#14813d] hover:text-white font-medium rounded-full text-[#156634] px-5 mt-4">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/store">{locale == "vi" ? "Xem thêm" : "Learn More"}</LinkComponent>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="hidden md:block my-16">
            <div className="flex items-center justify-between flex-col md:flex-row gap-10 md:mx-16 mx-4">
              <div className="">
                <h2 className="text-start font-bold md:text-[28px] text-2xl text-[#156634] mb-6 uppercase">{locale == "vi" ? "Nhà thuốc" : "Pharmacy"}</h2>
                <p className="text-base text-black text-justify">{locale == "vi" ? "Chúng tôi cung cấp sản phẩm phù hợp với nhu cầu và tình trạng sức khỏe với từng khách hàng. Thông qua tư vấn trực tiếp, các dược sĩ đảm bảo thuốc an toàn, hiệu quả, hợp lý và giúp rút ngắn quá trình điều trị với chi phí hợp lý." : "We offer products tailored to meet each client's health needs. Through direct consultations, pharmacists ensure that the medications are safe, effective, and affordable, helping to expedite the treatment process."}</p>
                <div className="w-full col-span-2 md:col-span-1 row-span-2 flex sm:justify-start justify-center h-auto">
                  <LinkComponent href={"/store"} locale={locale} skipLocaleHandling={false}>
                    <div className="flex items-center justify-start py-6 text-sm font-medium gap-1">
                      {locale == "vi" ? "Xem thêm" : "Learn More"}
                      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7.88675 2.91685C8.03978 2.9175 8.18643 2.97825 8.29508 3.08602L10.9726 5.76352C11.3003 6.09165 11.4844 6.53644 11.4844 7.00019C11.4844 7.46394 11.3003 7.90873 10.9726 8.23685L8.29508 10.9144C8.18579 11.023 8.03794 11.084 7.88383 11.084C7.72972 11.084 7.58188 11.023 7.47258 10.9144C7.41791 10.8601 7.37451 10.7956 7.34489 10.7245C7.31528 10.6534 7.30003 10.5772 7.30003 10.5002C7.30003 10.4232 7.31528 10.3469 7.34489 10.2759C7.37451 10.2048 7.41791 10.1402 7.47258 10.086L10.1501 7.41435C10.2048 7.36013 10.2482 7.29561 10.2778 7.22452C10.3074 7.15344 10.3226 7.0772 10.3226 7.00019C10.3226 6.92318 10.3074 6.84694 10.2778 6.77585C10.2482 6.70477 10.2048 6.64025 10.1501 6.58602L7.47258 3.91435C7.41791 3.86012 7.37451 3.79561 7.3449 3.72452C7.31528 3.65344 7.30003 3.57719 7.30003 3.50019C7.30003 3.42318 7.31528 3.34694 7.3449 3.27585C7.37451 3.20477 7.41791 3.14025 7.47258 3.08602C7.52709 3.03196 7.59173 2.98918 7.6628 2.96015C7.73387 2.93112 7.80998 2.91641 7.88675 2.91685Z" fill="#322F35" />
                        <path d="M3.80194 2.91685C3.95497 2.9175 4.10162 2.97825 4.21027 3.08602L7.71026 6.58602C7.76494 6.64025 7.80834 6.70477 7.83795 6.77585C7.86757 6.84694 7.88281 6.92318 7.88281 7.00019C7.88281 7.0772 7.86757 7.15344 7.83795 7.22452C7.80834 7.29561 7.76494 7.36013 7.71026 7.41435L4.21027 10.9144C4.10097 11.023 3.95313 11.084 3.79902 11.084C3.64491 11.084 3.49707 11.023 3.38777 10.9144C3.3331 10.8601 3.2897 10.7956 3.26008 10.7245C3.23047 10.6534 3.21522 10.5772 3.21522 10.5002C3.21522 10.4232 3.23047 10.3469 3.26008 10.2759C3.2897 10.2048 3.3331 10.1402 3.38777 10.086L6.4736 7.00019L3.38777 3.91435C3.3331 3.86012 3.2897 3.79561 3.26009 3.72452C3.23047 3.65344 3.21522 3.57719 3.21522 3.50019C3.21522 3.42318 3.23047 3.34694 3.26009 3.27585C3.2897 3.20477 3.3331 3.14025 3.38777 3.08602C3.44228 3.03196 3.50692 2.98918 3.57799 2.96015C3.64906 2.93112 3.72517 2.91641 3.80194 2.91685Z" fill="#322F35" />
                      </svg>
                    </div>
                  </LinkComponent>
                </div>
              </div>
              <div className="md:w-1/2 items-center justify-center flex">
                <SliderPharmacy />
              </div>
            </div>
          </div>
                    <div className="hidden md:block my-16">
            <div className="flex items-center justify-between flex-col md:flex-row gap-10 md:mx-16 mx-4">
              <div className="">
                <h2 className="text-start font-bold md:text-[28px] text-2xl text-[#156634] mb-6 uppercase">{locale == "vi" ? "Nhà thuốc" : "Pharmacy"}</h2>
                <p className="text-base text-black text-justify">{locale == "vi" ? "Chúng tôi cung cấp sản phẩm phù hợp với nhu cầu và tình trạng sức khỏe với từng khách hàng. Thông qua tư vấn trực tiếp, các dược sĩ đảm bảo thuốc an toàn, hiệu quả, hợp lý và giúp rút ngắn quá trình điều trị với chi phí hợp lý." : "We offer products tailored to meet each client's health needs. Through direct consultations, pharmacists ensure that the medications are safe, effective, and affordable, helping to expedite the treatment process."}</p>
                <div className="w-full col-span-2 md:col-span-1 row-span-2 flex sm:justify-start justify-center h-auto">
                  <LinkComponent href={"/store"} locale={locale} skipLocaleHandling={false}>
                    <div className="flex items-center justify-start py-6 text-sm font-medium gap-1">
                      {locale == "vi" ? "Xem thêm" : "Learn More"}
                      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7.88675 2.91685C8.03978 2.9175 8.18643 2.97825 8.29508 3.08602L10.9726 5.76352C11.3003 6.09165 11.4844 6.53644 11.4844 7.00019C11.4844 7.46394 11.3003 7.90873 10.9726 8.23685L8.29508 10.9144C8.18579 11.023 8.03794 11.084 7.88383 11.084C7.72972 11.084 7.58188 11.023 7.47258 10.9144C7.41791 10.8601 7.37451 10.7956 7.34489 10.7245C7.31528 10.6534 7.30003 10.5772 7.30003 10.5002C7.30003 10.4232 7.31528 10.3469 7.34489 10.2759C7.37451 10.2048 7.41791 10.1402 7.47258 10.086L10.1501 7.41435C10.2048 7.36013 10.2482 7.29561 10.2778 7.22452C10.3074 7.15344 10.3226 7.0772 10.3226 7.00019C10.3226 6.92318 10.3074 6.84694 10.2778 6.77585C10.2482 6.70477 10.2048 6.64025 10.1501 6.58602L7.47258 3.91435C7.41791 3.86012 7.37451 3.79561 7.3449 3.72452C7.31528 3.65344 7.30003 3.57719 7.30003 3.50019C7.30003 3.42318 7.31528 3.34694 7.3449 3.27585C7.37451 3.20477 7.41791 3.14025 7.47258 3.08602C7.52709 3.03196 7.59173 2.98918 7.6628 2.96015C7.73387 2.93112 7.80998 2.91641 7.88675 2.91685Z" fill="#322F35" />
                        <path d="M3.80194 2.91685C3.95497 2.9175 4.10162 2.97825 4.21027 3.08602L7.71026 6.58602C7.76494 6.64025 7.80834 6.70477 7.83795 6.77585C7.86757 6.84694 7.88281 6.92318 7.88281 7.00019C7.88281 7.0772 7.86757 7.15344 7.83795 7.22452C7.80834 7.29561 7.76494 7.36013 7.71026 7.41435L4.21027 10.9144C4.10097 11.023 3.95313 11.084 3.79902 11.084C3.64491 11.084 3.49707 11.023 3.38777 10.9144C3.3331 10.8601 3.2897 10.7956 3.26008 10.7245C3.23047 10.6534 3.21522 10.5772 3.21522 10.5002C3.21522 10.4232 3.23047 10.3469 3.26008 10.2759C3.2897 10.2048 3.3331 10.1402 3.38777 10.086L6.4736 7.00019L3.38777 3.91435C3.3331 3.86012 3.2897 3.79561 3.26009 3.72452C3.23047 3.65344 3.21522 3.57719 3.21522 3.50019C3.21522 3.42318 3.23047 3.34694 3.26009 3.27585C3.2897 3.20477 3.3331 3.14025 3.38777 3.08602C3.44228 3.03196 3.50692 2.98918 3.57799 2.96015C3.64906 2.93112 3.72517 2.91641 3.80194 2.91685Z" fill="#322F35" />
                      </svg>
                    </div>
                  </LinkComponent>
                </div>
              </div>
              <div className="md:w-1/2 items-center justify-center flex">
                <SliderPharmacy />
              </div>
            </div>
          </div>
          {/* <section className="block md:hidden">
            <div className="flex items-center justify-between flex-col md:flex-row md:rounded-[52px] px-4">
              <SliderMembershipMainMB />
              <div className="md:w-1/2 md:p-8 p-4">
                <p className="text-center md:text-left font-bold md:text-[28px] text-2xl text-[#156634] mb-6 uppercase whitespace-pre-line">
                  {tranlsate("what_we_offer", locale)}
                </p>
                <div className="flex items-center gap-4 justify-center">
                  <div className="grid place-items-center bg-[#FAFFFC] rounded-lg px-6 py-4">
                    <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="11.5" cy="12.166" r="11.5" fill="#F0FDF4" />
                      <g clip-path="url(#clip0_7608_4771)">
                        <path d="M18.5 10.25C18.4244 10.6736 18.2561 11.0752 18.0071 11.426L12.8837 18.321C12.7212 18.5309 12.5132 18.701 12.2753 18.8186C12.0374 18.9362 11.7759 18.9982 11.5105 18.9998C11.2452 19.0014 10.9829 18.9427 10.7436 18.828C10.5043 18.7134 10.2942 18.5458 10.1292 18.3379L4.98067 11.3C4.76196 10.9831 4.60757 10.6264 4.52625 10.25H8.18258L10.9552 17.4594C10.9974 17.5697 11.072 17.6646 11.1693 17.7316C11.2666 17.7985 11.3819 17.8343 11.5 17.8343C11.6181 17.8343 11.7334 17.7985 11.8307 17.7316C11.928 17.6646 12.0026 17.5697 12.0448 17.4594L14.8174 10.25H18.5ZM14.825 9.08333H18.4749C18.3862 8.68866 18.2159 8.3169 17.975 7.99192L16.4828 5.97708C16.2667 5.67507 15.9818 5.4289 15.6516 5.25898C15.3214 5.08906 14.9554 5.00028 14.5841 5H13.3007L14.825 9.08333ZM10.9797 5L9.42858 9.08333H13.5772L12.0571 5H10.9797ZM8.18083 9.08333L9.73133 5H8.37683C8.00862 4.99964 7.64558 5.08664 7.31753 5.25386C6.98948 5.42108 6.70579 5.66374 6.48975 5.96192L5.04717 7.85308C4.77114 8.21477 4.58373 8.63612 4.5 9.08333H8.18083ZM13.5673 10.25H9.43267L11.5 15.6248L13.5673 10.25Z" fill="#14A54A" />
                      </g>
                      <defs>
                        <clipPath id="clip0_7608_4771">
                          <rect width="14" height="14" fill="white" transform="translate(4.5 5)" />
                        </clipPath>
                      </defs>
                    </svg>
                    <p className="whitespace-break-spaces text-xs font-semibold text-center text-[#156634]">{locale === "en" ? "Medical\nProfessionals" : "Chuyên gia\n ưu tú"}</p>
                  </div>
                  <div className="grid place-items-center bg-[#FAFFFC] rounded-lg px-6 py-4">
                    <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="11.5" cy="12.166" r="11.5" fill="#F0FDF4" />
                      <g clip-path="url(#clip0_7608_4771)">
                        <path d="M18.5 10.25C18.4244 10.6736 18.2561 11.0752 18.0071 11.426L12.8837 18.321C12.7212 18.5309 12.5132 18.701 12.2753 18.8186C12.0374 18.9362 11.7759 18.9982 11.5105 18.9998C11.2452 19.0014 10.9829 18.9427 10.7436 18.828C10.5043 18.7134 10.2942 18.5458 10.1292 18.3379L4.98067 11.3C4.76196 10.9831 4.60757 10.6264 4.52625 10.25H8.18258L10.9552 17.4594C10.9974 17.5697 11.072 17.6646 11.1693 17.7316C11.2666 17.7985 11.3819 17.8343 11.5 17.8343C11.6181 17.8343 11.7334 17.7985 11.8307 17.7316C11.928 17.6646 12.0026 17.5697 12.0448 17.4594L14.8174 10.25H18.5ZM14.825 9.08333H18.4749C18.3862 8.68866 18.2159 8.3169 17.975 7.99192L16.4828 5.97708C16.2667 5.67507 15.9818 5.4289 15.6516 5.25898C15.3214 5.08906 14.9554 5.00028 14.5841 5H13.3007L14.825 9.08333ZM10.9797 5L9.42858 9.08333H13.5772L12.0571 5H10.9797ZM8.18083 9.08333L9.73133 5H8.37683C8.00862 4.99964 7.64558 5.08664 7.31753 5.25386C6.98948 5.42108 6.70579 5.66374 6.48975 5.96192L5.04717 7.85308C4.77114 8.21477 4.58373 8.63612 4.5 9.08333H8.18083ZM13.5673 10.25H9.43267L11.5 15.6248L13.5673 10.25Z" fill="#14A54A" />
                      </g>
                      <defs>
                        <clipPath id="clip0_7608_4771">
                          <rect width="14" height="14" fill="white" transform="translate(4.5 5)" />
                        </clipPath>
                      </defs>
                    </svg>
                    <p className="whitespace-break-spaces text-xs font-semibold text-center text-[#156634]">{locale === "en" ? "Intimate\nEnvironment" : "Môi trường\n thân thiện"}</p>
                  </div>
                  <div className="grid place-items-center bg-[#FAFFFC] rounded-lg px-6 py-4">
                    <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="11.5" cy="12.166" r="11.5" fill="#F0FDF4" />
                      <g clip-path="url(#clip0_7608_4771)">
                        <path d="M18.5 10.25C18.4244 10.6736 18.2561 11.0752 18.0071 11.426L12.8837 18.321C12.7212 18.5309 12.5132 18.701 12.2753 18.8186C12.0374 18.9362 11.7759 18.9982 11.5105 18.9998C11.2452 19.0014 10.9829 18.9427 10.7436 18.828C10.5043 18.7134 10.2942 18.5458 10.1292 18.3379L4.98067 11.3C4.76196 10.9831 4.60757 10.6264 4.52625 10.25H8.18258L10.9552 17.4594C10.9974 17.5697 11.072 17.6646 11.1693 17.7316C11.2666 17.7985 11.3819 17.8343 11.5 17.8343C11.6181 17.8343 11.7334 17.7985 11.8307 17.7316C11.928 17.6646 12.0026 17.5697 12.0448 17.4594L14.8174 10.25H18.5ZM14.825 9.08333H18.4749C18.3862 8.68866 18.2159 8.3169 17.975 7.99192L16.4828 5.97708C16.2667 5.67507 15.9818 5.4289 15.6516 5.25898C15.3214 5.08906 14.9554 5.00028 14.5841 5H13.3007L14.825 9.08333ZM10.9797 5L9.42858 9.08333H13.5772L12.0571 5H10.9797ZM8.18083 9.08333L9.73133 5H8.37683C8.00862 4.99964 7.64558 5.08664 7.31753 5.25386C6.98948 5.42108 6.70579 5.66374 6.48975 5.96192L5.04717 7.85308C4.77114 8.21477 4.58373 8.63612 4.5 9.08333H8.18083ZM13.5673 10.25H9.43267L11.5 15.6248L13.5673 10.25Z" fill="#14A54A" />
                      </g>
                      <defs>
                        <clipPath id="clip0_7608_4771">
                          <rect width="14" height="14" fill="white" transform="translate(4.5 5)" />
                        </clipPath>
                      </defs>
                    </svg>
                    <p className="whitespace-break-spaces text-xs font-semibold text-center text-[#156634]">{locale === "en" ? "Personalized\n Care" : "Chăm sóc\n riêng biệt"}</p>
                  </div>
                </div>
                <p className="py-6 text-base text-justify md:text-left">
                  {locale === "en" ? "Members gain on-demand access to a comprehensive range of concierge medical services, with a team of doctors, pharmacists, and wellness experts readily available for their convenience."
                    : "Thành viên đồng hành cùng đội ngũ bác sĩ sẽ được theo sát và thiết kế dịch vụ chăm sóc sức khoẻ tối ưu theo nhu cầu."}
                </p>
                <div className="w-full h-full flex md:justify-start justify-center mb-6">
                  <div className="py-2 px-8 text-center text-base inline text-[#166534] border border-[#14813d] hover:bg-[#14813d] rounded-full hover:text-white font-medium">
                    <LinkComponent locale={locale} skipLocaleHandling={false} href="/membership">{tranlsate("become_a_member", locale)}</LinkComponent>
                  </div>
                </div>
              </div>
            </div>
          </section> */}
          {/* <section className="md:mx-8 mx-4">
            <div className="hidden md:block">
              <div className="flex items-center justify-between flex-col md:flex-row bg-[#F4F7F5] py-12">
                <div className="md:w-1/2 md:p-8 p-4">
                  <p className="text-center md:text-left font-bold md:text-[28px] text-2xl text-[#156634] mb-6 uppercase whitespace-pre-line">
                    {tranlsate("what_we_offer", locale)}
                  </p>
                  <p className="text-[#166534] font-bold text-base text-center md:text-left mb-2 mt-4 whitespace-break-spaces sm:whitespace-normal hidden sm:block">
                    {locale === "en" ? "Medical Professionals \n- Intimate Environment \n- Personalized Care" :
                      "Chuyên gia ưu tú – Môi trường thân thiện \n– Chăm sóc riêng biệt"}</p>
                  <p className="text-[#166534] font-bold text-base text-center md:text-left mb-4 mt-4 whitespace-break-spaces sm:whitespace-normal block sm:hidden">
                    {locale === "en" ? "Medical Professionals \nIntimate Environment \nPersonalized Care" :
                      "Chuyên gia ưu tú – Môi trường thân thiện \n– Chăm sóc riêng biệt"}</p>
                  <p className="pb-4  text-base whitespace-break-spaces text-center md:text-left">
                    {locale === "en" ? "Members gain on-demand access to a comprehensive range of concierge medical services, with a team of doctors, pharmacists, and wellness experts readily available for their convenience."
                      : "Thành viên đồng hành cùng đội ngũ bác sĩ sẽ được theo sát và thiết kế dịch vụ chăm sóc sức khoẻ tối ưu theo nhu cầu."}
                  </p>
                  <div className="w-full h-full flex md:justify-start justify-center">
                    <div className="py-2 px-8 text-center text-base inline text-[#166534] border border-[#14813d] hover:bg-[#14813d] rounded-full hover:text-white font-medium">
                      <LinkComponent locale={locale} skipLocaleHandling={false} href="/membership">{tranlsate("become_a_member", locale)}</LinkComponent>
                    </div>
                  </div>
                </div>
                <div className="md:w-1/2 items-center justify-center flex mr-8">

                  <SliderMembershipMainPC />
                </div>
              </div>
            </div>
          </section> */}
        </div>
      </div>
      <section className="bg-[#FBFFFD]">
        <div className="md:px-16 px-4 py-8 max-w-screen-2xl mx-auto">
          <SliderOurPartners />
          <Partner />
        </div>
      </section>
      {/* <section className="px-4 block md:hidden">
        <BlogMainSlide />
        <BlogMediaMainSlide />
      </section> */}
      {/* <div className="mx-auto max-w-screen-2xl">
        <section className="hidden md:block md:px-16 px-4 max-w-screen-2xl">
          <BlogMainSeesion />
          <BlogTheMedia />
        </section>
        <section className="md:px-16 px-4">
          <FAQ />
        </section>
      </div> */}
      <Contact />
      <Modal
        showCloseButton
        visibleModal={showModal}
        wrapperClassName="!w-[350px]"
        contentClassName="!min-h-[0]" onClose={() => setShowModal(false)}      >
        <p className="text-2xl font-bold text-center mb-4">{locale === "en" ? "Book an Appointment" : "Đặt khám ngay"}</p>
        <p className="text-24 text-center">{locale === "en" ? "Please indicate whether you'd like to book home visit or clinic visit" : "Vui lòng lựa chọn phương thức khám tại nhà hoặc phòng khám"}</p>
        <div className="flex flex-col justify-center gap-x-40 mt-4">
          <LinkComponent href={"/booking/"} skipLocaleHandling={false} locale={locale}>
            <Button btnType="primary" onClick={() => { }} className="mb-2 m-auto" type={undefined} icon={undefined}>{locale == "en" ? "Book Clinic Visit" : "Tại phòng khám"}</Button>
          </LinkComponent>
          <p className="text-center mb-2">{locale == "en" ? "OR" : "Hoặc"}</p>
          <LinkComponent href={"/booking/"} skipLocaleHandling={false} locale={locale}>
            <Button btnType="primary" onClick={() => { }} className="mb-2 m-auto" type={undefined} icon={undefined}>{locale == "en" ? "Book Home Visit" : "Đặt lịch khám tại nhà"}</Button>
          </LinkComponent>
          <p className="text-center mb-2">{locale == "en" ? "OR" : "Hoặc"}</p>
          <p className="text-center">{locale == "en" ? "Contact" : "Liên hệ"} 1900 638 408</p>
        </div>
      </Modal>
    </>
  );
};

export default Home;
