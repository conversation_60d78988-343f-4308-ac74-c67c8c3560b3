import type { NextPage } from "next";
import Head from "next/head";
import Contact from "../../components/Contact/Contact";
import { useRouter } from 'next/router';
import { makeStaticProps } from '../../lib/getStatic';
import React from "react";
import { addToCart } from "../../lib/ui";
import Image from "next/image";
import HeaderMemBership from "../../components/Membership/HeaderMemBership";

const getStaticProps = makeStaticProps(['common', 'footer'])
const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  },
  {
    params: {
      locale: 'vi',
      slug: "test",
      label: "test2",
    }
  }],
})
export { getStaticPaths, getStaticProps }

const Home: NextPage = () => {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';


  return (
    <>
      <Head>
        <title>ECHO MEDI - Thành viên Vàng</title>
        <meta name="description" content="ECHO MEDI - Trở thành thành viên Vàng và tận hưởng những ưu đãi đặc biệt cùng dịch vụ chăm sóc sức khỏe chất lượng cao." />
        <meta name="keywords" content="ECHO MEDI, thành viên vàng, chăm sóc sức khỏe, ưu đãi, dịch vụ y tế" />
        <meta property="og:title" content="ECHO MEDI - Thành viên Vàng" />
        <meta property="og:description" content="Tận hưởng các lợi ích độc quyền và dịch vụ chất lượng hàng đầu khi trở thành thành viên Vàng tại ECHO MEDI." />
        <meta property="og:image" content="/favicon.png" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <HeaderMemBership locale={locale} />
      <div className="noselect 2xl:mx-auto lg:pb-16 lg:px-20 md:py-2 px-2 pb-9 m-auto">
        <h3 className="text-2xl md:text-3xl text-center text-[#156634] font-bold mt-4">{locale === "en" ? "GOLD MEMBERSHIP" : "GÓI THÀNH VIÊN VÀNG"}</h3>
        <div className="flex flex-col justify-between">
          <div className="w-full flex flex-row text-justify">
            <p className="font-normal leading-6 mb-3 text-base text-justify md:text-center align-middle my-2">
              {locale === "en" ? "Gold membership provides an enhanced healthcare package, including free clinic check-ups and monthly telemedicine consultations. Members also enjoy discounts on clinic services and medication purchases. Join as a Gold member for an affordable, fixed yearly price." : "Gói thành viên vàng là gói chăm sóc sức khỏe nâng cao cho thành viên với các đặc quyền như: Miễn phí khám bệnh tại phòng khám và tư vấn sức khỏe từ xa 1 lần mỗi tháng, và nhận được các ưu đãi khi sử dụng dịch vụ và mua thuốc tại phòng khám. Hãy trở thành thành viên vàng với mức chi phí tiết kiệm và cố định hằng năm."}
            </p>
          </div>
          <div className="mt-3">
            <div className="overflow-x-auto">
              <table className="table rounded-lg text-sm md:text-base">
                {/* head */}
                <thead>
                  <tr>
                    <th className="text-center align-middle w-[30%] bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634] md:text-xl text-base">{locale === "en" ? "Services" : "Dịch Vụ"}</th>
                    <th className="text-center align-middle w-[25%] bg-[#FAFAFA] h-16 border-[#E6E9F5] text-[#156634] md:text-xl text-base">{locale === "en" ? "Non-member" : "Khách Hàng \n Thông Thường"}</th>
                    <th className="text-center py-10 md:py-0 text-white align-middle w-[45%] relative bg-header-gold h-16 shadow-2xl border-none rounded-lg md:text-xl text-base">
                      {locale === "en" ? "Gold Membership" : "Thành Viên Vàng"}
                      <Image loading='lazy' width={65} height={32} className="absolute top-1 -left-1" alt="Image Icon Sale" src={locale === "en" ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_14e4417fba.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Sale_952366e44c.svg"} />
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {/* row 1 */}
                  <tr>
                    <td className="border-[#E6E9F5]">{locale === "en" ? "General health check-up package" : "Gói khám sức khỏe tổng quát"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "2.350.000 VND/package" : "2.350.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "1 visit/year" : "1 lần/năm"}
                      </section>
                    </td>
                  </tr>
                  {/* row 2 */}
                  <tr className="hover">
                    <td className="border-[#E6E9F5]">{locale === "en" ? "Psychological consultation (60 minutes)" : "Tham vấn tâm lý 60 phút"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "800.000 VND/visit" : "800.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "1 visit/year" : "1 lần/năm"}
                      </section>
                    </td>
                  </tr>
                  {/* row 3 */}
                  <tr>
                    <td className="border-[#E6E9F5]">{locale === "en" ? "Personalized Health Plan*" : "Bác sĩ thiết kế riêng “Kế Hoạch Chăm Sóc Sức Khỏe”*"}</td>
                    <td className="text-center align-middle whitespace-break-spaces border-[#E6E9F5]">{locale === "en" ? "-" : "-"}</td>
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "1 plan/year" : "1 lần/năm"}
                      </section>
                    </td>
                  </tr>
                  {/* row 4 */}
                  <tr className="hover">
                    <td className="border-[#E6E9F5]">{locale === "en" ? "In-clinic physician evaluation" : "Khám tại phòng khám"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "350.000 VND/visit" : "350.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "1 visit/month" : "Miễn phí\n1 lần/tháng"}
                      </section>
                    </td>
                  </tr>
                  {/* row 5 */}
                  <tr>
                    <td className="border-[#E6E9F5]">{locale === "en" ? "Telehealth consultation" : "Tư vấn sức khỏe từ xa"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "250.000 VND/time" : "250.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "Unlimited" : "Không giới hạn"}
                      </section>
                    </td>
                  </tr>
                  {/* row 6 */}
                  <tr className="hover">
                    <td className="align-middle border-[#E6E9F5]">{locale === "en" ? "In-home physician evaluation" : "Dịch vụ khám bệnh tại nhà"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "500.000 VND/visit" : "500.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "10% discount" : "Giảm 10%\ngiá niêm yết"}
                      </section>
                    </td>
                  </tr>
                  {/* row 7 */}
                  <tr>
                    <td className="align-middle border-[#E6E9F5]">{locale === "en" ? "Nursing care at home" : "Điều dưỡng chăm sóc tại nhà"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "250.000 VND/visit" : "250.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "10% discount" : "Giảm 10% \ngiá niêm yết"}
                      </section>
                    </td>
                  </tr>
                  {/* row 8 */}
                  <tr>
                    <td className="align-middle border-[#E6E9F5]">{locale === "en" ? "In clinic services" : "Dịch vụ xét nghiệm và gói khám thực hiện tại phòng khám"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "10% discount" : "Giảm 10% \ngiá niêm yết"}
                      </section>
                    </td>
                  </tr>
                  {/* row 9 */}
                  <tr>
                    <td className="align-middle border-[#E6E9F5]">{locale === "en" ? "Medicine and supplement" : "Thuốc và thực phẩm chức năng"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "5% discount" : "Giảm 5% \ngiá niêm yết"}
                      </section>
                    </td>
                  </tr>
                  {/* row 10 */}
                  <tr className="hover ">
                    <td className="border-[#E6E9F5]">{locale === "en" ? "Regular blood sugar check-up" : "Kiểm tra đường huyết định kỳ"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]"></td>
                    {/* {locale === "en" ? "50.000 VND/time" : "50.000 VND/lần"} */}
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "4 time/month" : "4 lần/tháng"}
                      </section>
                    </td>
                  </tr>
                  {/* row 11 */}
                  <tr>
                    <td className="align-middle border-[#E6E9F5]">{locale === "en" ? "Chronic diseases management packages" : "Các gói Quản lý bệnh mạn tính"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "20% discount" : "Giảm 20% \ngiá niêm yết"}
                      </section>
                    </td>
                  </tr>
                  <tr>
                    <td className="align-middle border-[#E6E9F5]">{locale === "en" ? "Psychological consultation packages" : "Các gói dịch vụ tham vấn tâm lý"}</td>
                    <td className="text-center align-middle border-[#E6E9F5]">{locale === "en" ? "Listed price" : "Giá niêm yết"}</td>
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1125 1.36104C10.1812 0.574966 8.8188 0.574967 7.88749 1.36104L7.03932 2.07692C6.64349 2.41102 6.15365 2.61394 5.6375 2.65759L4.53155 2.75111C3.31717 2.85381 2.35381 3.81717 2.25111 5.03155L2.15759 6.1375C2.11394 6.65365 1.91102 7.14349 1.57692 7.53934L0.861037 8.38749C0.0749661 9.3188 0.0749673 10.6812 0.861037 11.6125L1.57692 12.4607C1.91102 12.8565 2.11394 13.3464 2.15759 13.8625L2.25111 14.9685C2.35381 16.1829 3.31717 17.1462 4.53155 17.2489L5.6375 17.3424C6.15365 17.3861 6.64349 17.589 7.03934 17.9231L7.88749 18.639C8.8188 19.425 10.1812 19.425 11.1125 18.639L11.9607 17.9231C12.3565 17.589 12.8464 17.3861 13.3625 17.3424L14.4685 17.2489C15.6829 17.1462 16.6462 16.1829 16.7489 14.9685L16.8424 13.8625C16.8861 13.3464 17.089 12.8565 17.4231 12.4607L18.139 11.6125C18.925 10.6812 18.925 9.3188 18.139 8.38749L17.4231 7.53932C17.089 7.14349 16.8861 6.65365 16.8424 6.1375L16.7489 5.03155C16.6462 3.81717 15.6829 2.85381 14.4685 2.75111L13.3625 2.65759C12.8464 2.61394 12.3565 2.41102 11.9607 2.07692L11.1125 1.36104ZM14.046 8.29555C14.4854 7.85621 14.4854 7.1439 14.046 6.70456C13.6067 6.26521 12.8944 6.26521 12.455 6.70456L8.25054 10.9091L6.54604 9.20456C6.1067 8.76521 5.39439 8.76521 4.95505 9.20456C4.5157 9.6439 4.5157 10.3562 4.95505 10.7956L7.45505 13.2955C7.89439 13.7349 8.6067 13.7349 9.04604 13.2955L14.046 8.29555Z" fill="#252430" />
                        </svg>
                        {locale === "en" ? "20% discount" : "Giảm 20% \ngiá niêm yết"}
                      </section>
                    </td>
                  </tr>
                  {/* <tr>
                    <td colSpan={2} className="align-middle bg-[#FAFAFA] h-16 border-[#E6E9F5]">{locale === "en" ? "Price membership" : "Giá gói Thành Viên"}</td>
                    <td className="text-center align-middle bg-body-gold border-[#E6E9F5]">
                      <section className="flex items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <span className="line-through">{locale === "en" ? "8.000.000 VND/year" : "8.000.000 VND/năm"}</span>
                      </section>
                    </td>
                  </tr> */}
                  {/* <tr>
                    <td colSpan={2} className="font-bold align-middle border-[#E6E9F5] bg-white text-[#156634]">{locale === "en" ? "Special offer" : "Giá ưu đãi"}</td>
                    <td className="text-center align-middle border-[#E6E9F5] relative bg-header-gold md:h-16">
                      <section className="flex h-16 items-center justify-start gap-1 md:ml-[40%] ml-[10%] md:text-lg whitespace-break-spaces md:whitespace-normal text-start">
                        <span className="font-bold pt-4">{locale === "en" ? "4.000.000 VND/year" : "4.000.000 VND/năm"}</span>
                        <Image loading='lazy' width={65} height={32} className="absolute top-1 -left-1" alt="Image Icon Sale" src={locale === "en" ? "https://d3e4m6b6rxmux9.cloudfront.net/Sale_14e4417fba.png" : "https://d3e4m6b6rxmux9.cloudfront.net/Sale_952366e44c.svg"} />
                      </section>
                    </td>
                  </tr> */}
                  <tr>
                    <td colSpan={2} className="border-none">
                    </td>
                    <td className="border-none">
                      <section className="hidden md:block">
                        <div className='mt-4 flex items-center justify-around'>
                          <a href="tel:1900638408" className="border border-[#156634] rounded-3xl py-1.5 md:px-12 text-[#156634] font-medium" data-original-title="Liên hệ với chúng tôi">
                            {locale === "en" ? "Contact" : "Tư vấn gói"}
                          </a>
                          {/* <button onClick={() => { addToCart(740, locale) }} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 md:px-12 text-sm font-medium text-white">
                            {locale === "en" ? "Add To Cart" : "Mua ngay"}
                          </button> */}
                        </div>
                      </section>
                    </td>
                  </tr>
                </tbody>
              </table>
              <section className="block md:hidden">
                <div className="flex items-center justify-between">
                  <div>
                    <a href="tel:1900638408" className="border border-[#156634] rounded-3xl py-1 px-12 text-[#156634] font-medium" data-original-title="Liên hệ với chúng tôi">
                      {locale === "en" ? "Contact" : "Tư vấn gói"}
                    </a>
                  </div>
                  <div>
                    <button onClick={() => { addToCart(740, locale) }} className="bg-[#156634] hover:bg-[#14813d] rounded-3xl py-2 px-12 text-sm font-medium text-white">
                      {locale === "en" ? "Add To Cart" : "Mua ngay"}
                    </button>
                  </div>
                </div>
              </section>
              {locale == "vi" ?
                <div className="text-sm">
                  <p className="font-bold text-left my-2">* Dịch vụ này chỉ áp dụng cho gói thành viên, bao gồm các thông tin được cá nhân hóa: </p>
                  <li className="my-1"> Sức khỏe tổng quát, cách theo dõi</li>
                  <li className="my-1"> Chăm sóc phòng ngừa</li>
                  <li> Tư vấn tiêm ngừa</li>
                  <li> Hướng dẫn dinh dưỡng, vận động, chăm sóc giấc ngủ</li>
                  <li> 	Tham vấn tâm lý</li>
                </div> :
                <div className="text-sm">
                  <p className="font-bold text-left my-2">* This service only applies to membership packages, including the following information: </p>
                  <li className="my-1"> General health, monitoring method</li>
                  <li className="my-1"> Preventive care</li>
                  <li> Vaccination consultation</li>
                  <li> Instructions on nutrition, exercise, and sleep management</li>
                  <li> Psychological consultation</li>
                </div>}

            </div>

          </div>
        </div>
      </div>
      <Contact />
    </>
  );
};

export default Home;
