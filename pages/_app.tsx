import "../styles/globals.css";
import "../styles/minimal.css";
import type { AppProps } from "next/app";
import NavBar from "../components/NavigationBar/NavBar";
import Router from "next/router";
import toast, { Toaster } from 'react-hot-toast';
import React, { useEffect, useState } from "react";
import { useRouter } from 'next/router'
import Script from 'next/script';
import '../lib/datePicker/index.css'
import SocialButtons from "../components/Chat/SocialButtons";
import ScrollToTop from "../components/ScrollToTop";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import Head from "next/head";

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter()
  const locale = router.query.locale as string || 'vi';
  const [showChild, setShowChild] = useState(false)

  useEffect(() => {

    // let toastId: string;
    // const start = () => {
    //   console.log('locale', locale)
    //   toast.loading(locale == "vi" ? "Đang tải..." : 'Loading...');
    // };
    // const end = () => {
    //   toast.dismiss(toastId);
    // };

    // Router.events.on("routeChangeStart", start);
    // Router.events.on("routeChangeComplete", end);
    setShowChild(true);
    // return () => {
    //   Router.events.off("routeChangeStart", start);
    //   Router.events.off("routeChangeComplete", end);
    // };
  }, []);

  if (!showChild) {
    return null
  }

  return (
    <>
      <Head>
        <title>ECHO MEDI - Hệ thống y tế toàn diện cho bạn và gia đình</title>
        <meta name="description" content="ECHO MEDI là hệ thống y tế toàn diện cung cấp các dịch vụ chăm sóc sức khỏe cho bạn và gia đình." />
        <meta name="keywords" content="bác sĩ gia đình, y tế, sức khỏe, chăm sóc sức khỏe, khám bệnh, bệnh viện, Echo Medi" />
        <meta property="og:title" content="Hệ thống y tế toàn diện cho bạn và gia đình" />
        <meta property="og:description" content="ECHO MEDI là hệ thống y tế toàn diện cung cấp các dịch vụ chăm sóc sức khỏe cho bạn và gia đình." />
        <meta property="og:image" content="https://d3e4m6b6rxmux9.cloudfront.net/Old_Mobile_VIE_d75fc17c01.webp" />
      </Head>
      <NavBar />
      <Component {...pageProps} />
      <Script
        src="https://www.googletagmanager.com/gtag/js?id=G-7JR7J71T35"
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){window.dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-7JR7J71T35');

          function copyToClipboard() {
            // Create a "hidden" input
            var aux = document.createElement("input");
            // Assign it the value of the specified element
            aux.setAttribute("value", "Você não pode mais dar printscreen. Isto faz parte da nova medida de segurança do sistema.");
            // Append it to the body
            document.body.appendChild(aux);
            // Highlight its content
            aux.select();
            // Copy the highlighted text
            document.execCommand("copy");
            // Remove it from the body
            document.body.removeChild(aux);
            alert("Không được phép screenshot");
          }
          
          $(window).keyup(function(e){
            if(e.keyCode == 44){
              copyToClipboard();
            }
          }); 
          
          // $(window).focus(function() {
          //   $("body").show();
          // }).blur(function() {
          //   $("body").hide();
          // });
        
          $(window).bind('contextmenu', false);
        
        `}
      </Script>
      <div className="coccoc-alo-phone coccoc-alo-green coccoc-alo-show block sm:hidden" id="coccoc-alo-phoneIcon">
        <a href="tel:1900638408" data-original-title="Liên hệ với chúng tôi">
          <div className="coccoc-alo-ph-circle"></div>
          <div className="coccoc-alo-ph-circle-fill"></div>
          <div className="coccoc-alo-ph-img-circle"></div>
        </a>
      </div>
      <ScrollToTop />
      <SocialButtons />
      <Toaster
        position="bottom-center"
      />
    </>
  );
}

export default MyApp;