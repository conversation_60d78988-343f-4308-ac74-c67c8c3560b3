import React from "react";
import axios from "axios";


//pages/sitemap.xml.js
const EXTERNAL_DATA_URL = 'https://api.echomedi.com/api/services?pagination[page]=1&pagination[pageSize]=10000';
const EXTERNAL_DATA_PACKAGES_URL = 'https://api.echomedi.com/api/packages?pagination[page]=1&pagination[pageSize]=10000';
const EXTERNAL_DATA_ARTICLES_URL = 'https://api.echomedi.com/api/articles?pagination[page]=1&pagination[pageSize]=10000';

function generateSiteMap(posts, packages, articles) {
  return `<?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
     <!--We manually set the two URLs we know already-->
     <url>
       <loc>https://echomedi.com</loc>
     </url>
     <url>
       <loc>https://echomedi.com/vi</loc>
     </url>
      <url>
       <loc>https://echomedi.com/en</loc>
     </url>
     ${Array.isArray(posts?.data) && posts?.data?.map(({ attributes: { slug } }) => {
    return `
       <url>
           <loc>https://echomedi.com/vi/services/${slug}</loc>
       </url>
       <url>
           <loc>https://echomedi.com/en/services/${slug}</loc>
       </url>
     `;
  })
      .join('')}
       ${Array.isArray(packages?.data) && packages?.data?.map(({ attributes: { slug } }) => {
        return `
      <url>
          <loc>https://echomedi.com/vi/packages/${slug}</loc>
      </url>
      <url>
          <loc>https://echomedi.com/en/packages/${slug}</loc>
      </url>
    `;
      })
      .join('')}
      ${Array.isArray(articles?.data) && articles?.data?.map(({ attributes: { slug } }) => {
        return `
      <url>
          <loc>https://echomedi.com/vi/hoat_dong/${slug}</loc>
      </url>
      <url>
          <loc>https://echomedi.com/en/hoat_dong/${slug}</loc>
      </url>
    `;
      })
      .join('')}
   </urlset>
 `;
}

class Sitemap extends React.Component {
  static async getInitialProps({ res }) {
    // const data = await axios
    //   .get("https://domain.ltd/wp-json/wp/v2/works?filter=[orderby]=date")
    //   .then(response => response.data);

    // res.setHeader("Content-Type", "text/xml");
    // res.write(sitemapXml(data));
    // res.end();
    const request = await fetch(EXTERNAL_DATA_URL);
    const requestPackage = await fetch(EXTERNAL_DATA_PACKAGES_URL);
    const requestArticles = await fetch(EXTERNAL_DATA_ARTICLES_URL);
    const posts = await request.json();
    const packages = await requestPackage.json();
    const articles = await requestArticles.json();
    const sitemap = generateSiteMap(posts, packages, articles);
    res.setHeader("Content-Type", "text/xml");
    res.write(sitemap);
    res.end();
  }
}

// export async function getServerSideProps({ res }) {
//   // We make an API call to gather the URLs for our site
//   const request = await fetch(EXTERNAL_DATA_URL);
//   const requestPackage = await fetch(EXTERNAL_DATA_PACKAGES_URL);
//   const posts = await request.json();
//   const packages = await requestPackage.json();

//   // We generate the XML sitemap with the posts data
//   const sitemap = generateSiteMap(posts, packages);

//   res.setHeader('Content-Type', 'text/xml');
//   // we send the XML to the browser
//   res.write(sitemap);
//   res.end();

//   return {
//     props: {},
//   };
// }

export default Sitemap;