import axios from 'axios';
import toast from 'react-hot-toast';

export const shimmer = (w: number, h: number) => `
<svg width="${w}" height="${h}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <linearGradient id="g">
      <stop stop-color="#d3efc7" offset="20%" />
      <stop stop-color="white" offset="50%" />
      <stop stop-color="#d3efc7" offset="70%" />
    </linearGradient>
  </defs>
  <rect width="${w}" height="${h}" fill="#d3efc7" />
  <rect id="r" width="${w}" height="${h}" fill="url(#g)" />
  <animate xlink:href="#r" attributeName="x" from="-${w}" to="${w}" dur="1s" repeatCount="indefinite"  />
</svg>`;

export const toBase64 = (str: string) =>
  typeof window === "undefined"
    ? Buffer.from(str).toString("base64")
    : window.btoa(str);

export const addToCart = (id: number, locale: string) => {

  if (localStorage.getItem('token')) {
    const token = localStorage.getItem('token');
    axios.post('https://api.echomedi.com/api/product/addServiceToCart', {
      "service_id": id,
      "quantity": 1,
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
      .then(function (response) {
        toast.success(locale === "en" ? "Added to  cart" : "Thêm vào giỏ hàng thành công!");
        let els = document.getElementsByClassName('num-of-item');
        for (let i = 0; i < els.length; ++i) {
          let el = els[i] as HTMLElement;
          let parentNode = el.parentElement as HTMLElement;
          if (el) {
            el.innerText = (isNaN(parseInt(el.innerText)) ? 1 : parseInt(el.innerText) + 1).toString();
            parentNode.classList.remove('invisible');
          }
        }
      })
      .catch(function (error) {
        toast.error("Thêm vào giỏ hàng thất bại")
        if (error.response.status == 401) {
          toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
          localStorage.removeItem("token");
          window.location.href = '/login';
        }
      });
  } else {
    toast.success('Vui lòng đăng nhập.');
    window.location.href = "/" + locale + "/login", "/" + locale + "/login";
  }
}

export const addProductToCart = (id: number, quantity: number, locale: string) => {
  if (localStorage.getItem("token")) {
    const token = localStorage.getItem("token");
    axios
      .post(
        "https://api.echomedi.com/api/product/addProductToCart",
        {
          product_id: id,
          quantity: quantity,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then(function (response) {
        toast.success(locale === "en" ? "Added to  cart" : "Thêm vào giỏ hàng thành công!");

        let els = document.getElementsByClassName("num-of-item");
        for (let i = 0; i < els.length; ++i) {
          let el = els[i] as HTMLElement;
          let parentNode = el.parentElement as HTMLElement;
          if (el) {
            el.innerText = (isNaN(parseInt(el.innerText)) ? 1 : parseInt(el.innerText) + 1).toString();
            parentNode.classList.remove("invisible");
          }
        }
      })
      .catch(function (error) {
        toast.error("Thêm vào giỏ hàng thất bại");
        if (error.response.status == 401) {
          toast.error("Phiên làm việc đã hết giờ vui lòng đăng nhập lại");
          localStorage.removeItem("token");
          window.location.href = "/login";
        }
      });
  } else {
    toast.success("Vui lòng đăng nhập.");
    window.location.href = "/" + locale + "/login", "/" + locale + "/login";
  }
};