import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import i18nextConfig from '../next-i18next.config'
import { PackagesApi } from '../models/package';
import { ProductApi } from '../models/product';
import { ServiceApi } from '../models/service';
import { BlogApi } from '../models/blog';
import { ActivityApi } from '../models/activity';

const fetchPackages = async () => {
  return new PackagesApi().getAll();
};

const createI18nPaths = (json) => {
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));

  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));

  return en.concat(vi);
};

export const getI18nPaths = async () => {
  const json = await fetchPackages();
  return createI18nPaths(json);
};

export const getI18nPathsPackage = async () => {
  const json = await fetchPackages();
  const allowedSlugs = [
    "goi-tam-ly-nguoi-lon",
    "tam-ly-tre-em"
  ];
  const filteredJson = json.filter((p) => allowedSlugs.includes(p.slug));
  return createI18nPaths(filteredJson);
};



export const getI18nPathsPackagePediatricHealthcare = async () => {
  const json = await fetchPackages();
  const allowedSlugs = ["goi-cham-soc-suc-khoe-nhi"];

  const filteredJson = json.filter((p) => allowedSlugs.includes(p.slug));
  return createI18nPaths(filteredJson);
};

export const getI18nPathsPackagePreventiveCare = async () => {
  const json = await fetchPackages();
  const allowedSlugs = ["goi-cham-soc-phong-ngua"];

  const filteredJson = json.filter((p) => allowedSlugs.includes(p.slug));
  return createI18nPaths(filteredJson);
};

export const getI18nPathsPackagePrimaryCare = async () => {
  const json = await fetchPackages();
  const allowedSlugs = ["goi-dieu-tri-ban-dau"];

  const filteredJson = json.filter((p) => allowedSlugs.includes(p.slug));
  return createI18nPaths(filteredJson);
};

export const getI18nPathsPackageChronicDiseases = async () => {
  const json = await fetchPackages();
  const allowedSlugs = ["goi-quan-ly-benh-man-tinh"];

  const filteredJson = json.filter((p) => allowedSlugs.includes(p.slug));
  return createI18nPaths(filteredJson);
};

export const getI18nPathsPackageWellness = async () => {
  const json = await fetchPackages();
  const allowedSlugs = ["goi-suc-khoe-toan-dien"];

  const filteredJson = json.filter((p) => allowedSlugs.includes(p.slug));
  return createI18nPaths(filteredJson);
};


export const getI18nPathsPackageEchoMediGen = async () => {
  const json = await new PackagesApi().find("echomedi_gen");
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsPackageGen = async () => {
  const json = await new PackagesApi().find("gen");
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsService = async () => {
  const json = await new ServiceApi().getAll();
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsActivity = async () => {
  const json = await new ActivityApi().getAll();
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsServiceGenDetail = async () => {
  const json = await new ServiceApi().find("gen_detail");
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsServicesMembership = async () => {
  const json = await new ServiceApi().find("membership");
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsServicesPolicy = async () => {
  const json = await new ServiceApi().find("policy");
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsBlog = async () => {
  const json = await new BlogApi().getAll();
  const en = json?.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json?.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}

export const getI18nPathsProduct = async () => {
  const json = await new ProductApi().getAll();
  const en = json.map((p) => ({
    params: {
      locale: "en",
      slug: p.slug,
    }
  }));
  const vi = json.map((p) => ({
    params: {
      locale: "vi",
      slug: p.slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}
export const getI18nPathsPartnership = async () => {
  const slugs = ["van-anh", "hua-gia-tu", "nguyen-ngoc-bao-tran"];
  const en = slugs.map((slug) => ({
    params: {
      locale: "en",
      slug,
    }
  }));

  const vi = slugs.map((slug) => ({
    params: {
      locale: "vi",
      slug,
    }
  }));
  const sum = en.concat(vi);
  return sum;
}
export const getStaticPathsPackages = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackage()
  }
}


export const getStaticPathsPackagesPreventiveCare = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackagePreventiveCare()
  }
}

export const getStaticPathsPackagesPrimaryCare = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackagePrimaryCare()
  }
}

export const getStaticPathsPackagesChronicDiseases = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackageChronicDiseases()
  }
}

export const getStaticPathsPackagesWellness = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackageWellness()
  }
}

export const getStaticPathsPackagesPediatricHealthcare = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackagePediatricHealthcare()
  }
}

export const getStaticPathsPackagesEchomediGen = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackageEchoMediGen()
  }
}

export const getStaticPathsPackagesGen = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPackageGen()
  }
}

export const getStaticPathsServices = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsService()
  }
}

export const getStaticPathsActivities = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsActivity()
  }
}


export const getStaticPathsServicesGenDetail = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsServiceGenDetail()
  }
}

export const getStaticPathsServicesMembership = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsServicesMembership()
  }
}

export const getStaticPathsServicesPolicy = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsServicesPolicy()
  }
}

export const getStaticPathsBlogs = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsBlog()
  }
}

export const getStaticPathsProducts = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsProduct()
  }
}
export const getStaticPathsPartnership = async () => {
  return {
    fallback: false,
    paths: await getI18nPathsPartnership()
  }
}
export const getStaticPaths = () => ({
  fallback: false,
  paths: [{
    params: {
      locale: 'en',
      slug: "test",
      label: "test2",
    }
  }],
})

export async function getI18nProps(ctx, ns = ['common']) {
  const locale = ctx?.params?.locale
  let props = {
    ...(await serverSideTranslations(locale, ns)),
    label: "",
    desc: "",
    image_url: "",
    sub_packages: [],
  }
  return props
}

export function makeStaticProps(ns = {}) {
  return async function getStaticProps(ctx) {
    return {
      props: await getI18nProps(ctx, ns),
    }
  }
}

export async function getStaticPropsProduct(ctx) {
  const params = ctx?.params;
  const { locale, slug } = params;
  const product = await new ProductApi().findOne(slug);
  return {
    props: {
      id: product.id,
      label: product.label,
      en_label: product.en_label,
      desc: product.desc,
      en_desc: product.en_desc,
      medicines: product.medicines,
      image_url: product.image_url,
      image_placeholder_url: product.image_placeholder_url,
      price: product.price,
    }
  }
}

export async function getStaticPropsService(ctx) {
  const params = ctx?.params ?? {};
  const { locale, slug } = params;
  const service = await new ServiceApi().findOne(slug);
  return {
    props: {
      id: service.id,
      label: service.label,
      label_web: service?.label_web ?? "",
      label_web_en: service?.label_web_en ?? "",
      en_label: service.en_label,
      slug: service.slug,
      desc: service.desc,
      en_desc: service.en_desc,
      detail: service.detail,
      en_detail: service.en_detail,
      image_url: service.image ? service.image.url : '',
      placeholder_image_url: service.image ? service.image.formats.thumbnail.url : '',
      price: service.price ?? 0,
      original_price: service.original_price ?? 0,
      show_buy_btn: service.show_buy_btn ?? false,
      show_inquiry_form: service.show_inquiry_form ?? false,
      show_booking_btn: service.show_booking_btn ?? false,
      benefit: service.benefit,
      en_benefit: service.en_benefit,
      genetica_image_url: service.genetica_image_url ?? '',
      properties: service.properties ?? [],
      en_properties: service.en_properties ?? [],
      genetica_pdf: service.genetica_pdf,
      specification: service.specification ?? [],
      en_specification: service.en_specification ?? [],
      sub_package: service.sub_package,
      package: service.package,
      other_services: service.other_services ?? [],
    }
  }
}

export async function getStaticPropsActivity(ctx) {
  const params = ctx?.params ?? {};
  const { locale, slug } = params;
  const service = await new ActivityApi().findOne(slug);
  return {
    props: {
      id: service.id,
      title: service.title ?? '',
      slug: service.slug ?? "",
      description: service.description ?? '',
      content: service.content ?? '',
      cover: service.cover.url ?? '',
    }
  }
}

export async function getStaticPropsBlog(ctx) {
  const params = ctx?.params;
  const { locale, slug } = params;
  const service = await new BlogApi().findOne(slug);
  return {
    props: {
      // id: service.id,
      label: service.label,
      // en_label: service.en_label,
      slug: service.slug,
      // desc: service.desc,
      // en_desc: service.en_desc,
      article: service.article,
      // en_detail: service.en_detail,
      image_url: service.image_url,

      // placeholder_image_url: service.image ? service.image.formats.thumbnail.url : '',
      // price: service.price ?? 0,
      // show_buy_btn: service.show_buy_btn ?? false,
      // show_inquiry_form: service.show_inquiry_form ?? false,
      // show_booking_btn: service.show_booking_btn ?? false,
    }
  }
}

export async function getStaticPropsPackage(ctx) {
  const params = ctx?.params;
  const { locale, slug } = params;
  const pkg = await new PackagesApi().findOne(slug);
  const subPackages = pkg.sub_packages?.map(({ label, en_label, services }) => ({
    label: label ?? "",
    en_label: en_label ?? "",
    services: services?.map(({ desc, en_desc, label, en_label, slug, genetica_image }) => ({
      desc: desc ?? "",
      en_desc: en_desc ?? "",
      label: label ?? "",
      en_label: en_label ?? "",
      slug: slug || "",
      genetica_image: genetica_image ? {
        id: genetica_image.id ?? null,
        url: genetica_image.url ?? "",
      } : {}
    })) || []
  })) || [];
  return {
    props: {
      label: pkg.label ?? "",
      en_label: pkg.en_label ?? "",
      desc: pkg.desc ?? "",
      en_desc: pkg.en_desc ?? "",
      sub_packages: subPackages,
      image_url: pkg.image_url ?? "",
    }
  }
}


export async function getStaticPropsGenDetail(ctx) {
  const params = ctx?.params;
  const { locale, slug } = params;
  const pkg = await new PackagesApi().findOne(slug);
  return {
    props: {
      label: pkg.label ?? "",
      label_web: pkg.label_web ?? "",
      desc: pkg.desc ?? "",
      en_desc: pkg.en_desc ?? "",
      image_url: pkg.image_url ?? "",
      detail: pkg.detail ?? "",
      sub_packages: pkg.sub_packages ?? [],
      label_web_en: pkg.label_web_en ?? "",
      en_label: pkg.en_label ?? "",
      show_additional_fee: pkg.show_additional_fee ?? "",
      show_inquiry_form: pkg.show_inquiry_form ?? "",
    }
  }
}

export async function getStaticPropsPartnership(ctx) {
  const params = ctx?.params;
  const { locale, slug } = params;
  const dataURL = [
    {
      id: 1,
      url: "van-anh",
      name: "Anh V. Tran",
      imgURL_VI: "https://d3e4m6b6rxmux9.cloudfront.net/Name_Card_thang_9_Export_ENG_Anh_V_Tran_4f4ee31b8e.png",
      imgURL_EN: "https://d3e4m6b6rxmux9.cloudfront.net/Name_Card_thang_9_Export_ENG_Anh_V_Tran_4f4ee31b8e.png",
      contact: "+84 779 952 130",
      gmail: "<EMAIL>"
    },
    {
      id: 2,
      url: "hua-gia-tu",
      name: "Hứa Gia Tú",
      imgURL_VI: "https://d3e4m6b6rxmux9.cloudfront.net/NC_Export_VIE_TU_HUA_3fe957760b.png",
      imgURL_EN: "https://d3e4m6b6rxmux9.cloudfront.net/Name_Card_thang_9_Export_ENG_TU_HUA_97536e1b85.png",
      contact: "+84 837 548 397",
      gmail: "<EMAIL>"
    },
    {
      id: 3,
      url: "nguyen-ngoc-bao-tran",
      name: "NGUYỄN NGỌC BẢO TRÂN",
      imgURL_VI: "https://d3e4m6b6rxmux9.cloudfront.net/NC_Export_VIE_BS_TRAN_8578b5ed36.png",
      imgURL_EN: "https://d3e4m6b6rxmux9.cloudfront.net/Name_Card_thang_9_Export_ENG_BS_TRAN_c3ab5e5c67.png",
      contact: "+84 703 056 656",
      gmail: "<EMAIL>"
    }
  ];

  const data = dataURL.find((item) => item.url === slug) || null;

  return {
    props: {
      data
    }
  };
}