name: CI/CD

on:
  # Trigger the workflow on push or pull request,
  # but only for the master branch
  push:
    branches:
      - main
  repository_dispatch:
    schedule:
      - cron: '5 12 * * 0'

jobs:
  primary:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: 18.x
      - name: install dependencies
        run: yarn --frozen-lockfile
      - name: build
        run: yarn run build:production
        env:
          CI: false
      - name: Deploy to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl public-read --delete
        env:
          AWS_S3_BUCKET: ${{ secrets.AWS_BUCKET_PROD }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SOURCE_DIR: "out"
      - name: Cloudfront Invalidation
        uses: chetan/invalidate-cloudfront-action@master
        env:
          DISTRIBUTION: ${{ secrets.AWS_DISTRIBUTION_PRODUCTION }}
          PATHS: '/*'
          AWS_REGION: 'ap-southeast-1'
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}