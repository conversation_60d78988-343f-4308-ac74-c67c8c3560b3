

export const dataCompanyIcon = (locale : string) => [
    {
        id: 1,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/company_02_8bdcea435d.png",
        title: locale === "en" ? "Enhancing business value" : "Nâng cao giá trị của doanh nghiệp",
        desc: locale === "en" ? "Prioritizing employee health while fostering a sustainable work environment." : "Thể hiện sự quan tâm đến sức sức khỏe người lao động, tạo môi trường làm việc bền vững.",
    },
    {
        id: 2,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/company_06_faa4d5db8b.png",
        title: locale === "en" ? "Attracting highly skilled workforce" : "Thu hút nguồn nhân lực có chuyên môn cao",
        desc: locale === "en" ? "Enhance employee satisfaction and engagement to attract and retain top talent." : "<PERSON>ia tăng sự hài lòng và gắn bó của nhân viên, thu hút và giữ chân các ứng viên chất lượng.",
    },
    {
        id: 3,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/company_07_63f7c2fa0c.png",
        title: locale === "en" ? "Improving corporate culture" : "Cải thiện văn hóa doanh nghiệp",
        desc: locale === "en" ? "Cultivate employee engagement and mutual support to build a collaborative and positive workplace culture." : "Thúc đẩy sự gắn kết và hỗ trợ lẫn nhau giữa các nhân viên, tạo ra môi trường làm việc hợp tác và tích cực.",
    },
    {
        id: 4,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/company_04_ee14cf7bce.png",
        title: locale === "en" ? "Proficient in system operations" : "Chuyên nghiệp trong vận hành hệ thống",
        desc: locale === "en" ? "Promoting and maintaining employee health helps reduce sick days and increase workplace engagement." : "Cải thiện, duy trì trạng thái khỏe mạnh của nhân viên sẽ giúp giảm số ngày nghỉ ốm và tăng cường sự gắn bó với công việc.",
    },
    {
        id: 5,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/company_03_56fc01ab11.png",
        title: locale === "en" ? "Improving work performance" : "Cải thiện hiệu suất công việc",
        desc: locale === "en" ? "Enhancing focus and effective workload management boosts overall work efficiency." : "Nâng cao khả năng tậo trung và quản lý công việc dẫn đến hiệu quả làm việc cao hơn.",
    },
    {
        id: 6,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/company_05_5a49bfb1d9.png",
        title: locale === "en" ? "Boosting morale" : "Nâng cao tinh thần làm việc",
        desc: locale === "en" ? "Fostering a supportive and caring work environment where employees feel valued and motivated to give their best." : "Tạo ra môi trường làm việc hỗ trợ và chăm sóc, giúp nhân viên cảm thấy được trân trọng và có động lực cống hiến nhiều hơn.",
    },

]
export const dataStaffIcon = (locale : string) => [
    {
        id: 1,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/staff_03_982aa571c5.png",
        title: locale === "en" ? "Comprehensively assessing health" : "Đánh giá sức khỏe toàn diện",
        desc: locale === "en" ? "Employees receive comprehensive examinations, consultations, and support from medical professionals to effectively improve health and prevent diseases." : "Nhân viên sẽ được thăm khám, tư vấn và hỗ trợ bởi các chuyên gia y tế được đào tạo trong và ngoài nước, nhằm nâng cao sức khỏe và phòng ngừa bệnh tật một cách hiệu quả.",
    },
    {
        id: 2,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/staff_05_9dedeb7419.png",
        title: locale === "en" ? "Providing physical and mental health solutions" : "Giải pháp sức khỏe thể chất và tinh thần",
        desc: locale === "en" ? "Multi-channel healthcare services, including in-clinic care, at-home services, telemedicine, and the ECHO MEDI app." : "Dịch vụ chăm sóc y tế đa kênh: Trực tiếp, trực tuyến, qua các thiết bị điện tử. Các hình thức dịch vụ cụ thể: Phòng khám hoặc khám bệnh tại nhà, tư vấn y tế từ xa, App ECHO MEDI.",
    },
    {
        id: 3,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/staff_04_163e319203.png",
        title: locale === "en" ? "Personalizing health care" : "Chăm sóc sức khỏe cá nhân hóa",
        desc: locale === "en" ? "Employees will receive health examinations, assessments, and personalized healthcare plans tailored to their individual needs." : "Nhân viên sẽ được thăm khám, kiểm tra sức khỏe và xây dựng kế hoạch chăm sóc sức khỏe phù hợp với từng cá nhân.",
    },
    {
        id: 4,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/staff_02_93ae96099e.png",
        title: locale === "en" ? "Proactively preventing and detecting early" : "Chủ động phòng ngừa và phát hiện sớm",
        desc: locale === "en" ? "Early detection of dangerous or chronic disease risks enables more effective prevention and treatment." : "Phát hiện sớm nguy cơ mắc các bệnh lý nguy hiểm/ mạn tính giúp phòng ngừa hoặc điều trị hiệu quả hơn.",
    },
    {
        id: 5,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/staff_01_8cf4a27e20.png",
        title: locale === "en" ? "Saving on medical expenses" : "Giảm chi phí điều trị bệnh",
        desc: locale === "en" ? "By enrolling in comprehensive healthcare coverage, you ensure complete protection and access to essential services." : "Khi được chăm sóc sức khỏe toàn diện.",
    },
    {
        id: 6,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/staff_06_f906724ca0.png",
        title: locale === "en" ? "Improving quality of life" : "Nâng cao chất lượng cuộc sống",
        desc: locale === "en" ? "Promoting and maintaining the physical and mental health of every employee." : "Duy trì trạng thái khỏe mạnh cả về thể chất và tinh thần mỗi nhân viên.",
    },
]
export const dataEchoMediCard = (locale : string) => [
    {
        id: 1,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/echomedi_01_c884285eb3.png",
        title: locale === "en" ? "Healthcare planning" : "Lên kế hoạch chăm sóc sức khỏe",
        desc: locale === "en" ? "Provides detailed information and connects to supportive health services for holistic care." : "Cung cấp thông tin chi tiết và đầy đủ, đồng thời kết nối các dịch vụ y tế hỗ trợ để đảm bảo sự chăm sóc toàn diện.",
    },
    {
        id: 2,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/echomedi_02_5fb9ed2887.png",
        title: locale === "en" ? "Managing chronic diseases" : "Quản lý bệnh mạn tính",
        desc: locale === "en" ? "Includes medical treatment, regular health monitoring, nutritional counseling, and ongoing mental support for effective treatment and enhanced quality of life." : "Bao gồm điều trị bệnh lý, lên kế hoạch theo dõi sức khỏe định kỳ, tư vấn dinh dưỡng và hỗ trợ...",
    },
    {
        id: 3,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/echomedi_03_7df02e7991.png",
        title: locale === "en" ? "Organizing seminars and workshops" : "Tổ chức các buổi tọa đàm, workshop",
        desc: locale === "en" ? "Promote health awareness and foster a positive working environment." : "Tăng cường nhận thức về sức khỏe, góp phần tạo ra một môi trường làm việc tích cực.",
    },
    {
        id: 4,
        image: "https://d3e4m6b6rxmux9.cloudfront.net/echomedi_04_59377f8fac.png",
        title: locale === "en" ? "Improving mental health" : "Nâng cao sức khỏe tinh thần",
        desc: locale === "en" ? "Enhancing and sustaining positive mental and emotional well-being for employees in the workplace." : "Cải thiện và duy trì trạng thái tinh thần và cảm xúc tích cực của nhân viên tại môi trường làm việc.",
    },
]
export const dataImage = (locale : string) => [
    {
        id: 1,
        img: locale === "vi" ? "https://api.echomedi.com/uploads/web_app_01_ea074d29d2.png" : "https://api.echomedi.com/uploads/web_app_02_502fc314a3.png",
    },
    {
        id: 2,
        img: "https://api.echomedi.com/uploads/app_01_d05e8b203f.png"
    },
    {
        id: 3,
        img: "https://api.echomedi.com/uploads/app_02_6ae81d6c1e.png"
    },
];
export const dataImageCompany = [
    {
        id: 1,
        img: "https://api.echomedi.com/uploads/member_company_c80dad555d.png"
    },
    {
        id: 2,
        img: "https://api.echomedi.com/uploads/member_company_01_d4569f1fff.png"
    },
    {
        id: 3,
        img: "https://api.echomedi.com/uploads/member_company_02_95924f49c8.png"
    },
    {
        id: 3,
        img: "https://api.echomedi.com/uploads/member_company_03_01c75958e1.png"
    },
];