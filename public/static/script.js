// function copyToClipboard() {
//     // Create a "hidden" input
//     var aux = document.createElement("input");
//     // Assign it the value of the specified element
//     aux.setAttribute("value", "Você não pode mais dar printscreen. Isto faz parte da nova medida de segurança do sistema.");
//     // Append it to the body
//     document.body.appendChild(aux);
//     // Highlight its content
//     aux.select();
//     // Copy the highlighted text
//     document.execCommand("copy");
//     // Remove it from the body
//     document.body.removeChild(aux);
//     alert("Không được phép screenshot");
//   }
  
//   $(window).keyup(function(e){
//     if(e.keyCode == 44){
//       copyToClipboard();
//     }
//   }); 
  
//   $(window).focus(function() {
//     $("body").show();
//   }).blur(function() {
//     $("body").hide();
//   });

//   $(window).bind('contextmenu', false);
