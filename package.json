{"name": "nextjs-tailwind-landing-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:production": "REACT_APP_ENV=production npm run build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@dotlottie/player-component": "^2.7.5", "@dotlottie/react-player": "^1.6.12", "@hookform/resolvers": "^2.9.11", "@paypal/paypal-js": "^8.0.0", "@types/react-google-recaptcha": "^2.1.5", "axios": "^1.2.1", "classnames": "^2.3.2", "dayjs": "^1.11.7", "html-react-parser": "^3.0.7", "i18next": "^22.4.6", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "moment": "^2.29.4", "next": "^14.0.3", "next-i18next": "^13.0.2", "next-language-detector": "^1.0.2", "nextjs-google-analytics": "^2.3.3", "pure-react-carousel": "^1.30.1", "qs": "^6.11.2", "react": "^18.2.0", "react-datepicker": "^4.10.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^2.1.0", "react-hook-form": "^7.43.3", "react-hot-toast": "^2.4.0", "react-i18next": "^12.1.1", "react-inlinesvg": "^3.0.2", "react-lazy-load-image-component": "^1.5.6", "react-markdown": "^8.0.4", "react-otp-input": "^3.1.1", "react-router-dom": "^6.8.2", "react-select": "^5.7.0", "react-share": "^5.1.0", "react-tabs": "^6.0.1", "react-toastify": "^9.1.1", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "swiper": "^8.4.5", "yup": "^1.0.2"}, "devDependencies": {"@types/node": "18.6.3", "@types/qs": "^6.9.11", "@types/react": "18.0.15", "@types/react-dom": "18.0.6", "@types/react-lazy-load-image-component": "^1.5.2", "autoprefixer": "^10.4.14", "eslint": "^8.21.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.21", "prettier": "^2.7.1", "react-icons": "^4.4.0", "tailwindcss": "^3.3.1", "typescript": "4.7.4"}}